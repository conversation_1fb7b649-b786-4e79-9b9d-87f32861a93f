# docker-compose.yml - OptiKarburant Development Environment

version: '3.8'

services:
  # PostgreSQL with PostGIS
  db:
    image: postgis/postgis:15-3.3
    container_name: optikarburant_db
    environment:
      POSTGRES_DB: optikarburant
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - optikarburant_network

  # Redis for Celery message broker
  redis:
    image: redis:7-alpine
    container_name: optikarburant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - optikarburant_network

  # OSRM routing service
  osrm:
    image: osrm/osrm-backend:latest
    container_name: optikarburant_osrm
    command: osrm-routed --algorithm mld /data/albania-latest.osrm
    ports:
      - "5000:5000"
    volumes:
      - osrm_data:/data
    depends_on:
      - osrm-init
    networks:
      - optikarburant_network

  # OSRM data preparation (runs once)
  osrm-init:
    image: osrm/osrm-backend:latest
    container_name: optikarburant_osrm_init
    command: >
      bash -c "
        if [ ! -f /data/albania-latest.osrm ]; then
          echo 'Downloading Albania OSM data...'
          wget -O /data/albania-latest.osm.pbf http://download.geofabrik.de/europe/albania-latest.osm.pbf
          echo 'Extracting routing data...'
          osrm-extract -p /opt/car.lua /data/albania-latest.osm.pbf
          echo 'Partitioning...'
          osrm-partition /data/albania-latest.osrm
          echo 'Customizing...'
          osrm-customize /data/albania-latest.osrm
          echo 'OSRM data preparation complete!'
        else
          echo 'OSRM data already exists, skipping preparation.'
        fi
      "
    volumes:
      - osrm_data:/data
    networks:
      - optikarburant_network

  # Nominatim geocoding service
  nominatim:
    image: mediagis/nominatim:4.2
    container_name: optikarburant_nominatim
    environment:
      PBF_URL: http://download.geofabrik.de/europe/albania-latest.osm.pbf
      REPLICATION_URL: http://download.geofabrik.de/europe/albania-updates/
      NOMINATIM_PASSWORD: very_secure_password
    ports:
      - "8080:8080"
    volumes:
      - nominatim_data:/var/lib/postgresql/12/main
    shm_size: 1g
    networks:
      - optikarburant_network

  # Django web application
  web:
    build: .
    container_name: optikarburant_web
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/code
      - media_files:/code/media
      - static_files:/code/staticfiles
    ports:
      - "8000:8000"
    environment:
      - DEBUG=1
      - DB_NAME=optikarburant
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - OSRM_URL=http://osrm:5000
      - NOMINATIM_URL=http://nominatim:8080
    depends_on:
      - db
      - redis
      - osrm
    networks:
      - optikarburant_network

  # Celery worker for background tasks
  worker:
    build: .
    container_name: optikarburant_worker
    command: celery -A optikarburant worker --loglevel=info --concurrency=2
    volumes:
      - .:/code
    environment:
      - DEBUG=1
      - DB_NAME=optikarburant
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
      - OSRM_URL=http://osrm:5000
      - NOMINATIM_URL=http://nominatim:8080
    depends_on:
      - db
      - redis
      - web
    networks:
      - optikarburant_network

  # Celery beat scheduler
  beat:
    build: .
    container_name: optikarburant_beat
    command: celery -A optikarburant beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    volumes:
      - .:/code
    environment:
      - DEBUG=1
      - DB_NAME=optikarburant
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_HOST=db
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
      - web
    networks:
      - optikarburant_network

  # Flower for Celery monitoring
  flower:
    build: .
    container_name: optikarburant_flower
    command: celery -A optikarburant flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
      - worker
    networks:
      - optikarburant_network

volumes:
  postgres_data:
  redis_data:
  osrm_data:
  nominatim_data:
  media_files:
  static_files:

networks:
  optikarburant_network:
    driver: bridge
