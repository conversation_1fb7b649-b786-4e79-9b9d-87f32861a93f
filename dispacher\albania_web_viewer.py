#!/usr/bin/env python
"""
Simple web interface to view Albanian OptiKarburant sample data
This provides a web-based dashboard without Django spatial dependencies
"""

from flask import Flask, render_template_string, jsonify
import sqlite3
import os
import json

app = Flask(__name__)

def get_db_connection():
    """Get database connection"""
    db_path = 'albania_sample_data.db'
    if not os.path.exists(db_path):
        return None
    return sqlite3.connect(db_path)

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template_string(DASHBOARD_HTML)

@app.route('/api/summary')
def api_summary():
    """API endpoint for summary statistics"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database not found'})
    
    cursor = conn.cursor()
    
    # Get summary statistics
    cursor.execute('SELECT COUNT(*) FROM products')
    products_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM stations')
    stations_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM drivers')
    drivers_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM trucks')
    trucks_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM tanks')
    tanks_count = cursor.fetchone()[0]
    
    cursor.execute('SELECT SUM(kapaciteti_total), SUM(sasia_aktuale) FROM tanks')
    capacity_data = cursor.fetchone()
    total_capacity = capacity_data[0] or 0
    total_stock = capacity_data[1] or 0
    fill_rate = (total_stock / total_capacity * 100) if total_capacity > 0 else 0
    
    conn.close()
    
    return jsonify({
        'products': products_count,
        'stations': stations_count,
        'drivers': drivers_count,
        'trucks': trucks_count,
        'tanks': tanks_count,
        'total_capacity': total_capacity,
        'total_stock': total_stock,
        'fill_rate': round(fill_rate, 1)
    })

@app.route('/api/stations')
def api_stations():
    """API endpoint for stations data"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database not found'})
    
    cursor = conn.cursor()
    cursor.execute('''
        SELECT emri, kodi, city, station_type, latitude, longitude,
               orar_pranimi_nga, orar_pranimi_deri, kerkon_pompe, kerkon_kontaliter,
               max_kamione_njekohesisht, menaxher_emri, telefoni
        FROM stations
        ORDER BY city, emri
    ''')
    
    stations = []
    for row in cursor.fetchall():
        stations.append({
            'emri': row[0],
            'kodi': row[1],
            'city': row[2],
            'station_type': row[3],
            'latitude': row[4],
            'longitude': row[5],
            'orar_nga': row[6],
            'orar_deri': row[7],
            'kerkon_pompe': bool(row[8]),
            'kerkon_kontaliter': bool(row[9]),
            'max_kamione': row[10],
            'menaxher': row[11],
            'telefoni': row[12]
        })
    
    conn.close()
    return jsonify(stations)

@app.route('/api/trucks')
def api_trucks():
    """API endpoint for trucks data"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database not found'})
    
    cursor = conn.cursor()
    cursor.execute('''
        SELECT t.targa, t.modeli, t.viti_prodhimit, t.truck_type, t.ka_pompe, t.ka_kontaliter,
               t.pesha_maksimale_bruto_ton, t.gjatesia_totale_m, t.odometri_aktual_km,
               d.emri, d.mbiemri
        FROM trucks t
        LEFT JOIN drivers d ON t.shofer_id = d.id
        ORDER BY t.truck_type, t.targa
    ''')
    
    trucks = []
    for row in cursor.fetchall():
        trucks.append({
            'targa': row[0],
            'modeli': row[1],
            'year': row[2],
            'truck_type': row[3],
            'ka_pompe': bool(row[4]),
            'ka_kontaliter': bool(row[5]),
            'weight': row[6],
            'length': row[7],
            'odometer': row[8],
            'driver_name': f"{row[9]} {row[10]}" if row[9] else "No driver assigned"
        })
    
    conn.close()
    return jsonify(trucks)

@app.route('/api/tanks')
def api_tanks():
    """API endpoint for tank inventory"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database not found'})
    
    cursor = conn.cursor()
    cursor.execute('''
        SELECT s.emri, s.city, p.emri as product, t.numri_tankut,
               t.kapaciteti_total, t.sasia_aktuale, t.niveli_minimal_sigurise,
               ROUND(t.sasia_aktuale * 100.0 / t.kapaciteti_total, 1) as fill_percent
        FROM tanks t
        JOIN stations s ON t.station_id = s.id
        JOIN products p ON t.product_id = p.id
        ORDER BY s.city, s.emri, t.numri_tankut
    ''')
    
    tanks = []
    for row in cursor.fetchall():
        tanks.append({
            'station': row[0],
            'city': row[1],
            'product': row[2],
            'tank_number': row[3],
            'capacity': row[4],
            'stock': row[5],
            'safety_level': row[6],
            'fill_percent': row[7],
            'status': 'critical' if row[5] <= row[6] else 'low' if row[7] < 30 else 'normal'
        })
    
    conn.close()
    return jsonify(tanks)

# HTML template for the dashboard
DASHBOARD_HTML = '''
<!DOCTYPE html>
<html lang="sq">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇦🇱 OptiKarburant Albania - Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 1rem; text-align: center; }
        .container { max-width: 1200px; margin: 0 auto; padding: 2rem; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #3498db; }
        .stat-label { color: #666; margin-top: 0.5rem; }
        .tabs { display: flex; background: white; border-radius: 8px; overflow: hidden; margin-bottom: 1rem; }
        .tab { flex: 1; padding: 1rem; background: #ecf0f1; cursor: pointer; text-align: center; border: none; }
        .tab.active { background: #3498db; color: white; }
        .content { background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #ddd; }
        .table th { background: #f8f9fa; font-weight: 600; }
        .badge { padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.8rem; }
        .badge-success { background: #d4edda; color: #155724; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-danger { background: #f8d7da; color: #721c24; }
        .loading { text-align: center; padding: 2rem; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🇦🇱 OptiKarburant Albania</h1>
        <p>Fuel Distribution Network Dashboard</p>
    </div>
    
    <div class="container">
        <div class="stats-grid" id="stats">
            <div class="loading">Loading statistics...</div>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('stations')">⛽ Stations</button>
            <button class="tab" onclick="showTab('trucks')">🚚 Trucks</button>
            <button class="tab" onclick="showTab('tanks')">🛢️ Tanks</button>
        </div>
        
        <div class="content">
            <div id="stations" class="tab-content">
                <div class="loading">Loading stations...</div>
            </div>
            <div id="trucks" class="tab-content" style="display: none;">
                <div class="loading">Loading trucks...</div>
            </div>
            <div id="tanks" class="tab-content" style="display: none;">
                <div class="loading">Loading tanks...</div>
            </div>
        </div>
    </div>

    <script>
        let currentTab = 'stations';
        
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // Update content
            document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
            document.getElementById(tabName).style.display = 'block';
            
            currentTab = tabName;
            loadTabData(tabName);
        }
        
        function loadTabData(tabName) {
            const content = document.getElementById(tabName);
            
            if (content.dataset.loaded) return;
            
            fetch(`/api/${tabName}`)
                .then(response => response.json())
                .then(data => {
                    if (tabName === 'stations') {
                        content.innerHTML = renderStations(data);
                    } else if (tabName === 'trucks') {
                        content.innerHTML = renderTrucks(data);
                    } else if (tabName === 'tanks') {
                        content.innerHTML = renderTanks(data);
                    }
                    content.dataset.loaded = 'true';
                })
                .catch(error => {
                    content.innerHTML = `<div class="loading">Error loading ${tabName}: ${error}</div>`;
                });
        }
        
        function renderStations(stations) {
            let html = '<table class="table"><thead><tr><th>Station</th><th>City</th><th>Type</th><th>Hours</th><th>Equipment</th><th>Contact</th></tr></thead><tbody>';
            
            stations.forEach(station => {
                const equipment = [];
                if (station.kerkon_pompe) equipment.push('🔧 Pump');
                if (station.kerkon_kontaliter) equipment.push('📊 Meter');
                
                html += `<tr>
                    <td><strong>${station.emri}</strong><br><small>${station.kodi}</small></td>
                    <td>${station.city}</td>
                    <td><span class="badge badge-success">${station.station_type}</span></td>
                    <td>${station.orar_nga} - ${station.orar_deri}</td>
                    <td>${equipment.join(' ')}</td>
                    <td>${station.menaxher}<br><small>${station.telefoni}</small></td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        function renderTrucks(trucks) {
            let html = '<table class="table"><thead><tr><th>Truck</th><th>Driver</th><th>Specifications</th><th>Equipment</th><th>Mileage</th></tr></thead><tbody>';
            
            trucks.forEach(truck => {
                const equipment = [];
                if (truck.ka_pompe) equipment.push('🔧');
                if (truck.ka_kontaliter) equipment.push('📊');
                
                html += `<tr>
                    <td><strong>${truck.targa}</strong><br>${truck.modeli} (${truck.year})</td>
                    <td>${truck.driver_name}</td>
                    <td>${truck.weight}t, ${truck.length}m<br><span class="badge badge-success">${truck.truck_type}</span></td>
                    <td>${equipment.join(' ')}</td>
                    <td>${truck.odometer.toLocaleString()} km</td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        function renderTanks(tanks) {
            let html = '<table class="table"><thead><tr><th>Station</th><th>Product</th><th>Tank</th><th>Capacity</th><th>Stock</th><th>Status</th></tr></thead><tbody>';
            
            tanks.forEach(tank => {
                let statusBadge = 'badge-success';
                let statusText = 'Normal';
                
                if (tank.status === 'critical') {
                    statusBadge = 'badge-danger';
                    statusText = '🚨 Critical';
                } else if (tank.status === 'low') {
                    statusBadge = 'badge-warning';
                    statusText = '⚠️ Low';
                }
                
                html += `<tr>
                    <td><strong>${tank.station}</strong><br><small>${tank.city}</small></td>
                    <td>${tank.product}</td>
                    <td>${tank.tank_number}</td>
                    <td>${tank.capacity.toLocaleString()} L</td>
                    <td>${tank.stock.toLocaleString()} L<br><small>${tank.fill_percent}% full</small></td>
                    <td><span class="badge ${statusBadge}">${statusText}</span></td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            return html;
        }
        
        // Load initial data
        fetch('/api/summary')
            .then(response => response.json())
            .then(data => {
                document.getElementById('stats').innerHTML = `
                    <div class="stat-card">
                        <div class="stat-number">${data.stations}</div>
                        <div class="stat-label">⛽ Stations</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.trucks}</div>
                        <div class="stat-label">🚚 Trucks</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.tanks}</div>
                        <div class="stat-label">🛢️ Tanks</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">${data.fill_rate}%</div>
                        <div class="stat-label">📊 Fill Rate</div>
                    </div>
                `;
                
                // Load initial tab
                loadTabData('stations');
            });
    </script>
</body>
</html>
'''

if __name__ == '__main__':
    print("🇦🇱 Starting OptiKarburant Albania Web Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("🔧 Using Albanian sample data from: albania_sample_data.db")
    app.run(debug=True, host='0.0.0.0', port=5000)
