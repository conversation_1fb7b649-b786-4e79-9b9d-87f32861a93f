# logistics/admin.py - Django admin configuration for OptiKarburant

from django.contrib import admin
from django.contrib.gis.admin import OSMGeoAdmin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Sum, Count

from .models import (
    Produkt, DepoQendrore, Stacion, De<PERSON>te, <PERSON>er, Kamion, 
    Particion, Porosi, PlanRruge, Ndalese, NgarkeseShkarkese
)


@admin.register(Produkt)
class ProduktAdmin(admin.ModelAdmin):
    list_display = ['emri', 'densiteti', 'ngjyra_kodi', 'eshte_aktiv', 'data_krijimit']
    list_filter = ['eshte_aktiv', 'data_krijimit']
    search_fields = ['emri']
    filter_horizontal = ['produkte_kompatible']
    readonly_fields = ['data_krijimit']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('emri', 'densiteti', 'ngjyra_kodi', 'eshte_aktiv')
        }),
        (_('Compatibility'), {
            'fields': ('produkte_kompatible',),
            'description': _('Select products that can share the same compartment')
        }),
        (_('Metadata'), {
            'fields': ('data_krijimit',),
            'classes': ('collapse',)
        }),
    )


@admin.register(DepoQendrore)
class DepoQendroreAdmin(OSMGeoAdmin):
    list_display = ['emri', 'kapaciteti_ngarkimi', 'orar_punes_nga', 'orar_punes_deri', 'eshte_aktiv']
    list_filter = ['eshte_aktiv']
    search_fields = ['emri', 'adresa']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('emri', 'adresa', 'vendndodhja')
        }),
        (_('Operational Settings'), {
            'fields': ('kapaciteti_ngarkimi', 'koha_mesatare_ngarkimi', 
                      'orar_punes_nga', 'orar_punes_deri', 'eshte_aktiv')
        }),
    )


@admin.register(Stacion)
class StacionAdmin(OSMGeoAdmin):
    list_display = ['emri', 'kodi', 'orar_pranimi_nga', 'orar_pranimi_deri', 
                   'kerkon_pompe', 'kerkon_kontaliter', 'eshte_aktiv']
    list_filter = ['eshte_aktiv', 'kerkon_pompe', 'kerkon_kontaliter']
    search_fields = ['emri', 'kodi', 'adresa', 'menaxher_emri']
    readonly_fields = ['data_krijimit', 'data_perditesimit']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('emri', 'kodi', 'adresa', 'vendndodhja')
        }),
        (_('Operating Hours'), {
            'fields': ('orar_pranimi_nga', 'orar_pranimi_deri')
        }),
        (_('Equipment Requirements'), {
            'fields': ('kerkon_pompe', 'kerkon_kontaliter')
        }),
        (_('Physical Constraints'), {
            'fields': ('max_kamione_njekohesisht', 'koha_mesatare_shkarkimi',
                      'max_pesha_kamioni_ton', 'max_gjatesia_kamioni_m')
        }),
        (_('Contact Information'), {
            'fields': ('menaxher_emri', 'telefoni', 'email')
        }),
        (_('Status'), {
            'fields': ('eshte_aktiv',)
        }),
        (_('Metadata'), {
            'fields': ('data_krijimit', 'data_perditesimit'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related()


class DepoziteInline(admin.TabularInline):
    model = Depozite
    extra = 1
    fields = ['produkt', 'numri_tankut', 'kapaciteti_total', 'sasia_aktuale', 
             'niveli_i_porosise', 'niveli_minimal_sigurise']


@admin.register(Depozite)
class DepoziteAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'kapaciteti_total', 'sasia_aktuale', 'perqindja_mbushjes_display',
                   'nevojitet_rifornizim', 'eshte_kritik', 'data_perditesimit']
    list_filter = ['produkt', 'stacion', 'data_perditesimit']
    search_fields = ['stacion__emri', 'produkt__emri', 'numri_tankut']
    readonly_fields = ['perqindja_mbushjes', 'kapaciteti_i_disponueshem', 
                      'dite_deri_zbrazje', 'data_perditesimit']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('stacion', 'produkt', 'numri_tankut')
        }),
        (_('Capacity and Levels'), {
            'fields': ('kapaciteti_total', 'sasia_aktuale', 'perqindja_mbushjes',
                      'niveli_i_porosise', 'niveli_minimal_sigurise')
        }),
        (_('Forecasting'), {
            'fields': ('konsumi_mesatar_ditor', 'dite_deri_zbrazje')
        }),
        (_('Delivery Constraints'), {
            'fields': ('sasia_minimale_dorezimi', 'perqindja_maksimale_mbushjes',
                      'kapaciteti_i_disponueshem')
        }),
        (_('Metadata'), {
            'fields': ('data_dorezimit_fundit', 'data_perditesimit'),
            'classes': ('collapse',)
        }),
    )
    
    def perqindja_mbushjes_display(self, obj):
        percentage = obj.perqindja_mbushjes
        if percentage < 25:
            color = 'red'
        elif percentage < 50:
            color = 'orange'
        else:
            color = 'green'
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, percentage
        )
    perqindja_mbushjes_display.short_description = _('Fill %')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('stacion', 'produkt')


@admin.register(Shofer)
class ShoferAdmin(admin.ModelAdmin):
    list_display = ['emri_i_plote', 'leje_drejtimi_numri', 'leje_drejtimi_skadon', 
                   'leje_adr', 'eshte_aktiv']
    list_filter = ['eshte_aktiv', 'leje_adr', 'leje_drejtimi_skadon']
    search_fields = ['emri', 'mbiemri', 'leje_drejtimi_numri', 'telefoni']
    readonly_fields = ['emri_i_plote']
    
    fieldsets = (
        (_('Personal Information'), {
            'fields': ('emri', 'mbiemri', 'telefoni', 'email')
        }),
        (_('License Information'), {
            'fields': ('leje_drejtimi_numri', 'leje_drejtimi_skadon', 'leje_adr')
        }),
        (_('Working Constraints'), {
            'fields': ('ore_punes_maksimale_ditor', 'ore_drejtimi_maksimale_ditor')
        }),
        (_('Employment'), {
            'fields': ('eshte_aktiv', 'data_punesimit')
        }),
    )


class ParticionInline(admin.TabularInline):
    model = Particion
    extra = 1
    fields = ['numri_i_dhomes', 'kapaciteti', 'produkt_i_dedikuar', 
             'sasia_aktuale', 'eshte_i_pastruar']


@admin.register(Kamion)
class KamionAdmin(admin.ModelAdmin):
    list_display = ['targa', 'shofer_aktual', 'statusi', 'ka_pompe', 'ka_kontaliter', 
                   'kapaciteti_total_display', 'nevojitet_mirembajtje', 'eshte_aktiv']
    list_filter = ['statusi', 'eshte_aktiv', 'ka_pompe', 'ka_kontaliter', 'eshte_trailer']
    search_fields = ['targa', 'modeli', 'shofer_aktual__emri', 'shofer_aktual__mbiemri']
    inlines = [ParticionInline]
    readonly_fields = ['kapaciteti_total_litra', 'kapaciteti_i_disponueshem_litra', 
                      'nevojitet_mirembajtje', 'data_krijimit']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('targa', 'modeli', 'viti_prodhimit', 'shofer_aktual')
        }),
        (_('Physical Specifications'), {
            'fields': ('pesha_maksimale_bruto_ton', 'gjatesia_totale_m', 'eshte_trailer')
        }),
        (_('Equipment'), {
            'fields': ('ka_pompe', 'ka_kontaliter', 'ka_gps')
        }),
        (_('Capacity'), {
            'fields': ('kapaciteti_total_litra', 'kapaciteti_i_disponueshem_litra'),
            'classes': ('collapse',)
        }),
        (_('Status and Location'), {
            'fields': ('statusi', 'vendndodhja_aktuale', 'data_perditesimit_gps')
        }),
        (_('Maintenance'), {
            'fields': ('data_mirembajtjes_fundit', 'km_mirembajtjes_rradheses', 
                      'odometri_aktual_km', 'nevojitet_mirembajtje')
        }),
        (_('Performance'), {
            'fields': ('konsumi_mesatar_l_100km',)
        }),
        (_('Status'), {
            'fields': ('eshte_aktiv',)
        }),
        (_('Metadata'), {
            'fields': ('data_krijimit',),
            'classes': ('collapse',)
        }),
    )
    
    def kapaciteti_total_display(self, obj):
        return f"{obj.kapaciteti_total_litra:,.0f} L"
    kapaciteti_total_display.short_description = _('Total Capacity')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('shofer_aktual')# logistics/admin.py continued - Order and Route admin

@admin.register(Particion)
class ParticionAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'kapaciteti', 'produkt_i_dedikuar', 'produkti_aktual', 
                   'sasia_aktuale', 'eshte_i_pastruar', 'kapaciteti_i_disponueshem']
    list_filter = ['kamion', 'produkt_i_dedikuar', 'eshte_i_pastruar', 'kerkon_pastrimin']
    search_fields = ['kamion__targa', 'produkt_i_dedikuar__emri']
    readonly_fields = ['kapaciteti_i_disponueshem', 'eshte_bosh']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('kamion', 'numri_i_dhomes', 'kapaciteti')
        }),
        (_('Product Assignment'), {
            'fields': ('produkt_i_dedikuar', 'produkti_aktual', 'produkti_i_fundit')
        }),
        (_('Current Load'), {
            'fields': ('sasia_aktuale', 'sasia_heel_litra', 'kapaciteti_i_disponueshem', 'eshte_bosh')
        }),
        (_('Cleaning Status'), {
            'fields': ('eshte_i_pastruar', 'data_pastrimit', 'kerkon_pastrimin')
        }),
    )


@admin.register(Porosi)
class PorosiAdmin(admin.ModelAdmin):
    list_display = ['numri_porosise', 'stacion', 'produkt', 'sasia_e_kerkuar', 
                   'prioriteti', 'statusi', 'eshte_urgjente', 'data_afati', 'data_krijimit']
    list_filter = ['statusi', 'prioriteti', 'eshte_automatike', 'eshte_emergjente', 
                  'produkt', 'data_krijimit']
    search_fields = ['numri_porosise', 'stacion__emri', 'produkt__emri']
    readonly_fields = ['numri_porosise', 'eshte_urgjente', 'data_krijimit', 'data_perditesimit']
    autocomplete_fields = ['stacion', 'depozite', 'produkt']
    
    fieldsets = (
        (_('Order Information'), {
            'fields': ('numri_porosise', 'stacion', 'depozite', 'produkt')
        }),
        (_('Quantities'), {
            'fields': ('sasia_e_kerkuar', 'sasia_e_miratuar', 'sasia_e_dorezuar')
        }),
        (_('Scheduling'), {
            'fields': ('prioriteti', 'data_afati', 'koha_preferuar_fillimi', 
                      'koha_preferuar_mbarimi', 'eshte_urgjente')
        }),
        (_('Status'), {
            'fields': ('statusi', 'eshte_automatike', 'eshte_emergjente')
        }),
        (_('Notes'), {
            'fields': ('shenimet',)
        }),
        (_('Metadata'), {
            'fields': ('krijuar_nga', 'data_krijimit', 'data_perditesimit'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'stacion', 'depozite', 'produkt', 'krijuar_nga'
        )


class NdaleseInline(admin.TabularInline):
    model = Ndalese
    extra = 1
    fields = ['sekuenca', 'stacion', 'depo_qendrore', 'koha_parashikuar_mberritjes', 
             'koha_parashikuar_nisjes', 'eshte_perfunduar']
    readonly_fields = ['sasia_totale_dorezuar']


@admin.register(PlanRruge)
class PlanRrugeAdmin(admin.ModelAdmin):
    list_display = ['numri_rrugese', 'data_planifikimit', 'kamion', 'shofer', 
                   'statusi', 'distanca_e_planifikuar_km', 'pikuesi_efikasitetit_display']
    list_filter = ['statusi', 'data_planifikimit', 'kamion']
    search_fields = ['numri_rrugese', 'kamion__targa', 'shofer__emri']
    inlines = [NdaleseInline]
    readonly_fields = ['numri_rrugese', 'pikuesi_efikasitetit', 'sasia_totale_dorezuar',
                      'data_krijimit', 'data_perditesimit']
    
    fieldsets = (
        (_('Route Information'), {
            'fields': ('numri_rrugese', 'data_planifikimit', 'kamion', 'shofer')
        }),
        (_('Planning Metrics'), {
            'fields': ('koha_nisjes_nga_depo', 'distanca_e_planifikuar_km', 
                      'kohezgjatja_e_planifikuar_ore', 'kostoja_e_parashikuar')
        }),
        (_('Actual Performance'), {
            'fields': ('distanca_aktuale_km', 'kohezgjatja_aktuale_ore', 'kostoja_aktuale',
                      'koha_aktuale_nisjes', 'koha_aktuale_mbarimit')
        }),
        (_('Performance Metrics'), {
            'fields': ('pikuesi_efikasitetit', 'sasia_totale_dorezuar')
        }),
        (_('Status and Optimization'), {
            'fields': ('statusi', 'pikuesi_optimizimit')
        }),
        (_('Notes'), {
            'fields': ('shenimet',)
        }),
        (_('Metadata'), {
            'fields': ('krijuar_nga', 'data_krijimit', 'data_perditesimit'),
            'classes': ('collapse',)
        }),
    )
    
    def pikuesi_efikasitetit_display(self, obj):
        score = obj.pikuesi_efikasitetit
        if score is None:
            return '-'
        
        if score >= 90:
            color = 'green'
        elif score >= 70:
            color = 'orange'
        else:
            color = 'red'
        
        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, score
        )
    pikuesi_efikasitetit_display.short_description = _('Efficiency')
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('kamion', 'shofer', 'krijuar_nga')


class NgarkeseShkarkeseInline(admin.TabularInline):
    model = NgarkeseShkarkese
    extra = 1
    fields = ['porosi', 'particion', 'produkt', 'sasia', 'sasia_e_dorezuar', 
             'eshte_perfunduar', 'ka_variacion']


@admin.register(Ndalese)
class NdaleseAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'plan_rruge', 'sekuenca', 'koha_parashikuar_mberritjes', 
                   'eshte_perfunduar', 'vonesimi_display', 'sasia_totale_dorezuar']
    list_filter = ['eshte_perfunduar', 'plan_rruge__data_planifikimit']
    search_fields = ['plan_rruge__numri_rrugese', 'stacion__emri']
    inlines = [NgarkeseShkarkeseInline]
    readonly_fields = ['vonesimi_minuta', 'sasia_totale_dorezuar']
    
    fieldsets = (
        (_('Stop Information'), {
            'fields': ('plan_rruge', 'stacion', 'depo_qendrore', 'sekuenca')
        }),
        (_('Planned Timing'), {
            'fields': ('koha_parashikuar_mberritjes', 'koha_e_parashikuar_sherbimit_min',
                      'koha_parashikuar_nisjes')
        }),
        (_('Actual Timing'), {
            'fields': ('koha_aktuale_mberritjes', 'koha_aktuale_nisjes', 'vonesimi_minuta')
        }),
        (_('Delivery Status'), {
            'fields': ('eshte_perfunduar', 'sasia_totale_dorezuar', 'problemet_dorezimit',
                      'nenshkrimi_marresi')
        }),
        (_('GPS Verification'), {
            'fields': ('vendndodhja_gps',)
        }),
    )
    
    def vonesimi_display(self, obj):
        delay = obj.vonesimi_minuta
        if delay is None:
            return '-'
        
        if delay <= 0:
            return format_html('<span style="color: green;">On time</span>')
        elif delay <= 15:
            return format_html('<span style="color: orange;">+{} min</span>', delay)
        else:
            return format_html('<span style="color: red;">+{} min</span>', delay)
    vonesimi_display.short_description = _('Delay')


@admin.register(NgarkeseShkarkese)
class NgarkeseShkarkeseAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'ndalese', 'porosi', 'particion', 'sasia', 
                   'sasia_e_dorezuar', 'variacion_display', 'eshte_perfunduar']
    list_filter = ['eshte_perfunduar', 'ka_variacion', 'produkt']
    search_fields = ['ndalese__plan_rruge__numri_rrugese', 'porosi__numri_porosise', 
                    'produkt__emri']
    readonly_fields = ['variacion_sasia', 'variacion_perqindja']
    
    fieldsets = (
        (_('Operation Information'), {
            'fields': ('ndalese', 'porosi', 'particion', 'produkt', 'depozite')
        }),
        (_('Quantities'), {
            'fields': ('sasia', 'sasia_e_dorezuar', 'variacion_sasia', 'variacion_perqindja')
        }),
        (_('Quality Control'), {
            'fields': ('temperatura_celsius', 'densiteti_i_matur')
        }),
        (_('Delivery Details'), {
            'fields': ('leximi_fillimi_kontaliter', 'leximi_mbarimi_kontaliter',
                      'koha_fillimi_dorezimit', 'koha_mbarimi_dorezimit')
        }),
        (_('Status'), {
            'fields': ('eshte_perfunduar', 'ka_variacion', 'arsyeja_variacionit')
        }),
    )
    
    def variacion_display(self, obj):
        variance = obj.variacion_perqindja
        if abs(variance) < 1:
            return format_html('<span style="color: green;">{:.1f}%</span>', variance)
        elif abs(variance) < 5:
            return format_html('<span style="color: orange;">{:.1f}%</span>', variance)
        else:
            return format_html('<span style="color: red;">{:.1f}%</span>', variance)
    variacion_display.short_description = _('Variance %')


# Admin site customization
admin.site.site_header = _("OptiKarburant Administration")
admin.site.site_title = _("OptiKarburant Admin")
admin.site.index_title = _("Welcome to OptiKarburant Administration")

# Custom admin actions
@admin.action(description=_('Mark selected orders as delivered'))
def mark_orders_delivered(modeladmin, request, queryset):
    queryset.update(statusi='e_dorezuar')

@admin.action(description=_('Generate automatic orders for selected tanks'))
def generate_automatic_orders(modeladmin, request, queryset):
    from .tasks import create_automatic_order_for_tank
    
    count = 0
    for tank in queryset:
        if tank.nevojitet_rifornizim:
            create_automatic_order_for_tank.delay(tank.id)
            count += 1
    
    modeladmin.message_user(
        request, 
        _('Generated {} automatic orders').format(count)
    )

# Add actions to admin classes
PorosiAdmin.actions = [mark_orders_delivered]
DepoziteAdmin.actions = [generate_automatic_orders]