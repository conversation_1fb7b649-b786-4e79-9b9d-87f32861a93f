<!-- templates/dashboard/analytics.html - Analytics Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .metric-card {
        transition: transform 0.2s;
    }
    .metric-card:hover {
        transform: translateY(-2px);
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .performance-indicator {
        font-size: 2rem;
        font-weight: bold;
    }
    .trend-up {
        color: #28a745;
    }
    .trend-down {
        color: #dc3545;
    }
    .trend-neutral {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-chart-line text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex gap-2">
                <input type="date" name="start_date" class="form-control" 
                       value="{{ start_date|date:'Y-m-d' }}" style="max-width: 150px;">
                <input type="date" name="end_date" class="form-control" 
                       value="{{ end_date|date:'Y-m-d' }}" style="max-width: 150px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Filter
                </button>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="setDateRange(7)">7 Days</button>
                <button type="button" class="btn btn-outline-primary" onclick="setDateRange(30)">30 Days</button>
                <button type="button" class="btn btn-outline-primary" onclick="setDateRange(90)">90 Days</button>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card metric-card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="performance-indicator">{{ delivery_stats.total_deliveries }}</div>
                            <p class="mb-0">Total Deliveries</p>
                            <small class="trend-up">
                                <i class="fas fa-arrow-up"></i> {{ delivery_stats.delivery_growth|floatformat:1 }}%
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card metric-card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="performance-indicator">{{ delivery_stats.on_time_percentage|floatformat:1 }}%</div>
                            <p class="mb-0">On-Time Delivery</p>
                            <small class="trend-up">
                                <i class="fas fa-arrow-up"></i> {{ delivery_stats.on_time_improvement|floatformat:1 }}%
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card metric-card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="performance-indicator">{{ fuel_stats.total_liters|floatformat:0 }}</div>
                            <p class="mb-0">Liters Delivered</p>
                            <small class="trend-up">
                                <i class="fas fa-arrow-up"></i> {{ fuel_stats.volume_growth|floatformat:1 }}%
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-gas-pump fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card metric-card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <div class="performance-indicator">{{ route_stats.avg_efficiency|floatformat:1 }}%</div>
                            <p class="mb-0">Route Efficiency</p>
                            <small class="trend-up">
                                <i class="fas fa-arrow-up"></i> {{ route_stats.efficiency_improvement|floatformat:1 }}%
                            </small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-route fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Delivery Trends
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="deliveryTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Fuel Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="fuelDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Tables -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy"></i> Top Performing Routes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Route</th>
                                    <th>Deliveries</th>
                                    <th>Efficiency</th>
                                    <th>On-Time %</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for route in top_routes %}
                                <tr>
                                    <td>
                                        <strong>{{ route.name }}</strong><br>
                                        <small class="text-muted">{{ route.stations_count }} stations</small>
                                    </td>
                                    <td>{{ route.total_deliveries }}</td>
                                    <td>
                                        <span class="badge bg-{{ route.efficiency_color }}">
                                            {{ route.efficiency|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ route.on_time_color }}">
                                            {{ route.on_time_percentage|floatformat:1 }}%
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No route data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-gas-pump"></i> Station Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Station</th>
                                    <th>Orders</th>
                                    <th>Volume (L)</th>
                                    <th>Avg. Delivery Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for station in station_performance %}
                                <tr>
                                    <td>
                                        <strong>{{ station.name }}</strong><br>
                                        <small class="text-muted">{{ station.region }}</small>
                                    </td>
                                    <td>{{ station.total_orders }}</td>
                                    <td>{{ station.total_volume|floatformat:0 }}</td>
                                    <td>
                                        <span class="badge bg-{{ station.delivery_time_color }}">
                                            {{ station.avg_delivery_time|floatformat:1 }}h
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No station data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fleet Utilization -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck"></i> Fleet Utilization
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for truck in fleet_utilization %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h6 class="card-title">{{ truck.name }}</h6>
                                    <div class="progress mb-2" style="height: 20px;">
                                        <div class="progress-bar bg-{{ truck.utilization_color }}" 
                                             style="width: {{ truck.utilization_percentage }}%">
                                            {{ truck.utilization_percentage|floatformat:1 }}%
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        {{ truck.total_hours|floatformat:1 }}h / {{ truck.available_hours }}h
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <p class="text-center text-muted">No fleet data available</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Delivery Trends Chart
const deliveryCtx = document.getElementById('deliveryTrendsChart').getContext('2d');
const deliveryChart = new Chart(deliveryCtx, {
    type: 'line',
    data: {
        labels: {{ delivery_chart_labels|safe }},
        datasets: [{
            label: 'Deliveries',
            data: {{ delivery_chart_data|safe }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Fuel Distribution Chart
const fuelCtx = document.getElementById('fuelDistributionChart').getContext('2d');
const fuelChart = new Chart(fuelCtx, {
    type: 'doughnut',
    data: {
        labels: {{ fuel_chart_labels|safe }},
        datasets: [{
            data: {{ fuel_chart_data|safe }},
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Date range helper function
function setDateRange(days) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);
    
    document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0];
    document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0];
    
    // Submit form
    document.querySelector('form').submit();
}
</script>
{% endblock %}
