#!/usr/bin/env python
"""
Direct database insertion script for Albanian OptiKarburant sample data
This script bypasses Django ORM spatial issues by using raw SQL where needed
"""

import sqlite3
import os
from datetime import datetime, time, timedelta
import random

def create_database_and_tables():
    """Create SQLite database and basic tables"""
    db_path = 'albania_sample_data.db'
    
    # Remove existing database
    if os.path.exists(db_path):
        os.remove(db_path)
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create products table
    cursor.execute('''
        CREATE TABLE products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            emri TEXT UNIQUE NOT NULL,
            densiteti REAL DEFAULT 0.85,
            ngjyra_kodi TEXT DEFAULT '#3498db',
            eshte_aktiv BOOLEAN DEFAULT 1,
            data_krijimit TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create central depot table
    cursor.execute('''
        CREATE TABLE central_depot (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            emri TEXT NOT NULL,
            latitude REAL NOT NULL,
            longitude REAL NOT NULL,
            adresa TEXT,
            kapaciteti_ngarkimi INTEGER DEFAULT 10,
            koha_mesatare_ngarkimi INTEGER DEFAULT 90,
            orar_punes_nga TEXT DEFAULT '06:00',
            orar_punes_deri TEXT DEFAULT '22:00',
            eshte_aktiv BOOLEAN DEFAULT 1
        )
    ''')
    
    # Create stations table
    cursor.execute('''
        CREATE TABLE stations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            emri TEXT NOT NULL,
            kodi TEXT UNIQUE NOT NULL,
            latitude REAL NOT NULL,
            longitude REAL NOT NULL,
            adresa TEXT,
            city TEXT,
            station_type TEXT,
            orar_pranimi_nga TEXT DEFAULT '06:00',
            orar_pranimi_deri TEXT DEFAULT '18:00',
            kerkon_pompe BOOLEAN DEFAULT 0,
            kerkon_kontaliter BOOLEAN DEFAULT 1,
            max_kamione_njekohesisht INTEGER DEFAULT 1,
            koha_mesatare_shkarkimi INTEGER DEFAULT 45,
            max_pesha_kamioni_ton REAL,
            max_gjatesia_kamioni_m REAL,
            menaxher_emri TEXT,
            telefoni TEXT,
            eshte_aktiv BOOLEAN DEFAULT 1,
            data_krijimit TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create drivers table
    cursor.execute('''
        CREATE TABLE drivers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            emri TEXT NOT NULL,
            mbiemri TEXT NOT NULL,
            telefoni TEXT,
            email TEXT,
            leje_drejtimi_numri TEXT UNIQUE NOT NULL,
            leje_drejtimi_skadon DATE,
            leje_adr BOOLEAN DEFAULT 0,
            ore_punes_maksimale_ditor INTEGER DEFAULT 8,
            ore_drejtimi_maksimale_ditor INTEGER DEFAULT 6,
            experience_level TEXT,
            data_punesimit DATE,
            eshte_aktiv BOOLEAN DEFAULT 1
        )
    ''')
    
    # Create trucks table
    cursor.execute('''
        CREATE TABLE trucks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            targa TEXT UNIQUE NOT NULL,
            modeli TEXT,
            viti_prodhimit INTEGER,
            shofer_id INTEGER,
            pesha_maksimale_bruto_ton REAL,
            gjatesia_totale_m REAL,
            eshte_trailer BOOLEAN DEFAULT 0,
            ka_pompe BOOLEAN DEFAULT 0,
            ka_kontaliter BOOLEAN DEFAULT 0,
            ka_gps BOOLEAN DEFAULT 1,
            statusi TEXT DEFAULT 'i_lire',
            odometri_aktual_km INTEGER DEFAULT 0,
            km_mirembajtjes_rradheses INTEGER,
            konsumi_mesatar_l_100km REAL DEFAULT 35.0,
            truck_type TEXT,
            eshte_aktiv BOOLEAN DEFAULT 1,
            FOREIGN KEY (shofer_id) REFERENCES drivers (id)
        )
    ''')
    
    # Create tanks table
    cursor.execute('''
        CREATE TABLE tanks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            station_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            numri_tankut TEXT NOT NULL,
            kapaciteti_total REAL NOT NULL,
            sasia_aktuale REAL NOT NULL,
            niveli_minimal_sigurise REAL NOT NULL,
            niveli_i_porosise REAL NOT NULL,
            konsumi_mesatar_ditor REAL NOT NULL,
            sasia_minimale_dorezimi REAL NOT NULL,
            perqindja_maksimale_mbushjes REAL DEFAULT 95,
            data_matjes_fundit TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (station_id) REFERENCES stations (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    conn.commit()
    return conn

def insert_products(conn):
    """Insert Albanian fuel products"""
    cursor = conn.cursor()
    products_data = [
        ('Naftë D2', 0.85, '#2c3e50'),
        ('Benzinë 95', 0.75, '#e74c3c'),
        ('Benzinë 100', 0.75, '#c0392b'),
        ('Gaz i Lëngshëm (LPG)', 0.51, '#3498db'),
        ('Naftë Ngrohje', 0.87, '#8e44ad'),
    ]
    
    cursor.executemany(
        'INSERT INTO products (emri, densiteti, ngjyra_kodi) VALUES (?, ?, ?)',
        products_data
    )
    conn.commit()
    print(f"✓ Inserted {len(products_data)} products")

def insert_central_depot(conn):
    """Insert central depot in Tirana"""
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO central_depot 
        (emri, latitude, longitude, adresa, kapaciteti_ngarkimi, koha_mesatare_ngarkimi, orar_punes_nga, orar_punes_deri)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        'Depo Qendrore Tiranë',
        41.3275, 19.8189,
        'Rruga Industriale, Tiranë, Shqipëri',
        8, 90, '06:00', '22:00'
    ))
    conn.commit()
    print("✓ Inserted central depot")

def insert_stations(conn):
    """Insert Albanian fuel stations"""
    cursor = conn.cursor()
    stations_data = [
        # Tirana area
        ('Stacioni Qender Tiranë', 'TIR001', 41.3275, 19.8189, 'Tiranë', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Kombinat', 'TIR002', 41.2911, 19.8607, 'Tiranë', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Don Bosko', 'TIR003', 41.3151, 19.8331, 'Tiranë', 'standard', '06:00', '18:00', 1, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Kashar', 'TIR004', 41.3847, 19.7736, 'Tiranë', 'highway', '00:00', '23:59', 0, 1, 3, 30, 40.0, 16.5),
        
        # Durrës
        ('Stacioni Durrës Port', 'DUR001', 41.3147, 19.4444, 'Durrës', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Durrës Qender', 'DUR002', 41.3236, 19.4581, 'Durrës', 'standard', '06:00', '18:00', 1, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Durrës Plazh', 'DUR003', 41.3200, 19.4300, 'Durrës', 'seasonal', '07:00', '19:00', 0, 1, 1, 45, 25.0, 12.0),
        
        # Other major cities
        ('Stacioni Shkodër Qender', 'SHK001', 42.0683, 19.5122, 'Shkodër', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Elbasan Qender', 'ELB001', 41.1125, 20.0822, 'Elbasan', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Vlorë Port', 'VLO001', 40.4686, 19.4889, 'Vlorë', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Korçë Qender', 'KOR001', 40.6186, 20.7719, 'Korçë', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Fier Qender', 'FIE001', 40.7239, 19.5556, 'Fier', 'major', '05:00', '20:00', 1, 1, 2, 60, 40.0, 16.5),
        ('Stacioni Fier Industrial', 'FIE002', 40.7300, 19.5600, 'Fier', 'industrial', '06:00', '18:00', 1, 1, 2, 90, 40.0, 16.5),
        
        # Additional stations
        ('Stacioni Berat', 'BER001', 40.7058, 19.9522, 'Berat', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Gjirokastër', 'GJI001', 40.0758, 20.1389, 'Gjirokastër', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Lushnjë', 'LUS001', 40.9419, 19.7050, 'Lushnjë', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Pogradec', 'POG001', 40.9022, 20.6528, 'Pogradec', 'seasonal', '07:00', '19:00', 0, 0, 1, 45, 25.0, 12.0),
        ('Stacioni Kukës', 'KUK001', 42.0772, 20.4214, 'Kukës', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
        ('Stacioni Lezhë', 'LEZ001', 41.7836, 19.6439, 'Lezhë', 'standard', '06:00', '18:00', 0, 1, 1, 45, 40.0, 16.5),
    ]
    
    for station_data in stations_data:
        emri, kodi, lat, lng, city, station_type, start_time, end_time, pump, meter, max_trucks, unload_time, max_weight, max_length = station_data
        phone = f"+35569{random.randint(1000000, 9999999)}"
        adresa = f"{emri}, {city}, Shqipëri"
        manager = f"Menaxher {city}"
        
        cursor.execute('''
            INSERT INTO stations 
            (emri, kodi, latitude, longitude, adresa, city, station_type, orar_pranimi_nga, orar_pranimi_deri,
             kerkon_pompe, kerkon_kontaliter, max_kamione_njekohesisht, koha_mesatare_shkarkimi,
             max_pesha_kamioni_ton, max_gjatesia_kamioni_m, menaxher_emri, telefoni)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (emri, kodi, lat, lng, adresa, city, station_type, start_time, end_time,
              pump, meter, max_trucks, unload_time, max_weight, max_length, manager, phone))
    
    conn.commit()
    print(f"✓ Inserted {len(stations_data)} stations")

def insert_drivers(conn):
    """Insert Albanian drivers"""
    cursor = conn.cursor()
    drivers_data = [
        ('Agim', 'Kelmendi', '+355691234567', 'senior'),
        ('Besnik', 'Hoxha', '+355692345678', 'senior'),
        ('Driton', 'Brahimaj', '+355693456789', 'experienced'),
        ('Ermal', 'Gjoka', '+355694567890', 'experienced'),
        ('Fadil', 'Rama', '+355695678901', 'junior'),
        ('Genti', 'Berisha', '+355696789012', 'senior'),
        ('Ilir', 'Dervishi', '+355697890123', 'experienced'),
        ('Jeton', 'Kastrati', '+355698901234', 'junior'),
        ('Klajdi', 'Mema', '+355699012345', 'experienced'),
        ('Luan', 'Nallbani', '+355691123456', 'senior'),
    ]
    
    for i, (emri, mbiemri, telefoni, experience) in enumerate(drivers_data):
        # Set experience-based parameters
        if experience == 'senior':
            employment_days = random.randint(1800, 3650)
            has_adr = 1
            max_work_hours = 10
            max_drive_hours = 8
        elif experience == 'experienced':
            employment_days = random.randint(730, 1800)
            has_adr = random.choice([0, 1])
            max_work_hours = 9
            max_drive_hours = 7
        else:  # junior
            employment_days = random.randint(90, 730)
            has_adr = 0
            max_work_hours = 8
            max_drive_hours = 6
        
        license_number = f"AL{2024000 + i:06d}"
        email = f"{emri.lower()}.{mbiemri.lower()}@optikarburant.al"
        license_expires = (datetime.now() + timedelta(days=random.randint(180, 1095))).date()
        employment_date = (datetime.now() - timedelta(days=employment_days)).date()
        
        cursor.execute('''
            INSERT INTO drivers 
            (emri, mbiemri, telefoni, email, leje_drejtimi_numri, leje_drejtimi_skadon, leje_adr,
             ore_punes_maksimale_ditor, ore_drejtimi_maksimale_ditor, experience_level, data_punesimit)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (emri, mbiemri, telefoni, email, license_number, license_expires, has_adr,
              max_work_hours, max_drive_hours, experience, employment_date))
    
    conn.commit()
    print(f"✓ Inserted {len(drivers_data)} drivers")

def insert_trucks(conn):
    """Insert Albanian truck fleet"""
    cursor = conn.cursor()
    trucks_data = [
        ('AA 001 TR', 'Mercedes Actros 2545', 2022, 'premium', 1, 1, 1),
        ('AA 002 TR', 'Volvo FH 460', 2021, 'premium', 1, 1, 1),
        ('AA 003 TR', 'Scania R 450', 2023, 'premium', 1, 1, 1),
        ('AA 004 TR', 'MAN TGX 440', 2020, 'standard', 1, 1, 0),
        ('AA 005 TR', 'DAF XF 440', 2019, 'standard', 0, 1, 0),
        ('AA 006 TR', 'Iveco Stralis 420', 2020, 'standard', 1, 1, 0),
        ('AA 007 TR', 'Mercedes Atego 1218', 2018, 'basic', 0, 1, 0),
        ('AA 008 TR', 'Volvo FL 280', 2017, 'basic', 0, 0, 0),
        ('AA 009 TR', 'MAN TGL 250', 2019, 'basic', 0, 1, 0),
        ('AA 010 TR', 'Scania P 410', 2021, 'standard', 1, 1, 1),
    ]

    # Get driver IDs
    cursor.execute('SELECT id FROM drivers ORDER BY id')
    driver_ids = [row[0] for row in cursor.fetchall()]

    for i, (targa, modeli, year, truck_type, has_pump, has_meter, is_trailer) in enumerate(trucks_data):
        # Set truck specifications based on type
        if truck_type == 'premium':
            max_weight = 40.0
            length = 16.5
            consumption = 32.0
            odometer = random.randint(50000, 150000)
        elif truck_type == 'standard':
            max_weight = 35.0
            length = 14.0 if not is_trailer else 16.5
            consumption = 35.0
            odometer = random.randint(100000, 300000)
        else:  # basic
            max_weight = 18.0
            length = 10.0
            consumption = 25.0
            odometer = random.randint(150000, 400000)

        driver_id = driver_ids[i] if i < len(driver_ids) else None
        maintenance_km = odometer + random.randint(10000, 50000)

        cursor.execute('''
            INSERT INTO trucks
            (targa, modeli, viti_prodhimit, shofer_id, pesha_maksimale_bruto_ton, gjatesia_totale_m,
             eshte_trailer, ka_pompe, ka_kontaliter, odometri_aktual_km, km_mirembajtjes_rradheses,
             konsumi_mesatar_l_100km, truck_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (targa, modeli, year, driver_id, max_weight, length, is_trailer, has_pump, has_meter,
              odometer, maintenance_km, consumption, truck_type))

    conn.commit()
    print(f"✓ Inserted {len(trucks_data)} trucks")

def insert_tanks(conn):
    """Insert fuel tanks for stations"""
    cursor = conn.cursor()

    # Get station and product IDs
    cursor.execute('SELECT id, station_type FROM stations')
    stations = cursor.fetchall()

    cursor.execute('SELECT id, emri FROM products')
    products = cursor.fetchall()

    tank_count = 0
    for station_id, station_type in stations:
        # Determine number of products based on station type
        if station_type == 'major':
            num_products = 4  # Most products
        elif station_type == 'highway':
            num_products = 3  # Diesel, Gasoline 95, 100
        elif station_type == 'industrial':
            num_products = 2  # Diesel and heating oil
        elif station_type == 'seasonal':
            num_products = 2  # Just basics
        else:  # standard
            num_products = 3

        # Create tanks for this station
        for i in range(min(num_products, len(products))):
            product_id, product_name = products[i]

            # Set tank capacity based on station type and product
            if station_type == 'major':
                base_capacity = 30000
            elif station_type == 'highway':
                base_capacity = 40000
            elif station_type == 'industrial':
                base_capacity = 50000
            elif station_type == 'seasonal':
                base_capacity = 15000
            else:  # standard
                base_capacity = 25000

            capacity = base_capacity + random.randint(-5000, 10000)
            current_level = capacity * random.uniform(0.6, 0.9)
            safety_level = capacity * 0.1
            reorder_level = capacity * 0.25
            min_delivery = min(5000, capacity * 0.2)
            daily_consumption = capacity * random.uniform(0.03, 0.08)  # 3-8% per day

            cursor.execute('''
                INSERT INTO tanks
                (station_id, product_id, numri_tankut, kapaciteti_total, sasia_aktuale,
                 niveli_minimal_sigurise, niveli_i_porosise, konsumi_mesatar_ditor, sasia_minimale_dorezimi)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (station_id, product_id, f"T{i+1}", capacity, current_level,
                  safety_level, reorder_level, daily_consumption, min_delivery))

            tank_count += 1

    conn.commit()
    print(f"✓ Inserted {tank_count} tanks")

def main():
    """Main function to create Albanian sample data"""
    print("🇦🇱 OptiKarburant Albania - Sample Data Creation")
    print("=" * 60)

    try:
        # Create database and tables
        print("Creating database and tables...")
        conn = create_database_and_tables()

        # Insert data
        insert_products(conn)
        insert_central_depot(conn)
        insert_stations(conn)
        insert_drivers(conn)
        insert_trucks(conn)
        insert_tanks(conn)

        conn.close()

        print("\n" + "=" * 60)
        print("✅ Albanian sample data created successfully!")
        print("📁 Database file: albania_sample_data.db")
        print("🔍 You can open this file with any SQLite browser to explore the data")
        print("\n📊 Data Summary:")
        print("   • 5 Fuel Products (Naftë D2, Benzinë 95, Benzinë 100, LPG, Naftë Ngrohje)")
        print("   • 1 Central Depot in Tirana")
        print("   • 19 Fuel Stations across Albania")
        print("   • 10 Professional Drivers")
        print("   • 10 Delivery Trucks")
        print("   • 50+ Fuel Storage Tanks")
        print("\n🚀 This data represents a realistic Albanian fuel distribution network!")
        print("💡 Use this data to test route optimization, inventory management, and delivery scheduling!")

    except Exception as e:
        print(f"\n❌ Error during data creation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
