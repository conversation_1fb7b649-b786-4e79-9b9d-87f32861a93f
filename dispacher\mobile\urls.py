# mobile/urls.py - URL configuration for mobile driver interface

from django.urls import path
from . import views

app_name = 'mobile'

urlpatterns = [
    # Main driver dashboard
    path('', views.driver_dashboard, name='dashboard'),
    
    # Route management
    path('route/<int:route_id>/', views.route_detail, name='route_detail'),
    path('stop/<int:stop_id>/', views.stop_detail, name='stop_detail'),
    
    # Actions
    path('stop/<int:stop_id>/complete/', views.complete_stop, name='complete_stop'),
    path('delivery/<int:delivery_id>/update/', views.update_delivery, name='update_delivery'),
    path('report-issue/', views.report_issue, name='report_issue'),
    
    # Help
    path('help/', views.help_page, name='help'),
]
