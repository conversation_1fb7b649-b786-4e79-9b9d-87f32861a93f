# optikarburant/celery.py - Celery configuration for background tasks

import os
from celery import Celery
from django.conf import settings

# Set the default Django settings module for the 'celery' program.
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings')

app = Celery('optikarburant')

# Using a string here means the worker doesn't have to serialize
# the configuration object to child processes.
app.config_from_object('django.conf:settings', namespace='CELERY')

# Load task modules from all registered Django apps.
app.autodiscover_tasks()

# Celery beat schedule for periodic tasks
app.conf.beat_schedule = {
    'check-inventory-levels': {
        'task': 'logistics.tasks.check_inventory_levels',
        'schedule': 3600.0,  # Run every hour
    },
    'generate-daily-orders': {
        'task': 'logistics.tasks.generate_automatic_orders',
        'schedule': 21600.0,  # Run every 6 hours
    },
    'update-distance-matrix': {
        'task': 'optimization.tasks.update_distance_matrix',
        'schedule': 86400.0,  # Run daily
    },
    'cleanup-old-routes': {
        'task': 'logistics.tasks.cleanup_old_routes',
        'schedule': 86400.0,  # Run daily
    },
}

app.conf.timezone = settings.TIME_ZONE

@app.task(bind=True)
def debug_task(self):
    """Debug task for testing Celery setup"""
    print(f'Request: {self.request!r}')
    return 'Debug task completed successfully'
