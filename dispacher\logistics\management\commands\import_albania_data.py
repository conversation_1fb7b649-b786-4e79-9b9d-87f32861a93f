# logistics/management/commands/import_albania_data.py
"""
Management command to import Albanian sample data into Django models
This bypasses the spatial field issues by using simple coordinate fields
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, time, timedelta
import sqlite3
import os

from logistics.models import (
    Produkt, DepoQendrore, Stacion, Depo<PERSON>te, <PERSON>, 
    Kamion, Particion, Porosi
)


class MockPoint:
    """Mock Point class to handle coordinates without spatial dependencies"""
    def __init__(self, lng, lat):
        self.x = lng
        self.y = lat
        self.lng = lng
        self.lat = lat
    
    def __str__(self):
        return f"POINT({self.lng} {self.lat})"


class Command(BaseCommand):
    help = 'Import Albanian sample data from SQLite database into Django models'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before importing',
        )
        parser.add_argument(
            '--db-file',
            type=str,
            default='albania_sample_data.db',
            help='Path to the Albanian sample data SQLite file',
        )
    
    def handle(self, *args, **options):
        db_file = options['db_file']
        
        if not os.path.exists(db_file):
            self.stdout.write(
                self.style.ERROR(f'Database file {db_file} not found!')
            )
            return
        
        if options['clear']:
            self.stdout.write('Clearing existing Django data...')
            self.clear_django_data()
        
        self.stdout.write('Importing Albanian sample data...')
        
        try:
            conn = sqlite3.connect(db_file)
            
            # Import data in dependency order
            products = self.import_products(conn)
            self.stdout.write(f'✓ Imported {len(products)} products')
            
            depot = self.import_central_depot(conn)
            self.stdout.write(f'✓ Imported central depot')
            
            stations = self.import_stations(conn)
            self.stdout.write(f'✓ Imported {len(stations)} stations')
            
            drivers = self.import_drivers(conn)
            self.stdout.write(f'✓ Imported {len(drivers)} drivers')
            
            trucks = self.import_trucks(conn, drivers)
            self.stdout.write(f'✓ Imported {len(trucks)} trucks')
            
            tanks = self.import_tanks(conn, stations, products)
            self.stdout.write(f'✓ Imported {len(tanks)} tanks')
            
            conn.close()
            
            self.stdout.write(
                self.style.SUCCESS('✅ Albanian data imported successfully!')
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error importing data: {e}')
            )
            import traceback
            traceback.print_exc()
    
    def clear_django_data(self):
        """Clear existing Django data"""
        Porosi.objects.all().delete()
        Particion.objects.all().delete()
        Kamion.objects.all().delete()
        Shofer.objects.all().delete()
        Depozite.objects.all().delete()
        Stacion.objects.all().delete()
        DepoQendrore.objects.all().delete()
        Produkt.objects.all().delete()
    
    def import_products(self, conn):
        """Import products from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('SELECT emri, densiteti, ngjyra_kodi FROM products')
        products_data = cursor.fetchall()
        
        products = []
        for emri, densiteti, ngjyra_kodi in products_data:
            product, created = Produkt.objects.get_or_create(
                emri=emri,
                defaults={
                    'densiteti': densiteti,
                    'ngjyra_kodi': ngjyra_kodi,
                    'eshte_aktiv': True,
                }
            )
            products.append(product)
        
        return products
    
    def import_central_depot(self, conn):
        """Import central depot from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('''
            SELECT emri, latitude, longitude, adresa, kapaciteti_ngarkimi, 
                   koha_mesatare_ngarkimi, orar_punes_nga, orar_punes_deri
            FROM central_depot LIMIT 1
        ''')
        depot_data = cursor.fetchone()
        
        if depot_data:
            emri, lat, lng, adresa, kapaciteti, koha, orar_nga, orar_deri = depot_data
            
            depot, created = DepoQendrore.objects.get_or_create(
                emri=emri,
                defaults={
                    'vendndodhja': MockPoint(lng, lat),
                    'adresa': adresa,
                    'kapaciteti_ngarkimi': kapaciteti,
                    'koha_mesatare_ngarkimi': koha,
                    'orar_punes_nga': time.fromisoformat(orar_nga),
                    'orar_punes_deri': time.fromisoformat(orar_deri),
                    'eshte_aktiv': True,
                }
            )
            return depot
        return None
    
    def import_stations(self, conn):
        """Import stations from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('''
            SELECT emri, kodi, latitude, longitude, adresa, orar_pranimi_nga, orar_pranimi_deri,
                   kerkon_pompe, kerkon_kontaliter, max_kamione_njekohesisht, koha_mesatare_shkarkimi,
                   max_pesha_kamioni_ton, max_gjatesia_kamioni_m, menaxher_emri, telefoni
            FROM stations
        ''')
        stations_data = cursor.fetchall()
        
        stations = []
        for station_data in stations_data:
            (emri, kodi, lat, lng, adresa, orar_nga, orar_deri, kerkon_pompe, kerkon_kontaliter,
             max_kamione, koha_shkarkimi, max_pesha, max_gjatesia, menaxher, telefoni) = station_data
            
            station, created = Stacion.objects.get_or_create(
                kodi=kodi,
                defaults={
                    'emri': emri,
                    'vendndodhja': MockPoint(lng, lat),
                    'adresa': adresa,
                    'orar_pranimi_nga': time.fromisoformat(orar_nga),
                    'orar_pranimi_deri': time.fromisoformat(orar_deri),
                    'kerkon_pompe': bool(kerkon_pompe),
                    'kerkon_kontaliter': bool(kerkon_kontaliter),
                    'max_kamione_njekohesisht': max_kamione,
                    'koha_mesatare_shkarkimi': koha_shkarkimi,
                    'max_pesha_kamioni_ton': max_pesha,
                    'max_gjatesia_kamioni_m': max_gjatesia,
                    'menaxher_emri': menaxher,
                    'telefoni': telefoni,
                    'eshte_aktiv': True,
                }
            )
            stations.append(station)
        
        return stations
    
    def import_drivers(self, conn):
        """Import drivers from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('''
            SELECT emri, mbiemri, telefoni, email, leje_drejtimi_numri, leje_drejtimi_skadon,
                   leje_adr, ore_punes_maksimale_ditor, ore_drejtimi_maksimale_ditor, data_punesimit
            FROM drivers
        ''')
        drivers_data = cursor.fetchall()
        
        drivers = []
        for driver_data in drivers_data:
            (emri, mbiemri, telefoni, email, leje_numri, leje_skadon, leje_adr,
             ore_punes, ore_drejtimi, data_punesimit) = driver_data
            
            driver, created = Shofer.objects.get_or_create(
                leje_drejtimi_numri=leje_numri,
                defaults={
                    'emri': emri,
                    'mbiemri': mbiemri,
                    'telefoni': telefoni,
                    'email': email,
                    'leje_drejtimi_skadon': datetime.fromisoformat(leje_skadon).date(),
                    'leje_adr': bool(leje_adr),
                    'ore_punes_maksimale_ditor': ore_punes,
                    'ore_drejtimi_maksimale_ditor': ore_drejtimi,
                    'data_punesimit': datetime.fromisoformat(data_punesimit).date(),
                    'eshte_aktiv': True,
                }
            )
            drivers.append(driver)
        
        return drivers
    
    def import_trucks(self, conn, drivers):
        """Import trucks from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('''
            SELECT targa, modeli, viti_prodhimit, shofer_id, pesha_maksimale_bruto_ton,
                   gjatesia_totale_m, eshte_trailer, ka_pompe, ka_kontaliter,
                   odometri_aktual_km, km_mirembajtjes_rradheses, konsumi_mesatar_l_100km
            FROM trucks
        ''')
        trucks_data = cursor.fetchall()
        
        # Create driver mapping
        driver_map = {i+1: driver for i, driver in enumerate(drivers)}
        
        trucks = []
        for truck_data in trucks_data:
            (targa, modeli, viti, shofer_id, pesha, gjatesia, eshte_trailer, ka_pompe,
             ka_kontaliter, odometri, km_mirembajtjes, konsumi) = truck_data
            
            shofer = driver_map.get(shofer_id) if shofer_id else None
            
            truck, created = Kamion.objects.get_or_create(
                targa=targa,
                defaults={
                    'modeli': modeli,
                    'viti_prodhimit': viti,
                    'shofer_aktual': shofer,
                    'pesha_maksimale_bruto_ton': pesha,
                    'gjatesia_totale_m': gjatesia,
                    'eshte_trailer': bool(eshte_trailer),
                    'ka_pompe': bool(ka_pompe),
                    'ka_kontaliter': bool(ka_kontaliter),
                    'ka_gps': True,
                    'statusi': 'i_lire',
                    'odometri_aktual_km': odometri,
                    'km_mirembajtjes_rradheses': km_mirembajtjes,
                    'konsumi_mesatar_l_100km': konsumi,
                    'eshte_aktiv': True,
                }
            )
            trucks.append(truck)
        
        return trucks
    
    def import_tanks(self, conn, stations, products):
        """Import tanks from SQLite to Django"""
        cursor = conn.cursor()
        cursor.execute('''
            SELECT station_id, product_id, numri_tankut, kapaciteti_total, sasia_aktuale,
                   niveli_minimal_sigurise, niveli_i_porosise, konsumi_mesatar_ditor,
                   sasia_minimale_dorezimi, perqindja_maksimale_mbushjes
            FROM tanks
        ''')
        tanks_data = cursor.fetchall()
        
        # Create mappings
        station_map = {i+1: station for i, station in enumerate(stations)}
        product_map = {i+1: product for i, product in enumerate(products)}
        
        tanks = []
        for tank_data in tanks_data:
            (station_id, product_id, numri_tankut, kapaciteti_total, sasia_aktuale,
             niveli_minimal, niveli_porosise, konsumi_ditor, sasia_minimale, perqindja_max) = tank_data
            
            station = station_map.get(station_id)
            product = product_map.get(product_id)
            
            if station and product:
                tank, created = Depozite.objects.get_or_create(
                    stacion=station,
                    produkt=product,
                    numri_tankut=numri_tankut,
                    defaults={
                        'kapaciteti_total': kapaciteti_total,
                        'sasia_aktuale': sasia_aktuale,
                        'niveli_minimal_sigurise': niveli_minimal,
                        'niveli_i_porosise': niveli_porosise,
                        'konsumi_mesatar_ditor': konsumi_ditor,
                        'sasia_minimale_dorezimi': sasia_minimale,
                        'perqindja_maksimale_mbushjes': perqindja_max,
                        'data_matjes_fundit': timezone.now(),
                    }
                )
                tanks.append(tank)
        
        return tanks
