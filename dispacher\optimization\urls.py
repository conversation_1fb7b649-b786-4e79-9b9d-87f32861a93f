# optimization/urls.py - URL configuration for optimization module

from django.urls import path
from . import views

app_name = 'optimization'

urlpatterns = [
    # Main optimization dashboard
    path('', views.optimization_dashboard, name='dashboard'),
    
    # Optimization process
    path('start/', views.optimization_form, name='form'),
    path('progress/<str:task_id>/', views.optimization_progress, name='progress'),
    path('api/progress/<str:task_id>/', views.optimization_progress_api, name='progress_api'),
    
    # Emergency optimization
    path('emergency/', views.start_emergency_optimization, name='emergency'),
    path('quick/', views.quick_optimize, name='quick'),
    
    # Route management
    path('routes/', views.route_list, name='route_list'),
    path('routes/<int:route_id>/', views.route_detail, name='route_detail'),
    path('routes/<int:route_id>/delete/', views.delete_route, name='delete_route'),
    path('routes/<int:route_id>/approve/', views.approve_route, name='approve_route'),
    
    # Settings
    path('settings/', views.optimization_settings, name='settings'),
]
