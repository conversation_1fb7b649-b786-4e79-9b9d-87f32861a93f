<!-- templates/mobile/help.html - Mobile Help and Support -->
{% extends 'mobile/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .help-header {
        background: linear-gradient(135deg, #6f42c1, #e83e8c);
        color: white;
        padding: 1.5rem;
        margin: -1rem -1rem 1rem -1rem;
        border-radius: 0 0 1rem 1rem;
        text-align: center;
    }
    .help-section {
        margin-bottom: 1.5rem;
    }
    .help-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        transition: transform 0.2s;
    }
    .help-card:hover {
        transform: translateY(-2px);
    }
    .help-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
    }
    .faq-item {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 0;
    }
    .faq-item:last-child {
        border-bottom: none;
    }
    .faq-question {
        cursor: pointer;
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    .faq-answer {
        color: #6c757d;
        display: none;
        padding-left: 1rem;
        border-left: 3px solid #007bff;
        margin-top: 0.5rem;
    }
    .faq-answer.show {
        display: block;
    }
    .contact-card {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
    }
    .emergency-card {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-3">
    <!-- Help Header -->
    <div class="help-header">
        <i class="fas fa-question-circle fa-3x mb-3"></i>
        <h4 class="mb-1">Help & Support</h4>
        <p class="mb-0 opacity-75">Get help with OptiKarburant mobile app</p>
    </div>

    <!-- Quick Actions -->
    <div class="help-section">
        <h6 class="mb-3">Quick Actions</h6>
        <div class="row">
            <div class="col-6 mb-3">
                <div class="help-card card h-100">
                    <div class="card-body text-center">
                        <div class="help-icon bg-primary text-white mx-auto mb-2">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h6 class="card-title">Call Support</h6>
                        <a href="tel:+************" class="btn btn-sm btn-outline-primary">
                            Call Now
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-6 mb-3">
                <div class="help-card card h-100">
                    <div class="card-body text-center">
                        <div class="help-icon bg-success text-white mx-auto mb-2">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h6 class="card-title">Live Chat</h6>
                        <button class="btn btn-sm btn-outline-success" onclick="openChat()">
                            Start Chat
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Emergency Contact -->
    <div class="help-section">
        <div class="emergency-card">
            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
            <h6 class="mb-2">Emergency Support</h6>
            <p class="mb-3 small">For urgent issues during delivery</p>
            <a href="tel:+************" class="btn btn-light btn-lg">
                <i class="fas fa-phone"></i> Emergency: +355 69 299 9999
            </a>
        </div>
    </div>

    <!-- How-to Guides -->
    <div class="help-section">
        <h6 class="mb-3">How-to Guides</h6>
        
        <div class="help-card card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="help-icon bg-info text-white">
                        <i class="fas fa-route"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Starting a Route</h6>
                        <small class="text-muted">Learn how to begin your delivery route</small>
                    </div>
                </div>
                <div class="mt-3">
                    <ol class="small">
                        <li>Check your assigned route on the dashboard</li>
                        <li>Tap on the route to view details</li>
                        <li>Verify truck and compartment assignments</li>
                        <li>Tap "Start Route" to begin</li>
                        <li>Navigate to the first stop</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="help-card card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="help-icon bg-warning text-white">
                        <i class="fas fa-gas-pump"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Completing a Delivery</h6>
                        <small class="text-muted">Step-by-step delivery process</small>
                    </div>
                </div>
                <div class="mt-3">
                    <ol class="small">
                        <li>Arrive at the station and park safely</li>
                        <li>Connect delivery equipment</li>
                        <li>Verify product type and quantity</li>
                        <li>Complete the delivery</li>
                        <li>Take photos of receipts/meters</li>
                        <li>Enter actual quantity delivered</li>
                        <li>Mark delivery as complete</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="help-card card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="help-icon bg-danger text-white">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div>
                        <h6 class="mb-1">Reporting Issues</h6>
                        <small class="text-muted">How to report problems during delivery</small>
                    </div>
                </div>
                <div class="mt-3">
                    <ol class="small">
                        <li>Tap "Report Issue" on the stop screen</li>
                        <li>Select the type of issue</li>
                        <li>Provide detailed description</li>
                        <li>Take photos if relevant</li>
                        <li>Mark as urgent if needed</li>
                        <li>Submit the report</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="help-section">
        <h6 class="mb-3">Frequently Asked Questions</h6>
        <div class="help-card card">
            <div class="card-body">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <i class="fas fa-chevron-right me-2"></i>
                        What if I can't access a station?
                    </div>
                    <div class="faq-answer">
                        If you cannot access a station due to closure, road blocks, or other issues, 
                        use the "Report Issue" feature and select "Cannot access station". 
                        Contact dispatch immediately for alternative instructions.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <i class="fas fa-chevron-right me-2"></i>
                        What if the delivery quantity doesn't match?
                    </div>
                    <div class="faq-answer">
                        If there's a quantity mismatch, enter the actual delivered amount in the app. 
                        Add detailed notes explaining the difference and take photos of relevant documentation. 
                        Report the issue if the difference is significant.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <i class="fas fa-chevron-right me-2"></i>
                        How do I handle equipment problems?
                    </div>
                    <div class="faq-answer">
                        For equipment issues, stop the delivery immediately and report the problem. 
                        Do not attempt repairs yourself. Contact emergency support if the issue 
                        poses safety risks. Document the problem with photos.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <i class="fas fa-chevron-right me-2"></i>
                        What if my phone battery dies?
                    </div>
                    <div class="faq-answer">
                        Always carry a portable charger. If your phone dies, use the station's 
                        phone to contact dispatch. Complete deliveries manually and update 
                        the system once your phone is charged.
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <i class="fas fa-chevron-right me-2"></i>
                        How do I update my location?
                    </div>
                    <div class="faq-answer">
                        The app automatically tracks your location when GPS is enabled. 
                        Make sure location services are turned on for the OptiKarburant app. 
                        If location isn't updating, restart the app or check your GPS settings.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Information -->
    <div class="help-section">
        <div class="contact-card">
            <i class="fas fa-headset fa-2x mb-3"></i>
            <h6 class="mb-2">Need More Help?</h6>
            <p class="mb-3 small">Our support team is available 24/7</p>
            <div class="row">
                <div class="col-6">
                    <a href="tel:+************" class="btn btn-light btn-sm w-100 mb-2">
                        <i class="fas fa-phone"></i><br>
                        <small>Call Support</small>
                    </a>
                </div>
                <div class="col-6">
                    <a href="mailto:<EMAIL>" class="btn btn-light btn-sm w-100 mb-2">
                        <i class="fas fa-envelope"></i><br>
                        <small>Email Us</small>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- App Information -->
    <div class="help-section">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="mb-2">App Information</h6>
                <p class="text-muted small mb-1">OptiKarburant Mobile v1.0.0</p>
                <p class="text-muted small mb-0">© 2025 OptiKarburant. All rights reserved.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function toggleFaq(element) {
    const answer = element.nextElementSibling;
    const icon = element.querySelector('i');
    
    if (answer.classList.contains('show')) {
        answer.classList.remove('show');
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-right');
    } else {
        // Close all other FAQ items
        document.querySelectorAll('.faq-answer.show').forEach(item => {
            item.classList.remove('show');
        });
        document.querySelectorAll('.faq-question i').forEach(icon => {
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
        });
        
        // Open clicked item
        answer.classList.add('show');
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-down');
    }
}

function openChat() {
    // This would typically open a chat widget or redirect to chat system
    alert('Live chat feature coming soon! Please call support for immediate assistance.');
}

// Add smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
</script>
{% endblock %}
