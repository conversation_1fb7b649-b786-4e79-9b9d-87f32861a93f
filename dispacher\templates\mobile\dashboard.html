<!-- templates/mobile/dashboard.html - Mobile-optimized driver dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Driver Dashboard" %} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f8f9fa;
        font-size: 16px; /* Better readability on mobile */
    }
    
    .mobile-container {
        max-width: 480px;
        margin: 0 auto;
        padding: 0.5rem;
    }
    
    .route-card {
        border-radius: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
        border: none;
    }
    
    .route-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem;
    }
    
    .stop-item {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem;
        transition: background-color 0.2s;
    }
    
    .stop-item:last-child {
        border-bottom: none;
    }
    
    .stop-item.completed {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
    }
    
    .stop-item.current {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
    }
    
    .big-button {
        padding: 1rem 2rem;
        font-size: 1.2rem;
        font-weight: 600;
        border-radius: 0.75rem;
        margin: 0.5rem 0;
        min-height: 60px;
    }
    
    .status-circle {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
    
    .progress-ring {
        width: 80px;
        height: 80px;
    }
    
    .delivery-summary {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border-radius: 1rem;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .floating-action {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        border: none;
        font-size: 1.5rem;
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
        z-index: 1000;
    }
    
    @media (max-width: 576px) {
        .mobile-container {
            padding: 0.25rem;
        }
        
        .route-header {
            padding: 1rem;
        }
        
        .big-button {
            font-size: 1.1rem;
            padding: 0.875rem 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="mobile-container">
    <!-- Header with User Info -->
    <div class="delivery-summary">
        <h4 class="mb-1">{% trans "Welcome" %}, {{ user.get_full_name|default:user.username }}</h4>
        <p class="mb-0">{{ today|date:"l, F d, Y" }}</p>
    </div>

    {% if routes %}
        {% for route in routes %}
        <div class="card route-card">
            <div class="route-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">{{ route.numri_rrugese }}</h5>
                        <small class="opacity-75">{{ route.kamion.targa }}</small>
                    </div>
                    <div class="text-end">
                        <div class="badge bg-light text-dark mb-1">
                            {{ route.get_statusi_display }}
                        </div>
                        <div class="small opacity-75">
                            {{ route.ndalesat.count }} {% trans "stops" %}
                        </div>
                    </div>
                </div>
                
                <!-- Progress Ring -->
                <div class="d-flex align-items-center justify-content-center mt-3">
                    <div class="progress-ring">
                        <svg width="80" height="80" class="progress-ring">
                            <circle cx="40" cy="40" r="35" stroke="rgba(255,255,255,0.3)" stroke-width="4" fill="transparent"/>
                            <circle cx="40" cy="40" r="35" stroke="white" stroke-width="4" fill="transparent"
                                    stroke-dasharray="219.8" stroke-dashoffset="{% widthratio route.progress_percentage 1 2.198 as offset %}{{ offset|floatformat:1 }}"
                                    style="transform: rotate(-90deg); transform-origin: 50% 50%;"/>
                        </svg>
                        <div class="position-absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);">
                            <div class="text-white fw-bold">{{ route.progress_percentage|floatformat:0 }}%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body p-0">
                <!-- Route Information -->
                <div class="p-3 border-bottom">
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted d-block">{% trans "Distance" %}</small>
                            <strong>{{ route.distanca_e_planifikuar_km|default:0|floatformat:0 }} km</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">{% trans "Duration" %}</small>
                            <strong>{{ route.kohezgjatja_e_planifikuar_ore|default:0|floatformat:1 }}h</strong>
                        </div>
                        <div class="col-4">
                            <small class="text-muted d-block">{% trans "Started" %}</small>
                            <strong>
                                {% if route.koha_aktuale_nisjes %}
                                    {{ route.koha_aktuale_nisjes|time:"H:i" }}
                                {% else %}
                                    {% trans "Not started" %}
                                {% endif %}
                            </strong>
                        </div>
                    </div>
                </div>
                
                <!-- Stops List -->
                <div>
                    {% for stop in route.ndalesat.all|slice:":5" %}
                    <div class="stop-item {% if stop.eshte_perfunduar %}completed{% elif forloop.counter0 == route.next_stop_index %}current{% endif %}"
                         data-stop-id="{{ stop.id }}">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                {% if stop.eshte_perfunduar %}
                                    <span class="status-circle bg-success"></span>
                                {% elif forloop.counter0 == route.next_stop_index %}
                                    <span class="status-circle bg-warning"></span>
                                {% else %}
                                    <span class="status-circle bg-light border"></span>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ stop.stacion.emri }}</h6>
                                <p class="mb-1 text-muted small">{{ stop.stacion.adresa }}</p>
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">
                                        {% trans "ETA" %}: {{ stop.koha_parashikuar_mberritjes|time:"H:i" }}
                                    </small>
                                    {% if stop.ngarkesat_shkarkeset.count > 0 %}
                                    <small class="badge bg-info">
                                        {{ stop.ngarkesat_shkarkeset.count }} {% trans "deliveries" %}
                                    </small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="ms-2">
                                {% if not stop.eshte_perfunduar %}
                                <a href="{% url 'mobile:stop_detail' stop.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                                {% else %}
                                <i class="fas fa-check-circle text-success"></i>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if route.ndalesat.count > 5 %}
                    <div class="p-3 text-center">
                        <a href="{% url 'mobile:route_detail' route.id %}" class="btn btn-outline-primary">
                            {% trans "View All" %} {{ route.ndalesat.count }} {% trans "Stops" %}
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="card-footer bg-light">
                <div class="d-grid gap-2">
                    {% if route.statusi == 'miratuar' %}
                    <button class="btn btn-success big-button" onclick="startRoute({{ route.id }})">
                        <i class="fas fa-play me-2"></i> {% trans "Start Route" %}
                    </button>
                    {% elif route.statusi == 'ne_progres' %}
                    <a href="{% url 'mobile:route_detail' route.id %}" class="btn btn-primary big-button">
                        <i class="fas fa-route me-2"></i> {% trans "Continue Route" %}
                    </a>
                    {% elif route.statusi == 'perfunduar' %}
                    <div class="alert alert-success mb-0">
                        <i class="fas fa-check-circle me-2"></i> {% trans "Route Completed" %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <!-- No Routes Available -->
        <div class="card route-card">
            <div class="card-body text-center py-5">
                <i class="fas fa-route fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "No routes assigned for today" %}</h5>
                <p class="text-muted">{% trans "Check back later or contact dispatch" %}</p>
                <button class="btn btn-outline-primary" onclick="window.location.reload()">
                    <i class="fas fa-refresh me-2"></i> {% trans "Refresh" %}
                </button>
            </div>
        </div>
    {% endif %}

    <!-- Emergency Contact -->
    <div class="card route-card">
        <div class="card-body">
            <h6 class="card-title">
                <i class="fas fa-phone me-2 text-danger"></i> {% trans "Emergency Contact" %}
            </h6>
            <div class="d-grid gap-2">
                <a href="tel:+355692000000" class="btn btn-outline-danger">
                    <i class="fas fa-phone me-2"></i> {% trans "Call Dispatch" %}
                </a>
                <button class="btn btn-outline-warning" data-bs-toggle="modal" data-bs-target="#reportIssueModal">
                    <i class="fas fa-exclamation-triangle me-2"></i> {% trans "Report Issue" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Floating Help Button -->
<button class="floating-action" data-bs-toggle="modal" data-bs-target="#helpModal">
    <i class="fas fa-question"></i>
</button>

<!-- Report Issue Modal -->
<div class="modal fade" id="reportIssueModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Report Issue" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="reportIssueForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="issueType" class="form-label">{% trans "Issue Type" %}</label>
                        <select class="form-select" id="issueType" name="issue_type" required>
                            <option value="">{% trans "Select issue type" %}</option>
                            <option value="vehicle">{% trans "Vehicle Problem" %}</option>
                            <option value="delivery">{% trans "Delivery Issue" %}</option>
                            <option value="route">{% trans "Route Problem" %}</option>
                            <option value="other">{% trans "Other" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="issueDescription" class="form-label">{% trans "Description" %}</label>
                        <textarea class="form-control" id="issueDescription" name="description" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "Report Issue" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Help" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>{% trans "How to use the driver app:" %}</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-play text-success me-2"></i>
                        {% trans "Tap 'Start Route' to begin your delivery route" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                        {% trans "Follow stops in order - current stop is highlighted in yellow" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        {% trans "Mark stops as complete after delivery" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-phone text-danger me-2"></i>
                        {% trans "Use emergency contact for urgent issues" %}
                    </li>
                </ul>
                <hr>
                <h6>{% trans "Status Colors:" %}</h6>
                <ul class="list-unstyled">
                    <li><span class="status-circle bg-success"></span> {% trans "Completed" %}</li>
                    <li><span class="status-circle bg-warning"></span> {% trans "Current Stop" %}</li>
                    <li><span class="status-circle bg-light border"></span> {% trans "Pending" %}</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">{% trans "Got it!" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Start route function
function startRoute(routeId) {
    if (confirm('{% trans "Are you ready to start this route?" %}')) {
        fetch(`/api/routes/${routeId}/start-route/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                OptiKarburant.showNotification('{% trans "Route started successfully!" %}', 'success');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                OptiKarburant.showNotification(data.error || '{% trans "Failed to start route" %}', 'error');
            }
        })
        .catch(error => {
            OptiKarburant.showNotification('{% trans "Network error" %}', 'error');
        });
    }
}

// Report issue form
document.getElementById('reportIssueForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "mobile:report_issue" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            OptiKarburant.showNotification(data.message, 'success');
            this.reset();
            bootstrap.Modal.getInstance(document.getElementById('reportIssueModal')).hide();
        } else {
            OptiKarburant.showNotification(data.message || '{% trans "Failed to report issue" %}', 'error');
        }
    })
    .catch(error => {
        OptiKarburant.showNotification('{% trans "Network error" %}', 'error');
    });
});

// Auto-refresh every 30 seconds
setInterval(() => {
    if (document.visibilityState === 'visible') {
        window.location.reload();
    }
}, 30000);

// Pull to refresh
let startY = 0;
let pulling = false;

document.addEventListener('touchstart', function(e) {
    if (window.scrollY === 0) {
        startY = e.touches[0].clientY;
        pulling = true;
    }
}, { passive: true });

document.addEventListener('touchmove', function(e) {
    if (pulling && window.scrollY === 0) {
        const currentY = e.touches[0].clientY;
        const pullDistance = currentY - startY;
        
        if (pullDistance > 100) {
            window.location.reload();
            pulling = false;
        }
    }
}, { passive: true });

document.addEventListener('touchend', function() {
    pulling = false;
}, { passive: true });
</script>
{% endblock %}
