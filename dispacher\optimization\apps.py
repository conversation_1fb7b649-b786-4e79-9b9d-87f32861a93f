# optimization/apps.py
from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class OptimizationConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'optimization'
    verbose_name = _('Route Optimization')
    
    def ready(self):
        """
        Initialize the app when Django starts
        """
        # Import any signal handlers if needed
        pass
