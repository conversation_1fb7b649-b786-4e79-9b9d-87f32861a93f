<!-- templates/reports/dashboard.html - Reports Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .report-card {
        transition: transform 0.2s, box-shadow 0.2s;
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    .report-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: #495057;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
    .trend-indicator {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }
    .trend-up {
        background-color: #d4edda;
        color: #155724;
    }
    .trend-down {
        background-color: #f8d7da;
        color: #721c24;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .quick-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-chart-bar text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="get" class="d-flex gap-2 align-items-center">
                <label class="form-label mb-0 me-2">Date Range:</label>
                <input type="date" name="start_date" class="form-control" 
                       value="{{ start_date|date:'Y-m-d' }}" style="max-width: 150px;">
                <span class="mx-2">to</span>
                <input type="date" name="end_date" class="form-control" 
                       value="{{ end_date|date:'Y-m-d' }}" style="max-width: 150px;">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Apply
                </button>
            </form>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickRange('today')">Today</button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickRange('week')">This Week</button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setQuickRange('month')">This Month</button>
            </div>
        </div>
    </div>

    <!-- Quick Stats Overview -->
    <div class="quick-stats">
        <div class="row">
            <div class="col-md-3 text-center">
                <div class="metric-value">{{ delivery_analytics.total_deliveries }}</div>
                <div class="metric-label">Total Deliveries</div>
                <div class="trend-indicator trend-up mt-2">
                    <i class="fas fa-arrow-up"></i> {{ delivery_analytics.delivery_growth|floatformat:1 }}%
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="metric-value">{{ delivery_analytics.total_volume|floatformat:0 }}</div>
                <div class="metric-label">Liters Delivered</div>
                <div class="trend-indicator trend-up mt-2">
                    <i class="fas fa-arrow-up"></i> {{ delivery_analytics.volume_growth|floatformat:1 }}%
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="metric-value">{{ delivery_analytics.on_time_percentage|floatformat:1 }}%</div>
                <div class="metric-label">On-Time Delivery</div>
                <div class="trend-indicator trend-up mt-2">
                    <i class="fas fa-arrow-up"></i> {{ delivery_analytics.on_time_improvement|floatformat:1 }}%
                </div>
            </div>
            <div class="col-md-3 text-center">
                <div class="metric-value">{{ delivery_analytics.avg_efficiency|floatformat:1 }}%</div>
                <div class="metric-label">Route Efficiency</div>
                <div class="trend-indicator trend-up mt-2">
                    <i class="fas fa-arrow-up"></i> {{ delivery_analytics.efficiency_improvement|floatformat:1 }}%
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-primary text-white mx-auto">
                        <i class="fas fa-route"></i>
                    </div>
                    <h5 class="card-title">Route Performance</h5>
                    <p class="card-text text-muted">
                        Analyze route efficiency, delivery times, and optimization opportunities
                    </p>
                    <a href="{% url 'reports:route_performance' %}" class="btn btn-primary">
                        <i class="fas fa-chart-line"></i> View Report
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-success text-white mx-auto">
                        <i class="fas fa-gas-pump"></i>
                    </div>
                    <h5 class="card-title">Fuel Consumption</h5>
                    <p class="card-text text-muted">
                        Track fuel usage, costs, and consumption patterns across the fleet
                    </p>
                    <a href="{% url 'reports:fuel_consumption' %}" class="btn btn-success">
                        <i class="fas fa-chart-pie"></i> View Report
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-info text-white mx-auto">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h5 class="card-title">Fleet Utilization</h5>
                    <p class="card-text text-muted">
                        Monitor truck usage, maintenance schedules, and fleet efficiency
                    </p>
                    <a href="#" class="btn btn-info">
                        <i class="fas fa-chart-bar"></i> View Report
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-warning text-white mx-auto">
                        <i class="fas fa-building"></i>
                    </div>
                    <h5 class="card-title">Station Analytics</h5>
                    <p class="card-text text-muted">
                        Analyze station performance, demand patterns, and inventory levels
                    </p>
                    <a href="#" class="btn btn-warning">
                        <i class="fas fa-chart-area"></i> View Report
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-danger text-white mx-auto">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h5 class="card-title">Financial Reports</h5>
                    <p class="card-text text-muted">
                        Revenue analysis, cost breakdowns, and profitability metrics
                    </p>
                    <a href="#" class="btn btn-danger">
                        <i class="fas fa-chart-line"></i> View Report
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card report-card h-100">
                <div class="card-body text-center">
                    <div class="report-icon bg-secondary text-white mx-auto">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="card-title">Driver Performance</h5>
                    <p class="card-text text-muted">
                        Driver efficiency, safety records, and performance metrics
                    </p>
                    <a href="#" class="btn btn-secondary">
                        <i class="fas fa-chart-bar"></i> View Report
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Charts -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Delivery Trends (Last 30 Days)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="deliveryTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Product Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="productDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Station and Fleet Analytics -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-gas-pump"></i> Top Performing Stations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Station</th>
                                    <th>Deliveries</th>
                                    <th>Volume (L)</th>
                                    <th>Efficiency</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for station in station_analytics %}
                                <tr>
                                    <td>
                                        <strong>{{ station.name }}</strong><br>
                                        <small class="text-muted">{{ station.code }}</small>
                                    </td>
                                    <td>{{ station.total_deliveries }}</td>
                                    <td>{{ station.total_volume|floatformat:0 }}</td>
                                    <td>
                                        <span class="badge bg-{{ station.efficiency_color }}">
                                            {{ station.efficiency|floatformat:1 }}%
                                        </span>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck"></i> Fleet Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Truck</th>
                                    <th>Routes</th>
                                    <th>Distance (km)</th>
                                    <th>Utilization</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for truck in fleet_analytics %}
                                <tr>
                                    <td>
                                        <strong>{{ truck.name }}</strong><br>
                                        <small class="text-muted">{{ truck.plate_number }}</small>
                                    </td>
                                    <td>{{ truck.total_routes }}</td>
                                    <td>{{ truck.total_distance|floatformat:0 }}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-{{ truck.utilization_color }}" 
                                                 style="width: {{ truck.utilization_percentage }}%">
                                                {{ truck.utilization_percentage|floatformat:0 }}%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-download"></i> Export Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Export detailed reports for further analysis</p>
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-success w-100" onclick="exportReport('excel')">
                                <i class="fas fa-file-excel"></i> Export to Excel
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-danger w-100" onclick="exportReport('pdf')">
                                <i class="fas fa-file-pdf"></i> Export to PDF
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-info w-100" onclick="exportReport('csv')">
                                <i class="fas fa-file-csv"></i> Export to CSV
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button class="btn btn-outline-secondary w-100" onclick="scheduleReport()">
                                <i class="fas fa-clock"></i> Schedule Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Delivery Trends Chart
const deliveryCtx = document.getElementById('deliveryTrendsChart').getContext('2d');
const deliveryChart = new Chart(deliveryCtx, {
    type: 'line',
    data: {
        labels: {{ delivery_chart_labels|safe }},
        datasets: [{
            label: 'Daily Deliveries',
            data: {{ delivery_chart_data|safe }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// Product Distribution Chart
const productCtx = document.getElementById('productDistributionChart').getContext('2d');
const productChart = new Chart(productCtx, {
    type: 'doughnut',
    data: {
        labels: {{ product_chart_labels|safe }},
        datasets: [{
            data: {{ product_chart_data|safe }},
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d',
                '#17a2b8'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Quick date range functions
function setQuickRange(range) {
    const endDate = new Date();
    let startDate = new Date();
    
    switch(range) {
        case 'today':
            startDate = new Date();
            break;
        case 'week':
            startDate.setDate(endDate.getDate() - 7);
            break;
        case 'month':
            startDate.setMonth(endDate.getMonth() - 1);
            break;
    }
    
    document.querySelector('input[name="start_date"]').value = startDate.toISOString().split('T')[0];
    document.querySelector('input[name="end_date"]').value = endDate.toISOString().split('T')[0];
    
    // Submit form
    document.querySelector('form').submit();
}

// Export functions
function exportReport(format) {
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;
    
    const url = `/reports/export/${format}/?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

function scheduleReport() {
    alert('Scheduled reports feature coming soon!');
}
</script>
{% endblock %}
