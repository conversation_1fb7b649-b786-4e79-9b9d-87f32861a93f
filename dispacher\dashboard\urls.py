# dashboard/urls.py - URL configuration for dashboard module

from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # Main dashboard
    path('', views.home_dashboard, name='home'),
    
    # Overview pages
    path('stations/', views.station_overview, name='station_overview'),
    path('fleet/', views.fleet_overview, name='fleet_overview'),
    path('orders/', views.order_management, name='order_management'),
    path('analytics/', views.analytics_dashboard, name='analytics'),
    
    # API endpoints for real-time data
    path('api/station/<int:station_id>/status/', views.api_station_status, name='api_station_status'),
]
