<!-- templates/reports/route_performance.html - Route Performance Report -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .performance-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .performance-card:hover {
        transform: translateY(-2px);
    }
    .metric-box {
        text-align: center;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    .metric-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
    .route-row {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 0;
    }
    .route-row:last-child {
        border-bottom: none;
    }
    .efficiency-bar {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .efficiency-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
    .chart-container {
        position: relative;
        height: 300px;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-route text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">Reports</a></li>
            <li class="breadcrumb-item active">Route Performance</li>
        </ol>
    </nav>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label class="form-label">Start Date</label>
                <input type="date" name="start_date" class="form-control" 
                       value="{{ start_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">End Date</label>
                <input type="date" name="end_date" class="form-control" 
                       value="{{ end_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-3">
                <label class="form-label">Route</label>
                <select name="route_filter" class="form-select">
                    <option value="">All Routes</option>
                    {% for route in available_routes %}
                        <option value="{{ route.id }}" {% if route_filter == route.id|stringformat:"s" %}selected{% endif %}>
                            {{ route.emri }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i> Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Performance Overview -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-box bg-primary text-white">
                <div class="metric-value">{{ performance_stats.total_routes }}</div>
                <div class="metric-label">Total Routes</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-box bg-success text-white">
                <div class="metric-value">{{ performance_stats.avg_efficiency|floatformat:1 }}%</div>
                <div class="metric-label">Average Efficiency</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-box bg-info text-white">
                <div class="metric-value">{{ performance_stats.avg_completion_time|floatformat:1 }}h</div>
                <div class="metric-label">Avg. Completion Time</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-box bg-warning text-white">
                <div class="metric-value">{{ performance_stats.on_time_percentage|floatformat:1 }}%</div>
                <div class="metric-label">On-Time Delivery</div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card performance-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Route Efficiency Trends
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="efficiencyTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card performance-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Completion Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="completionStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Route Performance -->
    <div class="row">
        <div class="col-12">
            <div class="card performance-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list"></i> Route Performance Details
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="exportRouteData('excel')">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                        <button class="btn btn-outline-danger" onclick="exportRouteData('pdf')">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% for route in routes %}
                    <div class="route-row">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">{{ route.emri }}</h6>
                                <small class="text-muted">
                                    {{ route.data_krijimit|date:"M d, Y" }}
                                    {% if route.kamion %}
                                        | {{ route.kamion.emri }}
                                    {% endif %}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="metric-value text-primary">{{ route.total_stops }}</div>
                                    <div class="metric-label">Stops</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="metric-value text-success">{{ route.completed_stops }}</div>
                                    <div class="metric-label">Completed</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="metric-value text-info">{{ route.total_distance_km|floatformat:1 }}</div>
                                    <div class="metric-label">Distance (km)</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="metric-value text-warning">{{ route.completion_time_hours|floatformat:1 }}</div>
                                    <div class="metric-label">Time (h)</div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <span class="badge bg-{{ route.status_color }} fs-6">
                                    {{ route.get_statusi_display }}
                                </span>
                            </div>
                        </div>
                        
                        <!-- Efficiency Bar -->
                        <div class="row mt-2">
                            <div class="col-md-8">
                                <small class="text-muted">Route Efficiency:</small>
                                <div class="efficiency-bar">
                                    <div class="efficiency-fill bg-{{ route.efficiency_color }}" 
                                         style="width: {{ route.efficiency_percentage }}%"></div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <small class="text-muted">{{ route.efficiency_percentage|floatformat:1 }}% efficient</small>
                            </div>
                        </div>

                        <!-- Route Details -->
                        {% if route.performance_notes %}
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note"></i> {{ route.performance_notes }}
                                </small>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        <div class="row mt-2">
                            <div class="col-12 text-end">
                                <a href="#" class="btn btn-sm btn-outline-primary" 
                                   onclick="viewRouteDetails({{ route.id }})">
                                    <i class="fas fa-eye"></i> View Details
                                </a>
                                {% if route.statusi == 'completed' %}
                                    <a href="#" class="btn btn-sm btn-outline-success" 
                                       onclick="analyzeRoute({{ route.id }})">
                                        <i class="fas fa-chart-line"></i> Analyze
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-5">
                        <i class="fas fa-route fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No routes found</h5>
                        <p class="text-muted">Try adjusting your filter criteria.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="row mt-4">
        <div class="col-lg-6">
            <div class="card performance-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb"></i> Performance Insights
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for insight in performance_insights %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-{{ insight.icon }} text-{{ insight.color }} me-3"></i>
                                <div>
                                    <h6 class="mb-1">{{ insight.title }}</h6>
                                    <p class="mb-0 text-muted small">{{ insight.description }}</p>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-3">
                            <p class="text-muted mb-0">No insights available for the selected period.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card performance-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Optimization Opportunities
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for opportunity in optimization_opportunities %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-{{ opportunity.priority_color }} me-3">
                                    {{ opportunity.priority|upper }}
                                </span>
                                <div>
                                    <h6 class="mb-1">{{ opportunity.title }}</h6>
                                    <p class="mb-0 text-muted small">{{ opportunity.description }}</p>
                                    <small class="text-success">
                                        <i class="fas fa-arrow-up"></i> Potential {{ opportunity.improvement }}% improvement
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-3">
                            <p class="text-muted mb-0">No optimization opportunities identified.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Route Details Modal -->
<div class="modal fade" id="routeDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Route Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="routeDetailsContent">
                <!-- Content loaded via AJAX -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Efficiency Trends Chart
const efficiencyCtx = document.getElementById('efficiencyTrendsChart').getContext('2d');
const efficiencyChart = new Chart(efficiencyCtx, {
    type: 'line',
    data: {
        labels: {{ efficiency_chart_labels|safe }},
        datasets: [{
            label: 'Route Efficiency (%)',
            data: {{ efficiency_chart_data|safe }},
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// Completion Status Chart
const statusCtx = document.getElementById('completionStatusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'In Progress', 'Pending', 'Cancelled'],
        datasets: [{
            data: {{ completion_status_data|safe }},
            backgroundColor: [
                '#28a745',
                '#007bff',
                '#ffc107',
                '#dc3545'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Export functions
function exportRouteData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

// Route details modal
function viewRouteDetails(routeId) {
    fetch(`/api/routes/${routeId}/details/`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('routeDetailsContent').innerHTML = data.html;
            new bootstrap.Modal(document.getElementById('routeDetailsModal')).show();
        })
        .catch(error => {
            console.error('Error loading route details:', error);
            alert('Error loading route details');
        });
}

function analyzeRoute(routeId) {
    // Redirect to detailed route analysis page
    window.location.href = `/reports/routes/${routeId}/analysis/`;
}
</script>
{% endblock %}
