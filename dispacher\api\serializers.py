# api/serializers.py - Django REST Framework serializers

from rest_framework import serializers
from rest_framework_gis.serializers import GeoFeatureModelSerializer
from logistics.models import (
    Produkt, Stacion, Depozite, Kamion, Particion, 
    Porosi, PlanRruge, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
)


class ProduktSerializer(serializers.ModelSerializer):
    """Serializer for Product model"""
    
    class Meta:
        model = Produkt
        fields = ['id', 'emri', 'densiteti', 'ngjyra_kodi', 'eshte_aktiv', 'data_krijimit']
        read_only_fields = ['id', 'data_krijimit']


class DepoziteSerializer(serializers.ModelSerializer):
    """Serializer for Tank model"""
    
    produkt = ProduktSerializer(read_only=True)
    produkt_id = serializers.IntegerField(write_only=True)
    perqindja_mbushjes = serializers.ReadOnlyField()
    nevojitet_rifornizim = serializers.ReadOnlyField()
    eshte_kritik = serializers.ReadOnlyField()
    dite_deri_zbrazje = serializers.ReadOnlyField()
    kapaciteti_i_disponueshem = serializers.ReadOnlyField()
    
    class Meta:
        model = Depozite
        fields = [
            'id', 'stacion', 'produkt', 'produkt_id', 'numri_tankut',
            'kapaciteti_total', 'sasia_aktuale', 'niveli_minimal_sigurise',
            'niveli_i_porosise', 'konsumi_mesatar_ditor', 'sasia_minimale_dorezimi',
            'perqindja_maksimale_mbushjes', 'data_dorezimit_fundit', 'data_perditesimit',
            'perqindja_mbushjes', 'nevojitet_rifornizim', 'eshte_kritik', 
            'dite_deri_zbrazje', 'kapaciteti_i_disponueshem'
        ]
        read_only_fields = ['id', 'data_perditesimit']


class StacionSerializer(GeoFeatureModelSerializer):
    """GeoJSON serializer for Station model"""
    
    depozitat = DepoziteSerializer(many=True, read_only=True)
    total_tanks = serializers.SerializerMethodField()
    critical_tanks = serializers.SerializerMethodField()
    
    class Meta:
        model = Stacion
        geo_field = 'vendndodhja'
        fields = [
            'id', 'emri', 'kodi', 'adresa', 'orar_pranimi_nga', 'orar_pranimi_deri',
            'kerkon_pompe', 'kerkon_kontaliter', 'max_kamione_njekohesisht',
            'koha_mesatare_shkarkimi', 'max_pesha_kamioni_ton', 'max_gjatesia_kamioni_m',
            'menaxher_emri', 'telefoni', 'email', 'eshte_aktiv', 'data_krijimit',
            'depozitat', 'total_tanks', 'critical_tanks'
        ]
        read_only_fields = ['id', 'data_krijimit', 'data_perditesimit']
    
    def get_total_tanks(self, obj):
        return obj.depozitat.count()
    
    def get_critical_tanks(self, obj):
        return obj.depozitat.filter(
            sasia_aktuale__lte=models.F('niveli_minimal_sigurise')
        ).count()


class ShoferSerializer(serializers.ModelSerializer):
    """Serializer for Driver model"""
    
    emri_i_plote = serializers.ReadOnlyField()
    
    class Meta:
        model = Shofer
        fields = [
            'id', 'emri', 'mbiemri', 'emri_i_plote', 'telefoni', 'email',
            'leje_drejtimi_numri', 'leje_drejtimi_skadon', 'leje_adr',
            'ore_punes_maksimale_ditor', 'ore_drejtimi_maksimale_ditor',
            'eshte_aktiv', 'data_punesimit'
        ]
        read_only_fields = ['id', 'emri_i_plote']


class ParticionSerializer(serializers.ModelSerializer):
    """Serializer for Compartment model"""
    
    kapaciteti_i_disponueshem = serializers.ReadOnlyField()
    eshte_bosh = serializers.ReadOnlyField()
    produkt_i_dedikuar = ProduktSerializer(read_only=True)
    produkti_aktual = ProduktSerializer(read_only=True)
    produkti_i_fundit = ProduktSerializer(read_only=True)
    
    class Meta:
        model = Particion
        fields = [
            'id', 'kamion', 'numri_i_dhomes', 'kapaciteti',
            'produkt_i_dedikuar', 'produkti_aktual', 'produkti_i_fundit',
            'sasia_heel_litra', 'eshte_i_pastruar', 'data_pastrimit',
            'kerkon_pastrimin', 'sasia_aktuale', 'kapaciteti_i_disponueshem', 'eshte_bosh'
        ]
        read_only_fields = ['id', 'kapaciteti_i_disponueshem', 'eshte_bosh']


class KamionSerializer(serializers.ModelSerializer):
    """Serializer for Truck model"""
    
    shofer_aktual = ShoferSerializer(read_only=True)
    particionet = ParticionSerializer(many=True, read_only=True)
    kapaciteti_total_litra = serializers.ReadOnlyField()
    kapaciteti_i_disponueshem_litra = serializers.ReadOnlyField()
    nevojitet_mirembajtje = serializers.ReadOnlyField()
    
    class Meta:
        model = Kamion
        fields = [
            'id', 'targa', 'modeli', 'viti_prodhimit', 'shofer_aktual',
            'pesha_maksimale_bruto_ton', 'gjatesia_totale_m', 'eshte_trailer',
            'ka_pompe', 'ka_kontaliter', 'ka_gps', 'statusi', 'vendndodhja_aktuale',
            'data_perditesimit_gps', 'data_mirembajtjes_fundit', 'km_mirembajtjes_rradheses',
            'odometri_aktual_km', 'konsumi_mesatar_l_100km', 'eshte_aktiv',
            'particionet', 'kapaciteti_total_litra', 'kapaciteti_i_disponueshem_litra',
            'nevojitet_mirembajtje'
        ]
        read_only_fields = [
            'id', 'kapaciteti_total_litra', 'kapaciteti_i_disponueshem_litra',
            'nevojitet_mirembajtje', 'data_krijimit'
        ]


class PorosiSerializer(serializers.ModelSerializer):
    """Serializer for Order model"""
    
    stacion = StacionSerializer(read_only=True)
    stacion_id = serializers.IntegerField(write_only=True)
    produkt = ProduktSerializer(read_only=True)
    produkt_id = serializers.IntegerField(write_only=True)
    depozite = DepoziteSerializer(read_only=True)
    depozite_id = serializers.IntegerField(write_only=True)
    eshte_urgjente = serializers.ReadOnlyField()
    
    class Meta:
        model = Porosi
        fields = [
            'id', 'numri_porosise', 'stacion', 'stacion_id', 'depozite', 'depozite_id',
            'produkt', 'produkt_id', 'sasia_e_kerkuar', 'sasia_e_miratuar',
            'sasia_e_dorezuar', 'prioriteti', 'data_afati', 'koha_preferuar_fillimi',
            'koha_preferuar_mbarimi', 'statusi', 'eshte_automatike', 'eshte_emergjente',
            'shenimet', 'krijuar_nga', 'data_krijimit', 'data_perditesimit', 'eshte_urgjente'
        ]
        read_only_fields = ['id', 'numri_porosise', 'eshte_urgjente', 'data_krijimit', 'data_perditesimit']


class NgarkeseShkarkeseSerializer(serializers.ModelSerializer):
    """Serializer for Loading/Unloading operations"""
    
    porosi = PorosiSerializer(read_only=True)
    particion = ParticionSerializer(read_only=True)
    produkt = ProduktSerializer(read_only=True)
    depozite = DepoziteSerializer(read_only=True)
    variacion_sasia = serializers.ReadOnlyField()
    variacion_perqindja = serializers.ReadOnlyField()
    
    class Meta:
        model = NgarkeseShkarkese
        fields = [
            'id', 'ndalese', 'porosi', 'particion', 'produkt', 'depozite',
            'sasia', 'sasia_e_dorezuar', 'temperatura_celsius', 'densiteti_i_matur',
            'leximi_fillimi_kontaliter', 'leximi_mbarimi_kontaliter',
            'koha_fillimi_dorezimit', 'koha_mbarimi_dorezimit', 'eshte_perfunduar',
            'ka_variacion', 'arsyeja_variacionit', 'variacion_sasia', 'variacion_perqindja'
        ]
        read_only_fields = ['id', 'variacion_sasia', 'variacion_perqindja']


class NdaleseSerializer(serializers.ModelSerializer):
    """Serializer for Route stops"""
    
    stacion = StacionSerializer(read_only=True)
    ngarkesat_shkarkeset = NgarkeseShkarkeseSerializer(many=True, read_only=True)
    vonesimi_minuta = serializers.ReadOnlyField()
    sasia_totale_dorezuar = serializers.ReadOnlyField()
    
    class Meta:
        model = Ndalese
        fields = [
            'id', 'plan_rruge', 'stacion', 'depo_qendrore', 'sekuenca',
            'koha_parashikuar_mberritjes', 'koha_e_parashikuar_sherbimit_min',
            'koha_parashikuar_nisjes', 'koha_aktuale_mberritjes', 'koha_aktuale_nisjes',
            'eshte_perfunduar', 'problemet_dorezimit', 'nenshkrimi_marresi',
            'vendndodhja_gps', 'ngarkesat_shkarkeset', 'vonesimi_minuta', 'sasia_totale_dorezuar'
        ]
        read_only_fields = ['id', 'vonesimi_minuta', 'sasia_totale_dorezuar']


class PlanRrugeSerializer(serializers.ModelSerializer):
    """Serializer for Route plans"""
    
    kamion = KamionSerializer(read_only=True)
    shofer = ShoferSerializer(read_only=True)
    ndalesat = NdaleseSerializer(many=True, read_only=True)
    pikuesi_efikasitetit = serializers.ReadOnlyField()
    sasia_totale_dorezuar = serializers.ReadOnlyField()
    
    class Meta:
        model = PlanRruge
        fields = [
            'id', 'numri_rrugese', 'data_planifikimit', 'kamion', 'shofer',
            'koha_nisjes_nga_depo', 'distanca_e_planifikuar_km', 'kohezgjatja_e_planifikuar_ore',
            'kostoja_e_parashikuar', 'distanca_aktuale_km', 'kohezgjatja_aktuale_ore',
            'kostoja_aktuale', 'koha_aktuale_nisjes', 'koha_aktuale_mbarimit',
            'statusi', 'pikuesi_optimizimit', 'shenimet', 'krijuar_nga',
            'data_krijimit', 'data_perditesimit', 'ndalesat', 'pikuesi_efikasitetit',
            'sasia_totale_dorezuar'
        ]
        read_only_fields = [
            'id', 'numri_rrugese', 'pikuesi_efikasitetit', 'sasia_totale_dorezuar',
            'data_krijimit', 'data_perditesimit'
        ]


# Simplified serializers for mobile app
class MobileStacionSerializer(serializers.ModelSerializer):
    """Simplified station serializer for mobile app"""
    
    class Meta:
        model = Stacion
        fields = ['id', 'emri', 'kodi', 'adresa', 'telefoni']


class MobileNdaleseSerializer(serializers.ModelSerializer):
    """Simplified stop serializer for mobile app"""
    
    stacion = MobileStacionSerializer(read_only=True)
    
    class Meta:
        model = Ndalese
        fields = [
            'id', 'sekuenca', 'stacion', 'koha_parashikuar_mberritjes',
            'koha_parashikuar_nisjes', 'eshte_perfunduar'
        ]


class MobilePlanRrugeSerializer(serializers.ModelSerializer):
    """Simplified route serializer for mobile app"""
    
    ndalesat = MobileNdaleseSerializer(many=True, read_only=True)
    
    class Meta:
        model = PlanRruge
        fields = [
            'id', 'numri_rrugese', 'data_planifikimit', 'statusi',
            'koha_nisjes_nga_depo', 'ndalesat'
        ]
