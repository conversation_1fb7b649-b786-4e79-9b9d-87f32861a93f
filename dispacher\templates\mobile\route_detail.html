<!-- templates/mobile/route_detail.html - Mobile Route Detail View -->
{% extends 'mobile/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .route-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 1rem;
        margin: -1rem -1rem 1rem -1rem;
        border-radius: 0 0 1rem 1rem;
    }
    .progress-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: conic-gradient(#28a745 0deg {{ progress_angle }}deg, #e9ecef {{ progress_angle }}deg 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }
    .progress-circle::before {
        content: '';
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }
    .progress-text {
        position: relative;
        z-index: 1;
        font-weight: bold;
        font-size: 0.8rem;
    }
    .stop-card {
        border-left: 4px solid #dee2e6;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
    }
    .stop-card.completed {
        border-left-color: #28a745;
        background-color: #f8f9fa;
    }
    .stop-card.current {
        border-left-color: #007bff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
    }
    .stop-card.pending {
        border-left-color: #6c757d;
    }
    .action-buttons {
        position: fixed;
        bottom: 1rem;
        left: 1rem;
        right: 1rem;
        z-index: 1000;
    }
    .floating-btn {
        border-radius: 50px;
        padding: 0.75rem 1.5rem;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .eta-badge {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 0.25rem 0.75rem;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-3">
    <!-- Route Header -->
    <div class="route-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-1">{{ route.emri }}</h5>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-truck"></i> {{ route.kamion.emri }}
                    {% if route.kamion.numri_targave %}
                        ({{ route.kamion.numri_targave }})
                    {% endif %}
                </p>
            </div>
            <div class="progress-circle">
                <div class="progress-text">{{ completed_stops }}/{{ total_stops }}</div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-4 text-center">
                <div class="eta-badge">
                    <i class="fas fa-clock"></i><br>
                    <small>ETA</small><br>
                    <strong>{{ route.estimated_completion|date:"H:i" }}</strong>
                </div>
            </div>
            <div class="col-4 text-center">
                <div class="eta-badge">
                    <i class="fas fa-route"></i><br>
                    <small>Distance</small><br>
                    <strong>{{ route.total_distance_km|floatformat:1 }}km</strong>
                </div>
            </div>
            <div class="col-4 text-center">
                <div class="eta-badge">
                    <i class="fas fa-gas-pump"></i><br>
                    <small>Fuel</small><br>
                    <strong>{{ route.total_fuel_liters|floatformat:0 }}L</strong>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Stop Alert -->
    {% if current_stop %}
    <div class="alert alert-primary d-flex align-items-center mb-3">
        <i class="fas fa-map-marker-alt fa-2x me-3"></i>
        <div>
            <strong>Current Stop:</strong><br>
            {{ current_stop.stacion.emri }}<br>
            <small class="text-muted">{{ current_stop.stacion.adresa }}</small>
        </div>
        <div class="ms-auto">
            <a href="{% url 'mobile:stop_detail' current_stop.id %}" class="btn btn-sm btn-light">
                <i class="fas fa-arrow-right"></i>
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Route Stops -->
    <div class="stops-list">
        <h6 class="mb-3">
            <i class="fas fa-list"></i> Route Stops
            <span class="badge bg-secondary ms-2">{{ total_stops }}</span>
        </h6>

        {% for stop in route.ndaleset.all %}
        <div class="card stop-card 
            {% if stop.statusi == 'completed' %}completed
            {% elif stop == current_stop %}current
            {% else %}pending{% endif %}">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge bg-secondary me-2">{{ forloop.counter }}</span>
                            <h6 class="mb-0">{{ stop.stacion.emri }}</h6>
                            {% if stop.statusi == 'completed' %}
                                <i class="fas fa-check-circle text-success ms-2"></i>
                            {% elif stop == current_stop %}
                                <i class="fas fa-map-marker-alt text-primary ms-2"></i>
                            {% endif %}
                        </div>
                        
                        <p class="text-muted small mb-2">
                            <i class="fas fa-map-marker-alt"></i> {{ stop.stacion.adresa|truncatechars:40 }}
                        </p>

                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Product:</small><br>
                                <strong>{{ stop.produkt.emri }}</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Quantity:</small><br>
                                <strong>{{ stop.sasia_litra|floatformat:0 }}L</strong>
                            </div>
                        </div>

                        {% if stop.koha_e_planifikuar %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> 
                                Planned: {{ stop.koha_e_planifikuar|date:"H:i" }}
                                {% if stop.koha_e_perfundimit %}
                                    | Completed: {{ stop.koha_e_perfundimit|date:"H:i" }}
                                {% endif %}
                            </small>
                        </div>
                        {% endif %}

                        {% if stop.shenim %}
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-sticky-note"></i> {{ stop.shenim }}
                            </small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="ms-3">
                        {% if stop.statusi == 'completed' %}
                            <span class="badge bg-success">Completed</span>
                        {% elif stop == current_stop %}
                            <a href="{% url 'mobile:stop_detail' stop.id %}" class="btn btn-sm btn-primary">
                                <i class="fas fa-play"></i> Start
                            </a>
                        {% else %}
                            <span class="badge bg-secondary">Pending</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="text-center py-4">
            <i class="fas fa-map-marked-alt fa-3x text-muted mb-3"></i>
            <p class="text-muted">No stops in this route</p>
        </div>
        {% endfor %}
    </div>

    <!-- Spacer for fixed buttons -->
    <div style="height: 100px;"></div>
</div>

<!-- Fixed Action Buttons -->
<div class="action-buttons">
    {% if current_stop %}
        <div class="d-grid gap-2">
            <a href="{% url 'mobile:stop_detail' current_stop.id %}" 
               class="btn btn-primary floating-btn">
                <i class="fas fa-play"></i> Continue to {{ current_stop.stacion.emri }}
            </a>
        </div>
    {% elif route.statusi == 'completed' %}
        <div class="d-grid gap-2">
            <button class="btn btn-success floating-btn" disabled>
                <i class="fas fa-check-circle"></i> Route Completed
            </button>
        </div>
    {% else %}
        <div class="d-grid gap-2">
            <button class="btn btn-outline-secondary floating-btn" disabled>
                <i class="fas fa-clock"></i> Waiting for Assignment
            </button>
        </div>
    {% endif %}
</div>

<!-- Navigation Modal -->
<div class="modal fade" id="navigationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Navigation Options</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <a href="https://maps.google.com/?q={{ current_stop.stacion.latitude }},{{ current_stop.stacion.longitude }}" 
                       class="btn btn-outline-primary" target="_blank">
                        <i class="fab fa-google"></i> Open in Google Maps
                    </a>
                    <a href="https://waze.com/ul?ll={{ current_stop.stacion.latitude }},{{ current_stop.stacion.longitude }}" 
                       class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-route"></i> Open in Waze
                    </a>
                    <button class="btn btn-outline-secondary" onclick="shareLocation()">
                        <i class="fas fa-share"></i> Share Location
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Calculate progress angle for circular progress
const progressPercentage = {{ completed_stops }} / {{ total_stops }} * 100;
const progressAngle = progressPercentage * 3.6; // Convert to degrees
document.documentElement.style.setProperty('--progress-angle', progressAngle + 'deg');

// Auto-refresh every 30 seconds
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);

// Share location function
function shareLocation() {
    if (navigator.share) {
        navigator.share({
            title: 'Current Route Location',
            text: 'My current location on route {{ route.emri }}',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('Route link copied to clipboard!');
        });
    }
}

// Geolocation tracking
if (navigator.geolocation) {
    navigator.geolocation.watchPosition(function(position) {
        // Send location updates to server
        const data = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            route_id: {{ route.id }},
            timestamp: new Date().toISOString()
        };
        
        // This would typically send to an API endpoint
        console.log('Location update:', data);
    }, function(error) {
        console.log('Geolocation error:', error);
    }, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
    });
}
</script>
{% endblock %}
