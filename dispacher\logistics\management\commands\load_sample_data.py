# logistics/management/commands/load_sample_data.py
"""
Management command to load comprehensive sample data for OptiKarburant system
Focused on Albanian fuel distribution network with realistic data
"""

from django.core.management.base import BaseCommand
from django.contrib.gis.geos import Point
from django.utils import timezone
from datetime import time, timedelta
import random

from logistics.models import (
    Produkt, DepoQendrore, Stacion, Depozite, Shofer,
    Kamion, Particion, Porosi
)


class Command(BaseCommand):
    help = 'Load sample data for OptiKarburant system'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before loading',
        )
    
    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()
        
        self.stdout.write('Loading sample data...')
        
        # Load products
        products = self.create_products()
        self.stdout.write(f'Created {len(products)} products')
        
        # Load central depot
        depot = self.create_central_depot()
        self.stdout.write(f'Created central depot: {depot.emri}')
        
        # Load stations
        stations = self.create_stations()
        self.stdout.write(f'Created {len(stations)} stations')
        
        # Load tanks
        tanks = self.create_tanks(stations, products)
        self.stdout.write(f'Created {len(tanks)} tanks')
        
        # Load drivers
        drivers = self.create_drivers()
        self.stdout.write(f'Created {len(drivers)} drivers')
        
        # Load trucks
        trucks = self.create_trucks(drivers)
        self.stdout.write(f'Created {len(trucks)} trucks')
        
        # Load compartments
        compartments = self.create_compartments(trucks, products)
        self.stdout.write(f'Created {len(compartments)} compartments')
        
        # Load sample orders
        orders = self.create_sample_orders(stations, tanks, products)
        self.stdout.write(f'Created {len(orders)} sample orders')
        
        self.stdout.write(
            self.style.SUCCESS('Successfully loaded sample data!')
        )
    
    def clear_data(self):
        """Clear existing data"""
        Porosi.objects.all().delete()
        Particion.objects.all().delete()
        Kamion.objects.all().delete()
        Shofer.objects.all().delete()
        Depozite.objects.all().delete()
        Stacion.objects.all().delete()
        DepoQendrore.objects.all().delete()
        Produkt.objects.all().delete()
    
    def create_products(self):
        """Create sample fuel products"""
        products_data = [
            {'emri': 'Naftë D2', 'densiteti': 0.85, 'ngjyra_kodi': '#2c3e50'},
            {'emri': 'Benzinë 95', 'densiteti': 0.75, 'ngjyra_kodi': '#e74c3c'},
            {'emri': 'Benzinë 100', 'densiteti': 0.75, 'ngjyra_kodi': '#c0392b'},
            {'emri': 'Gaz i Lëngshëm', 'densiteti': 0.51, 'ngjyra_kodi': '#3498db'},
            {'emri': 'Naftë Heating', 'densiteti': 0.87, 'ngjyra_kodi': '#8e44ad'},
        ]
        
        products = []
        for data in products_data:
            product, created = Produkt.objects.get_or_create(
                emri=data['emri'],
                defaults=data
            )
            products.append(product)
        
        # Set product compatibility
        nafte = products[0]  # Naftë D2
        benzine_95 = products[1]  # Benzinë 95
        benzine_100 = products[2]  # Benzinë 100
        
        # Benzines are compatible with each other
        benzine_95.produkte_kompatible.add(benzine_100)
        benzine_100.produkte_kompatible.add(benzine_95)
        
        return products
    
    def create_central_depot(self):
        """Create central depot in Tirana"""
        depot, created = DepoQendrore.objects.get_or_create(
            emri='Depo Qendrore Tiranë',
            defaults={
                'vendndodhja': Point(19.8189, 41.3275),  # Tirana coordinates
                'adresa': 'Rruga Industriale, Tiranë',
                'kapaciteti_ngarkimi': 8,
                'koha_mesatare_ngarkimi': 90,
                'orar_punes_nga': time(6, 0),
                'orar_punes_deri': time(22, 0),
            }
        )
        return depot
    
    def create_stations(self):
        """Create comprehensive fuel stations around Albania"""
        stations_data = [
            # Tirana area - Major distribution hub
            {'emri': 'Stacioni Qender Tiranë', 'kodi': 'TIR001', 'lat': 41.3275, 'lng': 19.8189, 'city': 'Tiranë', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Kombinat', 'kodi': 'TIR002', 'lat': 41.2911, 'lng': 19.8607, 'city': 'Tiranë', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},
            {'emri': 'Stacioni Don Bosko', 'kodi': 'TIR003', 'lat': 41.3151, 'lng': 19.8331, 'city': 'Tiranë', 'type': 'standard', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Kashar', 'kodi': 'TIR004', 'lat': 41.3847, 'lng': 19.7736, 'city': 'Tiranë', 'type': 'highway', 'requires_pump': False, 'requires_meter': True},
            {'emri': 'Stacioni Kamëz', 'kodi': 'TIR005', 'lat': 41.3814, 'lng': 19.7631, 'city': 'Tiranë', 'type': 'standard', 'requires_pump': False, 'requires_meter': False},

            # Durrës - Port city, high volume
            {'emri': 'Stacioni Durrës Port', 'kodi': 'DUR001', 'lat': 41.3147, 'lng': 19.4444, 'city': 'Durrës', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Durrës Qender', 'kodi': 'DUR002', 'lat': 41.3236, 'lng': 19.4581, 'city': 'Durrës', 'type': 'standard', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Durrës Plazh', 'kodi': 'DUR003', 'lat': 41.3200, 'lng': 19.4300, 'city': 'Durrës', 'type': 'seasonal', 'requires_pump': False, 'requires_meter': True},

            # Shkodër - Northern region
            {'emri': 'Stacioni Shkodër Qender', 'kodi': 'SHK001', 'lat': 42.0683, 'lng': 19.5122, 'city': 'Shkodër', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Shkodër Veri', 'kodi': 'SHK002', 'lat': 42.0750, 'lng': 19.5200, 'city': 'Shkodër', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Elbasan - Central Albania
            {'emri': 'Stacioni Elbasan Qender', 'kodi': 'ELB001', 'lat': 41.1125, 'lng': 20.0822, 'city': 'Elbasan', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Elbasan Jug', 'kodi': 'ELB002', 'lat': 41.1000, 'lng': 20.0900, 'city': 'Elbasan', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Vlorë - Southern coast
            {'emri': 'Stacioni Vlorë Port', 'kodi': 'VLO001', 'lat': 40.4686, 'lng': 19.4889, 'city': 'Vlorë', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Vlorë Qender', 'kodi': 'VLO002', 'lat': 40.4650, 'lng': 19.4950, 'city': 'Vlorë', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},
            {'emri': 'Stacioni Orikum', 'kodi': 'VLO003', 'lat': 40.3167, 'lng': 19.4333, 'city': 'Vlorë', 'type': 'seasonal', 'requires_pump': False, 'requires_meter': False},

            # Korçë - Eastern region
            {'emri': 'Stacioni Korçë Qender', 'kodi': 'KOR001', 'lat': 40.6186, 'lng': 20.7719, 'city': 'Korçë', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Korçë Veri', 'kodi': 'KOR002', 'lat': 40.6250, 'lng': 20.7800, 'city': 'Korçë', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Fier - Agricultural region
            {'emri': 'Stacioni Fier Qender', 'kodi': 'FIE001', 'lat': 40.7239, 'lng': 19.5556, 'city': 'Fier', 'type': 'major', 'requires_pump': True, 'requires_meter': True},
            {'emri': 'Stacioni Fier Industrial', 'kodi': 'FIE002', 'lat': 40.7300, 'lng': 19.5600, 'city': 'Fier', 'type': 'industrial', 'requires_pump': True, 'requires_meter': True},

            # Berat - Historic city
            {'emri': 'Stacioni Berat', 'kodi': 'BER001', 'lat': 40.7058, 'lng': 19.9522, 'city': 'Berat', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Gjirokastër - Southern region
            {'emri': 'Stacioni Gjirokastër', 'kodi': 'GJI001', 'lat': 40.0758, 'lng': 20.1389, 'city': 'Gjirokastër', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Lushnjë - Agricultural hub
            {'emri': 'Stacioni Lushnjë', 'kodi': 'LUS001', 'lat': 40.9419, 'lng': 19.7050, 'city': 'Lushnjë', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Pogradec - Tourist area
            {'emri': 'Stacioni Pogradec', 'kodi': 'POG001', 'lat': 40.9022, 'lng': 20.6528, 'city': 'Pogradec', 'type': 'seasonal', 'requires_pump': False, 'requires_meter': False},

            # Kukës - Northern mountains
            {'emri': 'Stacioni Kukës', 'kodi': 'KUK001', 'lat': 42.0772, 'lng': 20.4214, 'city': 'Kukës', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},

            # Lezhë - Coastal north
            {'emri': 'Stacioni Lezhë', 'kodi': 'LEZ001', 'lat': 41.7836, 'lng': 19.6439, 'city': 'Lezhë', 'type': 'standard', 'requires_pump': False, 'requires_meter': True},
        ]
        
        stations = []
        for data in stations_data:
            # Set operating hours based on station type
            if data['type'] == 'major':
                start_time, end_time = time(5, 0), time(20, 0)
                max_trucks = 2
                unload_time = 60
            elif data['type'] == 'highway':
                start_time, end_time = time(0, 0), time(23, 59)  # 24/7
                max_trucks = 3
                unload_time = 30
            elif data['type'] == 'industrial':
                start_time, end_time = time(6, 0), time(18, 0)
                max_trucks = 2
                unload_time = 90
            elif data['type'] == 'seasonal':
                start_time, end_time = time(7, 0), time(19, 0)
                max_trucks = 1
                unload_time = 45
            else:  # standard
                start_time, end_time = time(6, 0), time(18, 0)
                max_trucks = 1
                unload_time = 45

            station, created = Stacion.objects.get_or_create(
                kodi=data['kodi'],
                defaults={
                    'emri': data['emri'],
                    'vendndodhja': Point(data['lng'], data['lat']),
                    'adresa': f"{data['emri']}, {data['city']}, Shqipëri",
                    'orar_pranimi_nga': start_time,
                    'orar_pranimi_deri': end_time,
                    'kerkon_pompe': data['requires_pump'],
                    'kerkon_kontaliter': data['requires_meter'],
                    'max_kamione_njekohesisht': max_trucks,
                    'koha_mesatare_shkarkimi': unload_time,
                    'max_pesha_kamioni_ton': 40.0 if data['type'] != 'seasonal' else 25.0,
                    'max_gjatesia_kamioni_m': 16.5 if data['type'] != 'seasonal' else 12.0,
                    'menaxher_emri': f"Menaxher {data['city']}",
                    'telefoni': f"+35569{random.randint(1000000, 9999999)}",
                }
            )
            stations.append(station)

        return stations
    
    def create_tanks(self, stations, products):
        """Create realistic tanks for stations based on their type and location"""
        tanks = []

        # Define tank configurations by station type
        tank_configs = {
            'major': {
                'products': products[:4],  # All main products
                'base_capacity': 30000,
                'capacity_variation': [0, 5000, 10000, -5000],  # Different sizes
                'consumption_base': 1500,
                'consumption_variation': [0, 300, 500, 200]
            },
            'highway': {
                'products': products[:3],  # Diesel, Gasoline 95, Gasoline 100
                'base_capacity': 40000,  # Larger for highway traffic
                'capacity_variation': [0, 8000, 5000],
                'consumption_base': 2000,
                'consumption_variation': [0, 400, 300]
            },
            'industrial': {
                'products': [products[0], products[4]],  # Diesel and Heating oil
                'base_capacity': 50000,  # Large industrial tanks
                'capacity_variation': [0, 10000],
                'consumption_base': 2500,
                'consumption_variation': [0, 500]
            },
            'seasonal': {
                'products': products[:2],  # Just Diesel and Gasoline 95
                'base_capacity': 15000,  # Smaller seasonal stations
                'capacity_variation': [0, 3000],
                'consumption_base': 600,
                'consumption_variation': [0, 200]
            },
            'standard': {
                'products': products[:3],  # Diesel, Gasoline 95, Gasoline 100
                'base_capacity': 25000,
                'capacity_variation': [0, 5000, 3000],
                'consumption_base': 1000,
                'consumption_variation': [0, 200, 150]
            }
        }

        for station in stations:
            # Determine station type from the enhanced data
            station_type = 'standard'  # default
            if 'major' in station.emri.lower() or station.kodi.endswith('001'):
                station_type = 'major'
            elif 'highway' in station.emri.lower() or 'kashar' in station.emri.lower():
                station_type = 'highway'
            elif 'industrial' in station.emri.lower():
                station_type = 'industrial'
            elif 'plazh' in station.emri.lower() or 'orikum' in station.emri.lower() or 'pogradec' in station.emri.lower():
                station_type = 'seasonal'

            config = tank_configs[station_type]

            for i, product in enumerate(config['products']):
                capacity = config['base_capacity'] + config['capacity_variation'][i]
                consumption = config['consumption_base'] + config['consumption_variation'][i]

                # Simulate realistic current levels (60-90% full)
                current_level = capacity * random.uniform(0.6, 0.9)

                tank, created = Depozite.objects.get_or_create(
                    stacion=station,
                    produkt=product,
                    numri_tankut=f"T{i+1}",
                    defaults={
                        'kapaciteti_total': capacity,
                        'sasia_aktuale': current_level,
                        'niveli_minimal_sigurise': capacity * 0.1,  # 10% safety stock
                        'niveli_i_porosise': capacity * 0.25,       # Reorder at 25%
                        'konsumi_mesatar_ditor': consumption,
                        'sasia_minimale_dorezimi': min(5000, capacity * 0.2),  # Min 20% or 5000L
                        'perqindja_maksimale_mbushjes': 95,
                        'data_matjes_fundit': timezone.now() - timedelta(hours=random.randint(1, 24)),
                    }
                )
                tanks.append(tank)

        return tanks
    
    def create_drivers(self):
        """Create comprehensive Albanian drivers with realistic details"""
        drivers_data = [
            {'emri': 'Agim', 'mbiemri': 'Kelmendi', 'telefoni': '+355691234567', 'experience': 'senior'},
            {'emri': 'Besnik', 'mbiemri': 'Hoxha', 'telefoni': '+355692345678', 'experience': 'senior'},
            {'emri': 'Driton', 'mbiemri': 'Brahimaj', 'telefoni': '+355693456789', 'experience': 'experienced'},
            {'emri': 'Ermal', 'mbiemri': 'Gjoka', 'telefoni': '+355694567890', 'experience': 'experienced'},
            {'emri': 'Fadil', 'mbiemri': 'Rama', 'telefoni': '+355695678901', 'experience': 'junior'},
            {'emri': 'Genti', 'mbiemri': 'Berisha', 'telefoni': '+355696789012', 'experience': 'senior'},
            {'emri': 'Ilir', 'mbiemri': 'Dervishi', 'telefoni': '+355697890123', 'experience': 'experienced'},
            {'emri': 'Jeton', 'mbiemri': 'Kastrati', 'telefoni': '+355698901234', 'experience': 'junior'},
            {'emri': 'Klajdi', 'mbiemri': 'Mema', 'telefoni': '+355699012345', 'experience': 'experienced'},
            {'emri': 'Luan', 'mbiemri': 'Nallbani', 'telefoni': '+355691123456', 'experience': 'senior'},
            {'emri': 'Mentor', 'mbiemri': 'Osmani', 'telefoni': '+355692234567', 'experience': 'junior'},
            {'emri': 'Nard', 'mbiemri': 'Prifti', 'telefoni': '+355693345678', 'experience': 'experienced'},
        ]
        
        drivers = []
        for i, data in enumerate(drivers_data):
            # Set experience-based parameters
            experience = data['experience']
            if experience == 'senior':
                employment_days = random.randint(1800, 3650)  # 5-10 years
                has_adr = True
                max_work_hours = 10
                max_drive_hours = 8
            elif experience == 'experienced':
                employment_days = random.randint(730, 1800)   # 2-5 years
                has_adr = random.choice([True, False])
                max_work_hours = 9
                max_drive_hours = 7
            else:  # junior
                employment_days = random.randint(90, 730)     # 3 months - 2 years
                has_adr = False
                max_work_hours = 8
                max_drive_hours = 6

            driver, created = Shofer.objects.get_or_create(
                leje_drejtimi_numri=f"AL{2024000 + i:06d}",
                defaults={
                    'emri': data['emri'],
                    'mbiemri': data['mbiemri'],
                    'telefoni': data['telefoni'],
                    'email': f"{data['emri'].lower()}.{data['mbiemri'].lower()}@optikarburant.al",
                    'leje_drejtimi_skadon': timezone.now().date() + timedelta(days=random.randint(180, 1095)),
                    'leje_adr': has_adr,
                    'ore_punes_maksimale_ditor': max_work_hours,
                    'ore_drejtimi_maksimale_ditor': max_drive_hours,
                    'data_punesimit': timezone.now().date() - timedelta(days=employment_days),
                }
            )
            drivers.append(driver)

        return drivers
    
    def create_trucks(self, drivers):
        """Create comprehensive Albanian truck fleet"""
        trucks_data = [
            # Premium trucks with full equipment
            {'targa': 'AA 001 TR', 'modeli': 'Mercedes Actros 2545', 'year': 2022, 'has_pump': True, 'has_meter': True, 'detachable': True, 'type': 'premium'},
            {'targa': 'AA 002 TR', 'modeli': 'Volvo FH 460', 'year': 2021, 'has_pump': True, 'has_meter': True, 'detachable': True, 'type': 'premium'},
            {'targa': 'AA 003 TR', 'modeli': 'Scania R 450', 'year': 2023, 'has_pump': True, 'has_meter': True, 'detachable': True, 'type': 'premium'},

            # Standard trucks with good equipment
            {'targa': 'AA 004 TR', 'modeli': 'MAN TGX 440', 'year': 2020, 'has_pump': True, 'has_meter': True, 'detachable': False, 'type': 'standard'},
            {'targa': 'AA 005 TR', 'modeli': 'DAF XF 440', 'year': 2019, 'has_pump': False, 'has_meter': True, 'detachable': False, 'type': 'standard'},
            {'targa': 'AA 006 TR', 'modeli': 'Iveco Stralis 420', 'year': 2020, 'has_pump': True, 'has_meter': True, 'detachable': False, 'type': 'standard'},

            # Basic trucks for local delivery
            {'targa': 'AA 007 TR', 'modeli': 'Mercedes Atego 1218', 'year': 2018, 'has_pump': False, 'has_meter': True, 'detachable': False, 'type': 'basic'},
            {'targa': 'AA 008 TR', 'modeli': 'Volvo FL 280', 'year': 2017, 'has_pump': False, 'has_meter': False, 'detachable': False, 'type': 'basic'},
            {'targa': 'AA 009 TR', 'modeli': 'MAN TGL 250', 'year': 2019, 'has_pump': False, 'has_meter': True, 'detachable': False, 'type': 'basic'},

            # Additional trucks for expanded fleet
            {'targa': 'AA 010 TR', 'modeli': 'Scania P 410', 'year': 2021, 'has_pump': True, 'has_meter': True, 'detachable': True, 'type': 'standard'},
            {'targa': 'AA 011 TR', 'modeli': 'Renault T 460', 'year': 2020, 'has_pump': False, 'has_meter': True, 'detachable': False, 'type': 'standard'},
            {'targa': 'AA 012 TR', 'modeli': 'DAF CF 400', 'year': 2018, 'has_pump': True, 'has_meter': True, 'detachable': False, 'type': 'standard'},
        ]
        
        trucks = []
        for i, data in enumerate(trucks_data):
            driver = drivers[i] if i < len(drivers) else None

            # Set truck specifications based on type
            truck_type = data['type']
            if truck_type == 'premium':
                max_weight = 40.0
                length = 16.5
                is_trailer = data['detachable']
                consumption = 32.0
                odometer = random.randint(50000, 150000)
            elif truck_type == 'standard':
                max_weight = 35.0
                length = 14.0 if not data['detachable'] else 16.5
                is_trailer = data['detachable']
                consumption = 35.0
                odometer = random.randint(100000, 300000)
            else:  # basic
                max_weight = 18.0
                length = 10.0
                is_trailer = False
                consumption = 25.0
                odometer = random.randint(150000, 400000)

            # Determine status based on truck age and condition
            truck_age = 2024 - data['year']
            if truck_age > 6 or odometer > 350000:
                status_options = ['i_lire', 'mirembajtje', 'jashte_sherbimi']
                status = random.choice(status_options)
            else:
                status_options = ['i_lire', 'ne_ngarkim', 'ne_rruge']
                status = random.choice(status_options)

            truck, created = Kamion.objects.get_or_create(
                targa=data['targa'],
                defaults={
                    'modeli': data['modeli'],
                    'viti_prodhimit': data['year'],
                    'shofer_aktual': driver,
                    'pesha_maksimale_bruto_ton': max_weight,
                    'gjatesia_totale_m': length,
                    'eshte_trailer': is_trailer,
                    'ka_pompe': data['has_pump'],
                    'ka_kontaliter': data['has_meter'],
                    'ka_gps': True,
                    'statusi': status,
                    'odometri_aktual_km': odometer,
                    'km_mirembajtjes_rradheses': odometer + random.randint(10000, 50000),
                    'konsumi_mesatar_l_100km': consumption,
                }
            )
            trucks.append(truck)

        return trucks
    
    def create_compartments(self, trucks, products):
        """Create realistic compartments for trucks based on their specifications"""
        compartments = []

        # Define compartment configurations by truck type
        compartment_configs = {
            'premium': {
                'count': 5,
                'capacities': [9000, 8500, 8000, 7500, 7000],  # Total ~40,000L
                'heel_amount': 80
            },
            'standard': {
                'count': 4,
                'capacities': [8000, 7500, 7000, 6500],  # Total ~29,000L
                'heel_amount': 60
            },
            'basic': {
                'count': 3,
                'capacities': [6000, 5500, 5000],  # Total ~16,500L
                'heel_amount': 40
            }
        }

        for truck in trucks:
            # Determine truck type from model and specifications
            if truck.pesha_maksimale_bruto_ton >= 35.0 and truck.eshte_trailer:
                truck_type = 'premium'
            elif truck.pesha_maksimale_bruto_ton >= 25.0:
                truck_type = 'standard'
            else:
                truck_type = 'basic'

            config = compartment_configs[truck_type]

            for i in range(config['count']):
                # Assign dedicated products to compartments
                # Compartment 1: Usually Diesel (most common)
                # Compartment 2: Gasoline 95
                # Compartment 3: Gasoline 100 or flexible
                # Additional compartments: Flexible or specialized
                if i == 0:
                    dedicated_product = products[0]  # Diesel
                elif i == 1:
                    dedicated_product = products[1]  # Gasoline 95
                elif i == 2 and len(products) > 2:
                    dedicated_product = products[2]  # Gasoline 100
                else:
                    dedicated_product = None  # Flexible compartment

                compartment, created = Particion.objects.get_or_create(
                    kamion=truck,
                    numri_i_dhomes=i + 1,
                    defaults={
                        'kapaciteti': config['capacities'][i],
                        'produkt_i_dedikuar': dedicated_product,
                        'sasia_heel_litra': config['heel_amount'],
                        'eshte_i_pastruar': True,
                        'sasia_aktuale': 0,
                        'data_pastrimit_fundit': timezone.now() - timedelta(days=random.randint(1, 30)),
                    }
                )
                compartments.append(compartment)

        return compartments
    
    def create_sample_orders(self, stations, tanks, products):
        """Create comprehensive sample orders for testing the system"""
        orders = []

        # Create orders for tanks that need refill
        low_tanks = [tank for tank in tanks if tank.nevojitet_rifornizim]

        # Create automatic orders for critical tanks
        critical_tanks = [tank for tank in tanks if tank.eshte_kritik]
        for tank in critical_tanks[:3]:
            order, created = Porosi.objects.get_or_create(
                stacion=tank.stacion,
                depozite=tank,
                produkt=tank.produkt,
                defaults={
                    'numri_porosise': f"AUTO-{timezone.now().strftime('%Y%m%d')}-{len(orders)+1:03d}",
                    'sasia_e_kerkuar': tank.kapaciteti_i_disponueshem * 0.9,  # Fill to 90%
                    'prioriteti': 'kritike',
                    'data_afati': timezone.now() + timedelta(hours=12),  # Urgent
                    'eshte_automatike': True,
                    'shenimet': 'Automatic critical order - tank below safety level',
                }
            )
            orders.append(order)

        # Create regular orders for low tanks
        regular_low_tanks = [tank for tank in low_tanks if not tank.eshte_kritik]
        for tank in regular_low_tanks[:8]:
            # Vary delivery timing based on consumption patterns
            days_ahead = random.randint(1, 3)
            priority = random.choice(['normale', 'e_larte'])

            order, created = Porosi.objects.get_or_create(
                stacion=tank.stacion,
                depozite=tank,
                produkt=tank.produkt,
                defaults={
                    'numri_porosise': f"REG-{timezone.now().strftime('%Y%m%d')}-{len(orders)+1:03d}",
                    'sasia_e_kerkuar': tank.kapaciteti_i_disponueshem * random.uniform(0.7, 0.85),
                    'prioriteti': priority,
                    'data_afati': timezone.now() + timedelta(days=days_ahead),
                    'eshte_automatike': True,
                    'shenimet': f'Automatic order based on consumption forecast - {tank.konsumi_mesatar_ditor}L/day',
                }
            )
            orders.append(order)

        # Create some manual orders for planning scenarios
        manual_tanks = [tank for tank in tanks if not tank.nevojitet_rifornizim][:5]
        for tank in manual_tanks:
            order, created = Porosi.objects.get_or_create(
                stacion=tank.stacion,
                depozite=tank,
                produkt=tank.produkt,
                defaults={
                    'numri_porosise': f"MAN-{timezone.now().strftime('%Y%m%d')}-{len(orders)+1:03d}",
                    'sasia_e_kerkuar': tank.kapaciteti_total * 0.3,  # Partial refill
                    'prioriteti': 'e_ulet',
                    'data_afati': timezone.now() + timedelta(days=random.randint(3, 7)),
                    'eshte_automatike': False,
                    'shenimet': 'Manual order for stock optimization',
                }
            )
            orders.append(order)

        return orders
