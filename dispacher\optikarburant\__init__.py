# optikarburant/__init__.py
"""
OptiKarburant - Fuel Distribution Optimization System

A comprehensive logistics management system for optimizing fuel distribution
operations, built with Django and modern open-source technologies.
"""

# This will make sure the app is always imported when
# Django starts so that shared_task will use this app.
from .celery import app as celery_app

__all__ = ('celery_app',)
__version__ = '1.0.0'
__author__ = 'OptiKarburant Development Team'
