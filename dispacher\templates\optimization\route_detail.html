<!-- templates/optimization/route_detail.html - Route Detail View -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .route-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .info-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 0.75rem;
        margin-bottom: 1.5rem;
    }
    .stop-item {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 0;
        transition: background-color 0.3s ease;
    }
    .stop-item:last-child {
        border-bottom: none;
    }
    .stop-item:hover {
        background-color: #f8f9fa;
    }
    .stop-number {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: #007bff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
    }
    .stop-number.completed {
        background: #28a745;
    }
    .stop-number.current {
        background: #ffc107;
        color: #212529;
    }
    .metric-box {
        text-align: center;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    .metric-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
    .map-container {
        height: 400px;
        border-radius: 0.5rem;
    }
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    .timeline-item {
        position: relative;
        margin-bottom: 1rem;
    }
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -23px;
        top: 8px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #6c757d;
    }
    .timeline-item.completed::before {
        background: #28a745;
    }
    .timeline-item.current::before {
        background: #ffc107;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-route text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'optimization:dashboard' %}">Optimization</a></li>
            <li class="breadcrumb-item"><a href="{% url 'optimization:route_list' %}">Routes</a></li>
            <li class="breadcrumb-item active">{{ route.emri }}</li>
        </ol>
    </nav>

    <!-- Route Header -->
    <div class="route-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">{{ route.emri }}</h2>
                <p class="mb-2 opacity-75">
                    <i class="fas fa-calendar me-2"></i>{{ route.data_planifikimit|date:"l, F d, Y" }}
                    {% if route.koha_fillimit_planifikuar %}
                        | <i class="fas fa-clock me-2"></i>Start: {{ route.koha_fillimit_planifikuar|date:"H:i" }}
                    {% endif %}
                </p>
                <span class="badge bg-{{ route.status_color }} fs-6">
                    {{ route.get_statusi_display }}
                </span>
            </div>
            <div class="col-md-4 text-end">
                {% if route.statusi == 'draft' %}
                    <button class="btn btn-success btn-lg" onclick="activateRoute()">
                        <i class="fas fa-play me-2"></i>Activate Route
                    </button>
                {% elif route.statusi == 'active' %}
                    <a href="{% url 'mobile:route_detail' route.id %}" class="btn btn-info btn-lg">
                        <i class="fas fa-mobile-alt me-2"></i>Mobile View
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Route Map -->
            <div class="info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map me-2"></i>Route Map
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="routeMap" class="map-container"></div>
                </div>
            </div>

            <!-- Stops List -->
            <div class="info-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Route Stops ({{ stops|length }})
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="optimizeStopOrder()">
                            <i class="fas fa-sort"></i> Re-order
                        </button>
                        <button class="btn btn-outline-success" onclick="exportStops()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% for stop in stops %}
                    <div class="stop-item">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <div class="stop-number {% if stop.statusi == 'completed' %}completed{% elif stop == current_stop %}current{% endif %}">
                                    {{ forloop.counter }}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6 class="mb-1">{{ stop.stacion.emri }}</h6>
                                <small class="text-muted">{{ stop.stacion.adresa|truncatechars:40 }}</small>
                            </div>
                            <div class="col-md-2">
                                <strong>{{ stop.produkt.emri }}</strong>
                                <br><small class="text-muted">{{ stop.sasia_litra|floatformat:0 }}L</small>
                            </div>
                            <div class="col-md-2">
                                {% if stop.koha_e_planifikuar %}
                                    <strong>{{ stop.koha_e_planifikuar|date:"H:i" }}</strong>
                                    <br><small class="text-muted">Planned</small>
                                {% else %}
                                    <span class="text-muted">Time TBD</span>
                                {% endif %}
                            </div>
                            <div class="col-md-2">
                                {% if stop.statusi == 'completed' %}
                                    <span class="badge bg-success">Completed</span>
                                    {% if stop.koha_e_perfundimit %}
                                        <br><small class="text-muted">{{ stop.koha_e_perfundimit|date:"H:i" }}</small>
                                    {% endif %}
                                {% elif stop == current_stop %}
                                    <span class="badge bg-warning text-dark">Current</span>
                                {% else %}
                                    <span class="badge bg-secondary">Pending</span>
                                {% endif %}
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewStopDetails({{ stop.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if route.statusi == 'draft' %}
                                        <button class="btn btn-outline-warning" onclick="editStop({{ stop.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        {% if stop.shenim %}
                        <div class="row mt-2">
                            <div class="col-12 offset-1">
                                <small class="text-muted">
                                    <i class="fas fa-sticky-note me-1"></i>{{ stop.shenim }}
                                </small>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Route Summary -->
            <div class="info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Route Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-box bg-primary text-white">
                                <div class="metric-value">{{ stops|length }}</div>
                                <div class="metric-label">Total Stops</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box bg-success text-white">
                                <div class="metric-value">{{ total_deliveries }}</div>
                                <div class="metric-label">Deliveries</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-box bg-info text-white">
                                <div class="metric-value">{{ route.total_distance_km|floatformat:1 }}</div>
                                <div class="metric-label">Distance (km)</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box bg-warning text-white">
                                <div class="metric-value">{{ route.estimated_duration_hours|floatformat:1 }}</div>
                                <div class="metric-label">Duration (h)</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-3">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Completion Progress:</small>
                            <small class="text-muted">{{ completion_percentage|floatformat:1 }}%</small>
                        </div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: {{ completion_percentage }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Truck Information -->
            <div class="info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck me-2"></i>Assigned Truck
                    </h5>
                </div>
                <div class="card-body">
                    {% if route.kamion %}
                        <h6 class="mb-2">{{ route.kamion.emri }}</h6>
                        <p class="text-muted mb-2">{{ route.kamion.numri_targave }}</p>
                        
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Capacity:</small><br>
                                <strong>{{ route.kamion.kapaciteti_total_litra|floatformat:0 }}L</strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Compartments:</small><br>
                                <strong>{{ route.kamion.particionet.count }}</strong>
                            </div>
                        </div>
                        
                        {% if route.kamion.shofer_aktual %}
                        <div class="mt-3">
                            <small class="text-muted">Driver:</small><br>
                            <strong>{{ route.kamion.shofer_aktual.emri_i_plote }}</strong>
                            {% if route.kamion.shofer_aktual.telefoni %}
                                <br><a href="tel:{{ route.kamion.shofer_aktual.telefoni }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>{{ route.kamion.shofer_aktual.telefoni }}
                                </a>
                            {% endif %}
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-truck fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-2">No truck assigned</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="assignTruck()">
                                <i class="fas fa-plus me-1"></i>Assign Truck
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Optimization Details -->
            <div class="info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Optimization Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <strong>Route Created</strong><br>
                            <small class="text-muted">{{ route.data_krijimit|date:"M d, Y H:i" }}</small>
                        </div>
                        {% if route.statusi != 'draft' %}
                        <div class="timeline-item completed">
                            <strong>Route Activated</strong><br>
                            <small class="text-muted">{{ route.koha_aktivizimit|date:"M d, Y H:i"|default:"N/A" }}</small>
                        </div>
                        {% endif %}
                        {% if route.statusi == 'active' %}
                        <div class="timeline-item current">
                            <strong>In Progress</strong><br>
                            <small class="text-muted">{{ completion_percentage|floatformat:1 }}% complete</small>
                        </div>
                        {% elif route.statusi == 'completed' %}
                        <div class="timeline-item completed">
                            <strong>Route Completed</strong><br>
                            <small class="text-muted">{{ route.koha_perfundimit|date:"M d, Y H:i" }}</small>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-3">
                        <small class="text-muted">Optimization Algorithm:</small><br>
                        <strong>{{ route.optimization_algorithm|default:"Standard OR-Tools" }}</strong>
                    </div>
                    
                    {% if route.optimization_notes %}
                    <div class="mt-2">
                        <small class="text-muted">Notes:</small><br>
                        <p class="mb-0">{{ route.optimization_notes }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="info-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if route.statusi == 'draft' %}
                            <button class="btn btn-success" onclick="activateRoute()">
                                <i class="fas fa-play me-2"></i>Activate Route
                            </button>
                            <button class="btn btn-outline-warning" onclick="editRoute()">
                                <i class="fas fa-edit me-2"></i>Edit Route
                            </button>
                        {% elif route.statusi == 'active' %}
                            <a href="{% url 'mobile:route_detail' route.id %}" class="btn btn-info">
                                <i class="fas fa-mobile-alt me-2"></i>Mobile View
                            </a>
                            <button class="btn btn-outline-warning" onclick="pauseRoute()">
                                <i class="fas fa-pause me-2"></i>Pause Route
                            </button>
                        {% elif route.statusi == 'completed' %}
                            <button class="btn btn-outline-info" onclick="viewReport()">
                                <i class="fas fa-chart-line me-2"></i>View Report
                            </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-success" onclick="reoptimizeRoute()">
                            <i class="fas fa-redo me-2"></i>Re-optimize
                        </button>
                        <button class="btn btn-outline-primary" onclick="duplicateRoute()">
                            <i class="fas fa-copy me-2"></i>Duplicate
                        </button>
                        
                        {% if route.statusi == 'draft' %}
                        <button class="btn btn-outline-danger" onclick="deleteRoute()">
                            <i class="fas fa-trash me-2"></i>Delete Route
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
<script>
// Initialize map
var map = L.map('routeMap').setView([41.3275, 19.8187], 10);

L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// Add route stops to map
var stops = {{ stops_map_data|safe }};
var routeCoordinates = [];

stops.forEach(function(stop, index) {
    var marker = L.circleMarker([stop.lat, stop.lng], {
        radius: 8,
        fillColor: stop.status === 'completed' ? '#28a745' : 
                   stop.status === 'current' ? '#ffc107' : '#007bff',
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    }).addTo(map);
    
    // Add stop number
    var numberIcon = L.divIcon({
        className: 'stop-number-icon',
        html: `<div style="background: ${stop.status === 'completed' ? '#28a745' : 
                                      stop.status === 'current' ? '#ffc107' : '#007bff'}; 
                      color: white; border-radius: 50%; width: 20px; height: 20px; 
                      display: flex; align-items: center; justify-content: center; 
                      font-weight: bold; font-size: 12px;">${index + 1}</div>`,
        iconSize: [20, 20],
        iconAnchor: [10, 10]
    });
    
    L.marker([stop.lat, stop.lng], {icon: numberIcon}).addTo(map);
    
    routeCoordinates.push([stop.lat, stop.lng]);
    
    // Popup
    marker.bindPopup(`
        <div class="stop-popup">
            <h6>${stop.name}</h6>
            <p><strong>Product:</strong> ${stop.product}</p>
            <p><strong>Quantity:</strong> ${stop.quantity}L</p>
            <p><strong>Status:</strong> ${stop.status}</p>
        </div>
    `);
});

// Draw route line
if (routeCoordinates.length > 1) {
    L.polyline(routeCoordinates, {
        color: '#007bff',
        weight: 3,
        opacity: 0.7
    }).addTo(map);
}

// Fit map to show all stops
if (routeCoordinates.length > 0) {
    map.fitBounds(routeCoordinates, {padding: [20, 20]});
}

// Action functions
function activateRoute() {
    if (confirm('Activate this route? This will make it available for the assigned driver.')) {
        fetch(`/optimization/routes/{{ route.id }}/activate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function editRoute() {
    window.location.href = `/optimization/routes/{{ route.id }}/edit/`;
}

function reoptimizeRoute() {
    if (confirm('Re-optimize this route? This will create a new optimized version.')) {
        fetch(`/optimization/routes/{{ route.id }}/reoptimize/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Route re-optimization started!');
                window.location.href = `/optimization/progress/${data.task_id}/`;
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function duplicateRoute() {
    if (confirm('Create a copy of this route?')) {
        fetch(`/optimization/routes/{{ route.id }}/duplicate/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = `/optimization/routes/${data.new_route_id}/`;
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function deleteRoute() {
    if (confirm('Delete this route? This action cannot be undone.')) {
        fetch(`/optimization/routes/{{ route.id }}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '{% url "optimization:route_list" %}';
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

function viewStopDetails(stopId) {
    window.location.href = `/optimization/stops/${stopId}/`;
}

function assignTruck() {
    // This would typically open a modal for truck selection
    alert('Truck assignment feature coming soon!');
}

function optimizeStopOrder() {
    alert('Stop re-ordering feature coming soon!');
}

function exportStops() {
    window.open(`/optimization/routes/{{ route.id }}/export/`, '_blank');
}
</script>
{% endblock %}
