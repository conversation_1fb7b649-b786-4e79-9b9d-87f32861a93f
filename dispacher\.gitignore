# OptiKarburant .gitignore

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Django specific
media/
staticfiles/
static/
logs/
*.sqlite3

# Local development
.DS_Store
Thumbs.db
.vscode/
.idea/
*.swp
*.swo

# Docker
.docker/
docker-compose.override.yml

# Database
*.sql
*.dump

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.sublime-*

# Node modules (if using any frontend tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production files
.env.production
.env.local
.env.staging

# Temporary files
*.tmp
*.temp
temp/
tmp/

# OSRM data files
*.osrm
*.osrm.*
*.pbf

# Celery
celerybeat.pid

# Coverage reports
htmlcov/
.coverage
coverage.xml

# Pytest
.pytest_cache/

# MyPy
.mypy_cache/

# Jupyter
.jupyter/

# Local configuration overrides
local_settings.py
settings_local.py
