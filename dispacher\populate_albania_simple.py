#!/usr/bin/env python
"""
Simple script to populate OptiKarburant database with Albanian sample data
This version avoids spatial/GIS dependencies and uses regular Django models
"""

import os
import sys
import django
from datetime import datetime, time, timedelta
import random

# Setup Django environment with regular settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'optikarburant.settings')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Mock the spatial Point class to avoid GIS dependencies
class MockPoint:
    def __init__(self, lng, lat):
        self.lng = lng
        self.lat = lat
        self.x = lng
        self.y = lat
    
    def __str__(self):
        return f"POINT({self.lng} {self.lat})"

# Replace the real Point with our mock
sys.modules['django.contrib.gis.geos'] = type('MockGeos', (), {'Point': MockPoint})()

try:
    django.setup()
    from django.utils import timezone
    from logistics.models import (
        <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, 
        <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
    )
    print("✓ Django setup successful (without spatial dependencies)")
except Exception as e:
    print(f"✗ Django setup failed: {e}")
    print("Let's try a different approach...")
    sys.exit(1)

def clear_existing_data():
    """Clear all existing data"""
    print("Clearing existing data...")
    try:
        Porosi.objects.all().delete()
        Particion.objects.all().delete()
        Kamion.objects.all().delete()
        Shofer.objects.all().delete()
        Depozite.objects.all().delete()
        Stacion.objects.all().delete()
        DepoQendrore.objects.all().delete()
        Produkt.objects.all().delete()
        print("✓ Existing data cleared")
    except Exception as e:
        print(f"Note: {e}")

def create_products():
    """Create Albanian fuel products"""
    print("Creating fuel products...")
    products_data = [
        {'emri': 'Naftë D2', 'densiteti': 0.85, 'ngjyra_kodi': '#2c3e50'},
        {'emri': 'Benzinë 95', 'densiteti': 0.75, 'ngjyra_kodi': '#e74c3c'},
        {'emri': 'Benzinë 100', 'densiteti': 0.75, 'ngjyra_kodi': '#c0392b'},
        {'emri': 'Gaz i Lëngshëm (LPG)', 'densiteti': 0.51, 'ngjyra_kodi': '#3498db'},
        {'emri': 'Naftë Ngrohje', 'densiteti': 0.87, 'ngjyra_kodi': '#8e44ad'},
    ]
    
    products = []
    for data in products_data:
        try:
            product, created = Produkt.objects.get_or_create(
                emri=data['emri'],
                defaults=data
            )
            products.append(product)
            if created:
                print(f"  ✓ Created product: {product.emri}")
        except Exception as e:
            print(f"  ⚠ Product creation issue: {e}")
    
    # Set product compatibility
    if len(products) >= 3:
        nafte = products[0]  # Naftë D2
        benzine_95 = products[1]  # Benzinë 95
        benzine_100 = products[2]  # Benzinë 100
        
        try:
            # Benzines are compatible with each other
            benzine_95.produkte_kompatible.add(benzine_100)
            benzine_100.produkte_kompatible.add(benzine_95)
        except Exception as e:
            print(f"  ⚠ Compatibility setting issue: {e}")
    
    print(f"✓ Created {len(products)} products")
    return products

def create_central_depot():
    """Create central depot in Tirana"""
    print("Creating central depot...")
    try:
        depot, created = DepoQendrore.objects.get_or_create(
            emri='Depo Qendrore Tiranë',
            defaults={
                'vendndodhja': MockPoint(19.8189, 41.3275),  # Tirana coordinates
                'adresa': 'Rruga Industriale, Tiranë, Shqipëri',
                'kapaciteti_ngarkimi': 8,
                'koha_mesatare_ngarkimi': 90,
                'orar_punes_nga': time(6, 0),
                'orar_punes_deri': time(22, 0),
            }
        )
        if created:
            print(f"  ✓ Created depot: {depot.emri}")
        print("✓ Central depot ready")
        return depot
    except Exception as e:
        print(f"  ⚠ Depot creation issue: {e}")
        return None

def create_drivers():
    """Create Albanian drivers"""
    print("Creating drivers...")
    drivers_data = [
        {'emri': 'Agim', 'mbiemri': 'Kelmendi', 'telefoni': '+355691234567', 'experience': 'senior'},
        {'emri': 'Besnik', 'mbiemri': 'Hoxha', 'telefoni': '+355692345678', 'experience': 'senior'},
        {'emri': 'Driton', 'mbiemri': 'Brahimaj', 'telefoni': '+355693456789', 'experience': 'experienced'},
        {'emri': 'Ermal', 'mbiemri': 'Gjoka', 'telefoni': '+355694567890', 'experience': 'experienced'},
        {'emri': 'Fadil', 'mbiemri': 'Rama', 'telefoni': '+355695678901', 'experience': 'junior'},
        {'emri': 'Genti', 'mbiemri': 'Berisha', 'telefoni': '+355696789012', 'experience': 'senior'},
        {'emri': 'Ilir', 'mbiemri': 'Dervishi', 'telefoni': '+355697890123', 'experience': 'experienced'},
        {'emri': 'Jeton', 'mbiemri': 'Kastrati', 'telefoni': '+355698901234', 'experience': 'junior'},
    ]
    
    drivers = []
    for i, data in enumerate(drivers_data):
        try:
            # Set experience-based parameters
            experience = data['experience']
            if experience == 'senior':
                employment_days = random.randint(1800, 3650)  # 5-10 years
                has_adr = True
                max_work_hours = 10
                max_drive_hours = 8
            elif experience == 'experienced':
                employment_days = random.randint(730, 1800)   # 2-5 years
                has_adr = random.choice([True, False])
                max_work_hours = 9
                max_drive_hours = 7
            else:  # junior
                employment_days = random.randint(90, 730)     # 3 months - 2 years
                has_adr = False
                max_work_hours = 8
                max_drive_hours = 6
            
            driver, created = Shofer.objects.get_or_create(
                leje_drejtimi_numri=f"AL{2024000 + i:06d}",
                defaults={
                    'emri': data['emri'],
                    'mbiemri': data['mbiemri'],
                    'telefoni': data['telefoni'],
                    'email': f"{data['emri'].lower()}.{data['mbiemri'].lower()}@optikarburant.al",
                    'leje_drejtimi_skadon': timezone.now().date() + timedelta(days=random.randint(180, 1095)),
                    'leje_adr': has_adr,
                    'ore_punes_maksimale_ditor': max_work_hours,
                    'ore_drejtimi_maksimale_ditor': max_drive_hours,
                    'data_punesimit': timezone.now().date() - timedelta(days=employment_days),
                }
            )
            drivers.append(driver)
            if created:
                print(f"  ✓ Created driver: {driver.emri} {driver.mbiemri}")
        except Exception as e:
            print(f"  ⚠ Driver creation issue: {e}")
    
    print(f"✓ Created {len(drivers)} drivers")
    return drivers

def create_trucks(drivers):
    """Create Albanian truck fleet"""
    print("Creating trucks...")
    trucks_data = [
        {'targa': 'AA 001 TR', 'modeli': 'Mercedes Actros 2545', 'year': 2022, 'has_pump': True, 'has_meter': True, 'type': 'premium'},
        {'targa': 'AA 002 TR', 'modeli': 'Volvo FH 460', 'year': 2021, 'has_pump': True, 'has_meter': True, 'type': 'premium'},
        {'targa': 'AA 003 TR', 'modeli': 'Scania R 450', 'year': 2023, 'has_pump': True, 'has_meter': True, 'type': 'premium'},
        {'targa': 'AA 004 TR', 'modeli': 'MAN TGX 440', 'year': 2020, 'has_pump': True, 'has_meter': True, 'type': 'standard'},
        {'targa': 'AA 005 TR', 'modeli': 'DAF XF 440', 'year': 2019, 'has_pump': False, 'has_meter': True, 'type': 'standard'},
        {'targa': 'AA 006 TR', 'modeli': 'Iveco Stralis 420', 'year': 2020, 'has_pump': True, 'has_meter': True, 'type': 'standard'},
        {'targa': 'AA 007 TR', 'modeli': 'Mercedes Atego 1218', 'year': 2018, 'has_pump': False, 'has_meter': True, 'type': 'basic'},
        {'targa': 'AA 008 TR', 'modeli': 'Volvo FL 280', 'year': 2017, 'has_pump': False, 'has_meter': False, 'type': 'basic'},
    ]
    
    trucks = []
    for i, data in enumerate(trucks_data):
        try:
            driver = drivers[i] if i < len(drivers) else None
            
            # Set truck specifications based on type
            truck_type = data['type']
            if truck_type == 'premium':
                max_weight = 40.0
                length = 16.5
                is_trailer = True
                consumption = 32.0
                odometer = random.randint(50000, 150000)
            elif truck_type == 'standard':
                max_weight = 35.0
                length = 14.0
                is_trailer = False
                consumption = 35.0
                odometer = random.randint(100000, 300000)
            else:  # basic
                max_weight = 18.0
                length = 10.0
                is_trailer = False
                consumption = 25.0
                odometer = random.randint(150000, 400000)
            
            truck, created = Kamion.objects.get_or_create(
                targa=data['targa'],
                defaults={
                    'modeli': data['modeli'],
                    'viti_prodhimit': data['year'],
                    'shofer_aktual': driver,
                    'pesha_maksimale_bruto_ton': max_weight,
                    'gjatesia_totale_m': length,
                    'eshte_trailer': is_trailer,
                    'ka_pompe': data['has_pump'],
                    'ka_kontaliter': data['has_meter'],
                    'ka_gps': True,
                    'statusi': 'i_lire',
                    'odometri_aktual_km': odometer,
                    'km_mirembajtjes_rradheses': odometer + random.randint(10000, 50000),
                    'konsumi_mesatar_l_100km': consumption,
                }
            )
            trucks.append(truck)
            if created:
                print(f"  ✓ Created truck: {truck.targa} - {truck.modeli}")
        except Exception as e:
            print(f"  ⚠ Truck creation issue: {e}")
    
    print(f"✓ Created {len(trucks)} trucks")
    return trucks

def main():
    """Main function to populate the database"""
    print("🇦🇱 OptiKarburant Albania - Simple Data Population")
    print("=" * 60)
    
    try:
        # Clear existing data
        clear_existing_data()
        
        # Create products
        products = create_products()
        
        # Create central depot
        depot = create_central_depot()
        
        # Create drivers
        drivers = create_drivers()
        
        # Create trucks
        trucks = create_trucks(drivers)
        
        print("\n" + "=" * 60)
        print("✅ Basic sample data population completed!")
        print(f"📊 Summary:")
        print(f"   • Products: {len(products)}")
        print(f"   • Central Depot: {1 if depot else 0}")
        print(f"   • Drivers: {len(drivers)}")
        print(f"   • Trucks: {len(trucks)}")
        print("\n🚀 You can now run the Django server and explore the data!")
        print("💡 Note: This version uses simplified data without spatial features")
        
    except Exception as e:
        print(f"\n❌ Error during data population: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
