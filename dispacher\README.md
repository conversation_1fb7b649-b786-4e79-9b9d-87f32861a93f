# OptiKarburant - Fuel Distribution Optimization System

OptiKarburant është një sistem i avancuar për optimizimin e shpërndarjes së karburanteve, i ndërtuar me Django dhe teknologji moderne open-source. Sistemi zgjidh problemin e Vehicle Routing Problem (VRP) me kufizime të shumta specifike për industrinë e karburanteve.

*OptiKarburant is an advanced fuel distribution optimization system built with Django and modern open-source technologies. The system solves the Vehicle Routing Problem (VRP) with multiple constraints specific to the fuel industry.*

## 🚀 Karakteristikat Kryesore / Key Features

### 📊 Menaxhimi i Inventarit / Inventory Management
- Monitorim në kohë reale të niveleve të depozitave
- Gjenerimi automatik i porosive bazuar në safety stock
- Parashikimi i kërkesës bazuar në të dhënat historike
- Raportim i detajuar për çdo stacion

### 🚛 Menaxhimi i Flotës / Fleet Management  
- Menaxhimi i kamionëve me compartmente të shumta
- Ndjekja e gjendjes së pajisjeve (pompa, kontalitër)
- Kompatibiliteti i produkteve dhe parandalimi i kontaminimit
- Monitorimi i mirëmbajtjes dhe performancës

### 🗺️ Optimizimi i Rrugëve / Route Optimization
- Algoritëm i avancuar optimizimi duke përdorur Google OR-Tools
- Respektimi i kufizimeve të kohës dhe orarit të punës
- Integrimi me OSRM për llogaritjen e distancave reale
- Optimizim multi-objektiv (kohë, kosto, karburant)

### 🌐 Ndërfaqe të Shumta / Multiple Interfaces
- Dashboard menaxherial me harta interaktive
- Ndërfaqe mobile për shoferët
- API RESTful për integrime të jashtme
- Suport për anglisht dhe shqip

## 🛠️ Stack Teknologjik / Technology Stack

### Backend
- **Django 4.2+** - Web framework kryesor
- **PostgreSQL + PostGIS** - Databaza me suport gjeografik
- **Celery + Redis** - Procesim në sfond
- **Google OR-Tools** - Motor optimizimi

### Shërbime të Jashtme / External Services  
- **OSRM** - Open Source Routing Machine (self-hosted)
- **Nominatim** - Geocoding service (self-hosted)
- **OpenStreetMap** - Harta dhe të dhëna gjeografike

### Frontend
- **Django Templates** + **Bootstrap 5**
- **Leaflet.js** - Harta interaktive
- **JavaScript/HTMX** - Interaktivitet

### DevOps
- **Docker & Docker Compose** - Containerization
- **Nginx** - Reverse proxy (production)
- **Gunicorn** - WSGI server

## 📋 Kërkesat e Sistemit / System Requirements

### Minimale / Minimum
- **RAM:** 4GB
- **CPU:** 2 cores
- **Storage:** 20GB
- **OS:** Linux, Windows, macOS

### Të Rekomanduara / Recommended
- **RAM:** 8GB+
- **CPU:** 4+ cores
- **Storage:** 50GB+ SSD
- **Network:** Banda e gjerë për shërbimet e hartave

## 🚀 Instalimi / Installation

### 1. Klono Projektin / Clone Project
```bash
git clone https://github.com/your-org/optikarburant.git
cd optikarburant
```

### 2. Kopjo dhe Konfiguro Variablat / Copy and Configure Environment
```bash
cp .env.example .env
# Edit .env with your settings
```

### 3. Ngrije me Docker / Start with Docker
```bash
# Ndërtoni dhe nisni të gjitha shërbimet
docker-compose up --build

# Ose për background execution
docker-compose up -d
```

### 4. Krijo Databazën / Setup Database
```bash
# Krijo migrimet
docker-compose exec web python manage.py makemigrations

# Ekzekuto migrimet  
docker-compose exec web python manage.py migrate

# Krijo superuser
docker-compose exec web python manage.py createsuperuser

# Ngarko të dhëna fillestare (opsionale)
docker-compose exec web python manage.py loaddata fixtures/initial_data.json
```

### 5. Akses në Sistem / Access System
- **Web Application:** http://localhost:8000
- **Admin Panel:** http://localhost:8000/admin
- **API Documentation:** http://localhost:8000/api/
- **Celery Monitor (Flower):** http://localhost:5555
- **OSRM API:** http://localhost:5000
- **Nominatim API:** http://localhost:8080

## 📖 Udhëzues Përdorimi / Usage Guide

### Sprint 1: Konfigurimi Fillestar / Initial Setup

1. **Krijo Produktet:** Shto produktet e karburanteve (Naftë, Benzinë, etj.)
2. **Regjistro Stacionet:** Shto të gjitha stacionet me koordinatat GPS
3. **Konfiguro Depon Qendrore:** Cakto vendndodhjen dhe kapacitetin
4. **Regjistro Flotën:** Shto kamionët dhe compartmentet e tyre

### Sprint 2: Menaxhimi i Inventarit / Inventory Management

1. **Krijo Depozitat:** Për çdo stacion, krijo depozitat për secilin produkt
2. **Vendos Nivelet:** Konfiguro safety stock dhe reorder levels
3. **Përditëso Sasitë:** Raporto nivelet aktuale të inventarit

### Sprint 3: Optimizimi i Parë / First Optimization

1. **Gjenero Porosite:** Sistemi do të krijojë porosi automatike
2. **Nis Optimizimin:** Kliko "Gjenero Planin e Ditës"
3. **Shiko Rezultatet:** Kontrollo rrugët e krijuara në hartë
4. **Miratohen Rrugët:** Mirato planet për implementim

## 🔧 Zhvillimi / Development

### Struktura e Projektit / Project Structure
```
optikarburant/
├── optikarburant/          # Django main project
├── logistics/              # Core logistics models
├── optimization/           # Route optimization engine  
├── dashboard/              # Web dashboard interface
├── api/                   # REST API endpoints
├── mobile/                # Mobile driver interface
├── reports/               # Analytics and reporting
├── templates/             # Django templates
├── static/                # Static files (CSS, JS, images)
├── media/                 # Uploaded files
├── locale/                # Translation files
└── docs/                  # Documentation
```

### Ekzekutimi pa Docker / Running without Docker
```bash
# Virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Install dependencies
pip install -r requirements.txt

# Setup database (PostgreSQL with PostGIS required)
python manage.py migrate

# Start Redis server (required for Celery)
redis-server

# Start Celery worker (separate terminal)
celery -A optikarburant worker --loglevel=info

# Start Django development server
python manage.py runserver
```

### Testing
```bash
# Run all tests
python manage.py test

# Run with coverage
coverage run --source='.' manage.py test
coverage report
coverage html
```

## 📊 Monitorimi / Monitoring

### Celery Tasks
- Mbikëqyrja e detyrave në Flower: http://localhost:5555
- Logat në `/code/logs/optikarburant.log`

### Performance Metrics
- Django Debug Toolbar (development)
- PostgreSQL query analysis
- OSRM response times

## 🔐 Siguria / Security

### Prodhimi / Production
- Përdorni HTTPS (SSL/TLS)
- Konfiguroni SECURE_* settings në Django
- Përdorni secrets për API keys
- Monitoroni me Sentry

### Backup
```bash
# Database backup
docker-compose exec db pg_dump -U postgres optikarburant > backup.sql

# Restore
docker-compose exec -T db psql -U postgres optikarburant < backup.sql
```

## 🌍 Internationalization

Sistemi suporton anglisht dhe shqip:

```bash
# Generate translation files
python manage.py makemessages -l sq
python manage.py makemessages -l en

# Compile translations
python manage.py compilemessages
```

## 🤝 Kontributi / Contributing

1. Fork projektin
2. Krijo branch për feature-in: `git checkout -b feature/amazing-feature`
3. Commit ndryshimet: `git commit -m 'Add amazing feature'`
4. Push në branch: `git push origin feature/amazing-feature`
5. Krijo Pull Request

## 📝 Licensa / License

Ky projekt është nën licencën MIT. Shiko fajlin `LICENSE` për detaje.

## 🆘 Support

- **Email:** <EMAIL>
- **Documentation:** [Wiki](https://github.com/your-org/optikarburant/wiki)
- **Issues:** [GitHub Issues](https://github.com/your-org/optikarburant/issues)

## 🚀 Roadmap

### Version 1.1
- [ ] Mobile app për shoferët (React Native)
- [ ] Advanced analytics dhe AI forecasting  
- [ ] Integration me sisteme të jashtme ERP
- [ ] Multi-depot support

### Version 1.2
- [ ] Real-time GPS tracking
- [ ] IoT sensor integration
- [ ] Advanced reporting dashboards
- [ ] Multi-tenant architecture

---

**Zhvilluar me ❤️ nga ekipi OptiKarburant**
