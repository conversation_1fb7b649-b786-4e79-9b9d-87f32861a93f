<!-- templates/dashboard/fleet_overview.html - Fleet Management Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .truck-card {
        transition: transform 0.2s;
    }
    .truck-card:hover {
        transform: translateY(-2px);
    }
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    .utilization-bar {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    .utilization-fill {
        height: 100%;
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-truck text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Fleet Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ fleet_stats.total_trucks }}</h4>
                            <p class="mb-0">Total Trucks</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ fleet_stats.available_trucks }}</h4>
                            <p class="mb-0">Available</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ fleet_stats.en_route_trucks }}</h4>
                            <p class="mb-0">En Route</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-route fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ fleet_stats.maintenance_trucks }}</h4>
                            <p class="mb-0">Maintenance</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-wrench fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="get" class="d-flex gap-2">
                <select name="status" class="form-select">
                    <option value="">All Status</option>
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
                <button type="submit" class="btn btn-primary">Filter</button>
                <a href="{% url 'dashboard:fleet_overview' %}" class="btn btn-outline-secondary">Clear</a>
            </form>
        </div>
        <div class="col-md-6 text-end">
            <span class="text-muted">Showing {{ trucks|length }} trucks</span>
        </div>
    </div>

    <!-- Trucks Grid -->
    <div class="row">
        {% for truck in trucks %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card truck-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <strong>{{ truck.emri }}</strong>
                        <small class="text-muted">({{ truck.numri_targave }})</small>
                    </h6>
                    <span class="status-indicator 
                        {% if truck.statusi == 'i_lire' %}bg-success
                        {% elif truck.statusi == 'ne_rruge' %}bg-warning
                        {% elif truck.statusi == 'mirembajtje' %}bg-danger
                        {% else %}bg-secondary{% endif %}"></span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Driver:</small><br>
                            {% if truck.shofer_aktual %}
                                <strong>{{ truck.shofer_aktual.emri_i_plote }}</strong>
                            {% else %}
                                <span class="text-muted">Not assigned</span>
                            {% endif %}
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Status:</small><br>
                            <span class="badge 
                                {% if truck.statusi == 'i_lire' %}bg-success
                                {% elif truck.statusi == 'ne_rruge' %}bg-warning
                                {% elif truck.statusi == 'mirembajtje' %}bg-danger
                                {% else %}bg-secondary{% endif %}">
                                {{ truck.get_statusi_display }}
                            </span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-6">
                            <small class="text-muted">Capacity:</small><br>
                            <strong>{{ truck.kapaciteti_total_litra|floatformat:0 }}L</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Compartments:</small><br>
                            <strong>{{ truck.particionet.count }}</strong>
                        </div>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted">Odometer:</small><br>
                        <strong>{{ truck.odometri_aktual_km|floatformat:0 }} km</strong>
                        {% if truck.duhet_mirembajtje %}
                            <span class="badge bg-warning ms-2">Maintenance Due</span>
                        {% endif %}
                    </div>

                    <!-- Utilization Bar -->
                    <div class="mb-2">
                        <small class="text-muted">Weekly Utilization:</small>
                        <div class="utilization-bar">
                            <div class="utilization-fill bg-primary" style="width: {{ truck.utilization_percentage|default:0 }}%"></div>
                        </div>
                        <small class="text-muted">{{ truck.utilization_percentage|default:0|floatformat:1 }}%</small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> Details
                        </a>
                        {% if truck.statusi == 'i_lire' %}
                            <a href="#" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-route"></i> Assign Route
                            </a>
                        {% else %}
                            <a href="#" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-map-marked-alt"></i> Track
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No trucks found</h5>
                <p class="text-muted">Try adjusting your filter criteria or add new trucks to the fleet.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Maintenance Alerts -->
    {% if maintenance_alerts %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle"></i> Maintenance Alerts
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for alert in maintenance_alerts %}
                        <div class="col-md-6 mb-2">
                            <div class="alert alert-warning mb-2">
                                <strong>{{ alert.truck.emri }}</strong> ({{ alert.truck.numri_targave }})
                                <br><small>{{ alert.message }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh every 30 seconds for real-time updates
setInterval(function() {
    // Only refresh if no modals are open
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 30000);

// Add click handlers for truck cards
document.querySelectorAll('.truck-card').forEach(function(card) {
    card.addEventListener('click', function(e) {
        // Don't trigger if clicking on buttons
        if (!e.target.closest('.btn')) {
            // Could open truck details modal here
            console.log('Truck card clicked');
        }
    });
});
</script>
{% endblock %}
