# logistics/models.py - Core models for OptiKarburant system

from django.contrib.gis.db import models as gis_models
from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User
from django.utils import timezone


class Produkt(models.Model):
    """Fuel products (Diesel, Gasoline, Gas, etc.)"""
    emri = models.CharField(_("Product Name"), max_length=100, unique=True)
    densiteti = models.FloatField(
        _("Density (kg/L)"), 
        default=0.85,
        help_text=_("Density for volume/weight conversions")
    )
    ngjyra_kodi = models.CharField(
        _("Color Code"), 
        max_length=7, 
        default="#3498db",
        help_text=_("Hex color code for UI display")
    )
    eshte_aktiv = models.BooleanField(_("Active"), default=True)
    data_krijimit = models.DateTimeField(_("Created"), auto_now_add=True)
    
    # Product compatibility for cross-contamination prevention
    produkte_kompatible = models.ManyToManyField(
        'self', 
        blank=True, 
        symmetrical=True,
        verbose_name=_("Compatible Products"),
        help_text=_("Products that can share the same compartment")
    )
    
    class Meta:
        verbose_name = _("Product")
        verbose_name_plural = _("Products")
        ordering = ['emri']
    
    def __str__(self):
        return self.emri


class DepoQendrore(models.Model):
    """Central depot where trucks are loaded"""
    emri = models.CharField(_("Depot Name"), max_length=100, default="Depo Qendrore")
    vendndodhja = gis_models.PointField(_("Location"))
    adresa = models.TextField(_("Address"), blank=True)
    kapaciteti_ngarkimi = models.IntegerField(
        _("Loading Capacity"), 
        default=10,
        help_text=_("Number of trucks that can be loaded simultaneously")
    )
    koha_mesatare_ngarkimi = models.IntegerField(
        _("Average Loading Time (minutes)"), 
        default=90,
        help_text=_("Average time to load one truck")
    )
    orar_punes_nga = models.TimeField(_("Working Hours From"), default="06:00")
    orar_punes_deri = models.TimeField(_("Working Hours To"), default="22:00")
    eshte_aktiv = models.BooleanField(_("Active"), default=True)
    
    class Meta:
        verbose_name = _("Central Depot")
        verbose_name_plural = _("Central Depots")
    
    def __str__(self):
        return self.emri


class Stacion(models.Model):
    """Fuel stations that need deliveries"""
    emri = models.CharField(_("Station Name"), max_length=200)
    kodi = models.CharField(_("Station Code"), max_length=20, unique=True)
    vendndodhja = gis_models.PointField(_("Location"))
    adresa = models.TextField(_("Address"), blank=True)
    
    # Operating hours for deliveries
    orar_pranimi_nga = models.TimeField(
        _("Delivery Start Time"), 
        default="06:00"
    )
    orar_pranimi_deri = models.TimeField(
        _("Delivery End Time"), 
        default="18:00"
    )
    
    # Equipment requirements
    kerkon_pompe = models.BooleanField(
        _("Requires Pump"), 
        default=False,
        help_text=_("Station requires truck with pump for unloading")
    )
    kerkon_kontaliter = models.BooleanField(
        _("Requires Flow Meter"), 
        default=False,
        help_text=_("Station requires truck with flow meter")
    )
    
    # Physical constraints
    max_kamione_njekohesisht = models.IntegerField(
        _("Max Simultaneous Trucks"), 
        default=1,
        help_text=_("Maximum trucks that can unload simultaneously")
    )
    koha_mesatare_shkarkimi = models.IntegerField(
        _("Average Unloading Time (minutes)"), 
        default=45
    )
    
    # Access restrictions
    max_pesha_kamioni_ton = models.FloatField(
        _("Max Truck Weight (tons)"), 
        null=True, 
        blank=True,
        help_text=_("Maximum truck weight allowed")
    )
    max_gjatesia_kamioni_m = models.FloatField(
        _("Max Truck Length (m)"), 
        null=True, 
        blank=True
    )
    
    # Contact information
    menaxher_emri = models.CharField(_("Manager Name"), max_length=100, blank=True)
    telefoni = models.CharField(_("Phone"), max_length=20, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    
    eshte_aktiv = models.BooleanField(_("Active"), default=True)
    data_krijimit = models.DateTimeField(_("Created"), auto_now_add=True)
    data_perditesimit = models.DateTimeField(_("Updated"), auto_now=True)
    
    class Meta:
        verbose_name = _("Station")
        verbose_name_plural = _("Stations")
        ordering = ['emri']
    
    def __str__(self):
        return f"{self.emri} ({self.kodi})"


class Depozite(models.Model):
    """Individual fuel tanks at stations"""
    stacion = models.ForeignKey(
        Stacion, 
        on_delete=models.CASCADE, 
        related_name='depozitat',
        verbose_name=_("Station")
    )
    produkt = models.ForeignKey(
        Produkt, 
        on_delete=models.PROTECT,
        verbose_name=_("Product")
    )
    numri_tankut = models.CharField(_("Tank Number"), max_length=10)
    
    # Capacity and levels
    kapaciteti_total = models.FloatField(
        _("Total Capacity (L)"), 
        validators=[MinValueValidator(0)]
    )
    sasia_aktuale = models.FloatField(
        _("Current Quantity (L)"), 
        validators=[MinValueValidator(0)]
    )
    niveli_minimal_sigurise = models.FloatField(
        _("Safety Stock Level (L)"), 
        validators=[MinValueValidator(0)],
        help_text=_("Critical level - generates emergency order")
    )
    niveli_i_porosise = models.FloatField(
        _("Reorder Level (L)"), 
        validators=[MinValueValidator(0)],
        help_text=_("Level when automatic order should be generated")
    )
    
    # Consumption forecasting
    konsumi_mesatar_ditor = models.FloatField(
        _("Average Daily Consumption (L)"), 
        default=0,
        help_text=_("Used for demand forecasting")
    )
    
    # Delivery constraints
    sasia_minimale_dorezimi = models.FloatField(
        _("Minimum Delivery Quantity (L)"), 
        default=1000,
        help_text=_("Minimum economical delivery quantity")
    )
    perqindja_maksimale_mbushjes = models.FloatField(
        _("Max Fill Percentage"), 
        default=95, 
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    
    data_dorezimit_fundit = models.DateField(_("Last Delivery"), null=True, blank=True)
    data_perditesimit = models.DateTimeField(_("Last Updated"), auto_now=True)
    
    class Meta:
        verbose_name = _("Tank")
        verbose_name_plural = _("Tanks")
        unique_together = [['stacion', 'produkt', 'numri_tankut']]
        ordering = ['stacion', 'produkt', 'numri_tankut']
    
    def __str__(self):
        return f"{self.stacion.emri} - {self.produkt.emri} Tank {self.numri_tankut}"
    
    @property
    def dite_deri_zbrazje(self):
        """Calculate days until tank reaches safety level"""
        if self.konsumi_mesatar_ditor > 0:
            mbetur = self.sasia_aktuale - self.niveli_minimal_sigurise
            return max(0, mbetur / self.konsumi_mesatar_ditor)
        return float('inf')
    
    @property
    def nevojitet_rifornizim(self):
        """Check if tank needs refill"""
        return self.sasia_aktuale <= self.niveli_i_porosise
    
    @property
    def eshte_kritik(self):
        """Check if tank is at critical level"""
        return self.sasia_aktuale <= self.niveli_minimal_sigurise
    
    @property
    def perqindja_mbushjes(self):
        """Current fill percentage"""
        if self.kapaciteti_total > 0:
            return (self.sasia_aktuale / self.kapaciteti_total) * 100
        return 0
    
    @property
    def kapaciteti_i_disponueshem(self):
        """Available capacity considering max fill percentage"""
        max_sasia = (self.kapaciteti_total * self.perqindja_maksimale_mbushjes) / 100
        return max(0, max_sasia - self.sasia_aktuale)# logistics/models.py continued - Fleet and Order models

class Shofer(models.Model):
    """Truck drivers"""
    emri = models.CharField(_("Driver Name"), max_length=100)
    mbiemri = models.CharField(_("Surname"), max_length=100)
    telefoni = models.CharField(_("Phone"), max_length=20, blank=True)
    email = models.EmailField(_("Email"), blank=True)
    
    # License information
    leje_drejtimi_numri = models.CharField(
        _("License Number"), 
        max_length=50, 
        unique=True
    )
    leje_drejtimi_skadon = models.DateField(_("License Expires"))
    leje_adr = models.BooleanField(
        _("ADR License"), 
        default=False,
        help_text=_("License for dangerous goods transport")
    )
    
    # Working hours constraints
    ore_punes_maksimale_ditor = models.IntegerField(
        _("Max Daily Working Hours"), 
        default=8
    )
    ore_drejtimi_maksimale_ditor = models.IntegerField(
        _("Max Daily Driving Hours"), 
        default=6
    )
    
    # Employment status
    eshte_aktiv = models.BooleanField(_("Active"), default=True)
    data_punesimit = models.DateField(_("Employment Date"), null=True, blank=True)
    
    class Meta:
        verbose_name = _("Driver")
        verbose_name_plural = _("Drivers")
        ordering = ['emri', 'mbiemri']
    
    def __str__(self):
        return f"{self.emri} {self.mbiemri}"
    
    @property
    def emri_i_plote(self):
        return f"{self.emri} {self.mbiemri}"


class Kamion(models.Model):
    """Delivery trucks with compartments"""
    STATUS_CHOICES = [
        ('i_lire', _('Available')),
        ('ne_ngarkim', _('Loading')),
        ('ne_rruge', _('En Route')),
        ('ne_shkarkim', _('Unloading')),
        ('jashte_sherbimi', _('Out of Service')),
        ('mirembajtje', _('Maintenance')),
    ]
    
    targa = models.CharField(_("License Plate"), max_length=20, unique=True)
    modeli = models.CharField(_("Truck Model"), max_length=100, blank=True)
    viti_prodhimit = models.IntegerField(_("Year"), null=True, blank=True)
    
    # Current driver assignment
    shofer_aktual = models.ForeignKey(
        Shofer, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='kamioni_aktual',
        verbose_name=_("Current Driver")
    )
    
    # Physical specifications
    pesha_maksimale_bruto_ton = models.FloatField(
        _("Max Gross Weight (tons)"),
        help_text=_("Maximum legal weight including cargo")
    )
    gjatesia_totale_m = models.FloatField(_("Total Length (m)"))
    
    # Truck type and capabilities
    eshte_trailer = models.BooleanField(
        _("Is Trailer"), 
        default=True,
        help_text=_("Truck head can be detached")
    )
    ka_pompe = models.BooleanField(
        _("Has Pump"), 
        default=False,
        help_text=_("Equipped with unloading pump")
    )
    ka_kontaliter = models.BooleanField(
        _("Has Flow Meter"), 
        default=False,
        help_text=_("Equipped with flow meter")
    )
    ka_gps = models.BooleanField(_("Has GPS Tracking"), default=False)
    
    # Current status and location
    statusi = models.CharField(
        _("Status"), 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='i_lire'
    )
    vendndodhja_aktuale = gis_models.PointField(
        _("Current Location"), 
        null=True, 
        blank=True
    )
    data_perditesimit_gps = models.DateTimeField(
        _("Last GPS Update"), 
        null=True, 
        blank=True
    )
    
    # Maintenance tracking
    data_mirembajtjes_fundit = models.DateField(
        _("Last Maintenance"), 
        null=True, 
        blank=True
    )
    km_mirembajtjes_rradheses = models.IntegerField(
        _("Next Maintenance (km)"), 
        null=True, 
        blank=True
    )
    odometri_aktual_km = models.IntegerField(_("Current Odometer (km)"), default=0)
    
    # Fuel consumption
    konsumi_mesatar_l_100km = models.FloatField(
        _("Average Fuel Consumption (L/100km)"), 
        default=35.0
    )
    
    eshte_aktiv = models.BooleanField(_("Active"), default=True)
    data_krijimit = models.DateTimeField(_("Created"), auto_now_add=True)
    
    class Meta:
        verbose_name = _("Truck")
        verbose_name_plural = _("Trucks")
        ordering = ['targa']
    
    def __str__(self):
        return f"{self.targa} ({self.get_statusi_display()})"
    
    @property
    def kapaciteti_total_litra(self):
        """Total capacity of all compartments"""
        return sum(p.kapaciteti for p in self.particionet.all())
    
    @property
    def kapaciteti_i_disponueshem_litra(self):
        """Available capacity considering heel stock"""
        return sum(p.kapaciteti_i_disponueshem for p in self.particionet.all())
    
    @property
    def nevojitet_mirembajtje(self):
        """Check if truck needs maintenance"""
        if self.km_mirembajtjes_rradheses:
            return self.odometri_aktual_km >= self.km_mirembajtjes_rradheses
        return False


class Particion(models.Model):
    """Individual compartments within trucks"""
    kamion = models.ForeignKey(
        Kamion, 
        on_delete=models.CASCADE, 
        related_name='particionet',
        verbose_name=_("Truck")
    )
    numri_i_dhomes = models.IntegerField(_("Compartment Number"))
    kapaciteti = models.FloatField(
        _("Capacity (L)"), 
        validators=[MinValueValidator(0)]
    )
    
    # Product assignment and contamination tracking
    produkt_i_dedikuar = models.ForeignKey(
        Produkt, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='particionet_te_dedikuar',
        verbose_name=_("Dedicated Product"),
        help_text=_("Product exclusively assigned to this compartment")
    )
    produkti_aktual = models.ForeignKey(
        Produkt, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='particionet_aktuale',
        verbose_name=_("Current Product")
    )
    produkti_i_fundit = models.ForeignKey(
        Produkt, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='particionet_te_fundit',
        verbose_name=_("Last Product")
    )
    
    # Heel stock (unusable remainder due to pump limitations)
    sasia_heel_litra = models.FloatField(
        _("Heel Stock (L)"), 
        default=50,
        help_text=_("Quantity that cannot be pumped out")
    )
    
    # Cleaning status
    eshte_i_pastruar = models.BooleanField(_("Is Clean"), default=True)
    data_pastrimit = models.DateTimeField(_("Last Cleaned"), null=True, blank=True)
    kerkon_pastrimin = models.BooleanField(_("Needs Cleaning"), default=False)
    
    # Current load
    sasia_aktuale = models.FloatField(
        _("Current Quantity (L)"), 
        default=0,
        validators=[MinValueValidator(0)]
    )
    
    class Meta:
        verbose_name = _("Compartment")
        verbose_name_plural = _("Compartments")
        unique_together = [['kamion', 'numri_i_dhomes']]
        ordering = ['kamion', 'numri_i_dhomes']
    
    def __str__(self):
        return f"{self.kamion.targa} - Dhoma {self.numri_i_dhomes}"
    
    @property
    def kapaciteti_i_disponueshem(self):
        """Usable capacity excluding heel stock and current load"""
        return max(0, self.kapaciteti - self.sasia_heel_litra - self.sasia_aktuale)
    
    @property
    def eshte_bosh(self):
        """Check if compartment is empty (only heel stock remains)"""
        return self.sasia_aktuale <= self.sasia_heel_litra
    
    def mund_te_ngarkohet_produkti(self, produkt):
        """Check if compartment can safely load a specific product"""
        # If dedicated to a specific product, only that product allowed
        if self.produkt_i_dedikuar:
            return self.produkt_i_dedikuar == produkt
        
        # If compartment is empty and clean, can load any product
        if self.eshte_bosh and self.eshte_i_pastruar:
            return True
        
        # If has current product, must be same product
        if self.produkti_aktual:
            return self.produkti_aktual == produkt
        
        # If not clean, must match last product or be compatible
        if not self.eshte_i_pastruar and self.produkti_i_fundit:
            return (self.produkti_i_fundit == produkt or 
                    produkt in self.produkti_i_fundit.produkte_kompatible.all())
        
        return False
    
    def merr_kapacitetin_ngarkimi_per_produktin(self, produkt):
        """Get maximum quantity that can be loaded for specific product"""
        if not self.mund_te_ngarkohet_produkti(produkt):
            return 0
        return self.kapaciteti_i_disponueshem# logistics/models.py continued - Order and Route models

class Porosi(models.Model):
    """Delivery orders generated automatically or manually"""
    STATUS_CHOICES = [
        ('e_hapur', _('Open')),
        ('e_planifikuar', _('Planned')),
        ('ne_transport', _('In Transport')),
        ('e_dorezuar', _('Delivered')),
        ('e_anulluar', _('Cancelled')),
    ]
    
    PRIORITY_CHOICES = [
        ('e_ulet', _('Low')),
        ('normale', _('Normal')),
        ('e_larte', _('High')),
        ('kritike', _('Critical')),
    ]
    
    numri_porosise = models.CharField(
        _("Order Number"), 
        max_length=20, 
        unique=True,
        blank=True
    )
    stacion = models.ForeignKey(
        Stacion, 
        on_delete=models.CASCADE,
        verbose_name=_("Station")
    )
    depozite = models.ForeignKey(
        Depozite, 
        on_delete=models.CASCADE,
        verbose_name=_("Tank")
    )
    produkt = models.ForeignKey(
        Produkt, 
        on_delete=models.PROTECT,
        verbose_name=_("Product")
    )
    
    # Quantities
    sasia_e_kerkuar = models.FloatField(
        _("Requested Quantity (L)"), 
        validators=[MinValueValidator(0)]
    )
    sasia_e_miratuar = models.FloatField(
        _("Approved Quantity (L)"), 
        null=True, 
        blank=True
    )
    sasia_e_dorezuar = models.FloatField(
        _("Delivered Quantity (L)"), 
        default=0
    )
    
    # Scheduling
    prioriteti = models.CharField(
        _("Priority"), 
        max_length=10, 
        choices=PRIORITY_CHOICES, 
        default='normale'
    )
    data_afati = models.DateTimeField(
        _("Due Date"),
        help_text=_("Latest acceptable delivery time")
    )
    koha_preferuar_fillimi = models.TimeField(
        _("Preferred Time Start"), 
        null=True, 
        blank=True
    )
    koha_preferuar_mbarimi = models.TimeField(
        _("Preferred Time End"), 
        null=True, 
        blank=True
    )
    
    # Status and tracking
    statusi = models.CharField(
        _("Status"), 
        max_length=15, 
        choices=STATUS_CHOICES, 
        default='e_hapur'
    )
    eshte_automatike = models.BooleanField(
        _("Automatic Order"), 
        default=True,
        help_text=_("Order generated automatically by safety stock rules")
    )
    eshte_emergjente = models.BooleanField(_("Emergency Order"), default=False)
    
    # Metadata
    shenimet = models.TextField(_("Notes"), blank=True)
    krijuar_nga = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name=_("Created By")
    )
    data_krijimit = models.DateTimeField(_("Created"), auto_now_add=True)
    data_perditesimit = models.DateTimeField(_("Updated"), auto_now=True)
    
    class Meta:
        verbose_name = _("Order")
        verbose_name_plural = _("Orders")
        ordering = ['-prioriteti', 'data_afati', 'data_krijimit']
    
    def __str__(self):
        return f"Porosi {self.numri_porosise}: {self.stacion.emri} - {self.produkt.emri}"
    
    @property
    def eshte_urgjente(self):
        """Check if order is urgent based on tank level and time"""
        return (self.depozite.dite_deri_zbrazje <= 1 or 
                self.prioriteti in ['e_larte', 'kritike'] or
                self.eshte_emergjente)
    
    def save(self, *args, **kwargs):
        # Auto-generate order number if not provided
        if not self.numri_porosise:
            today = timezone.now().strftime('%Y%m%d')
            count = Porosi.objects.filter(
                data_krijimit__date=timezone.now().date()
            ).count() + 1
            self.numri_porosise = f"ORD-{today}-{count:04d}"
        
        # Set approved quantity to requested if not set
        if self.sasia_e_miratuar is None:
            self.sasia_e_miratuar = self.sasia_e_kerkuar
            
        super().save(*args, **kwargs)


class PlanRruge(models.Model):
    """Optimized delivery routes for trucks"""
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('miratuar', _('Approved')),
        ('ne_progres', _('In Progress')),
        ('perfunduar', _('Completed')),
        ('anulluar', _('Cancelled')),
    ]
    
    numri_rrugese = models.CharField(
        _("Route Number"), 
        max_length=20, 
        unique=True,
        blank=True
    )
    data_planifikimit = models.DateField(_("Route Date"))
    kamion = models.ForeignKey(
        Kamion, 
        on_delete=models.CASCADE,
        verbose_name=_("Truck")
    )
    shofer = models.ForeignKey(
        Shofer, 
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_("Driver")
    )
    
    # Planning metrics
    koha_nisjes_nga_depo = models.DateTimeField(
        _("Departure Time from Depot"), 
        null=True, 
        blank=True
    )
    distanca_e_planifikuar_km = models.FloatField(
        _("Planned Distance (km)"), 
        null=True, 
        blank=True
    )
    kohezgjatja_e_planifikuar_ore = models.FloatField(
        _("Planned Duration (hours)"), 
        null=True, 
        blank=True
    )
    kostoja_e_parashikuar = models.FloatField(
        _("Estimated Cost"), 
        null=True, 
        blank=True
    )
    
    # Actual performance tracking
    distanca_aktuale_km = models.FloatField(
        _("Actual Distance (km)"), 
        null=True, 
        blank=True
    )
    kohezgjatja_aktuale_ore = models.FloatField(
        _("Actual Duration (hours)"), 
        null=True, 
        blank=True
    )
    kostoja_aktuale = models.FloatField(
        _("Actual Cost"), 
        null=True, 
        blank=True
    )
    koha_aktuale_nisjes = models.DateTimeField(
        _("Actual Start Time"), 
        null=True, 
        blank=True
    )
    koha_aktuale_mbarimit = models.DateTimeField(
        _("Actual End Time"), 
        null=True, 
        blank=True
    )
    
    # Status and optimization
    statusi = models.CharField(
        _("Status"), 
        max_length=15, 
        choices=STATUS_CHOICES, 
        default='draft'
    )
    pikuesi_optimizimit = models.FloatField(
        _("Optimization Score"), 
        null=True, 
        blank=True,
        help_text=_("Algorithm optimization score (lower is better)")
    )
    
    # Metadata
    shenimet = models.TextField(_("Notes"), blank=True)
    krijuar_nga = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        verbose_name=_("Created By")
    )
    data_krijimit = models.DateTimeField(_("Created"), auto_now_add=True)
    data_perditesimit = models.DateTimeField(_("Updated"), auto_now=True)
    
    class Meta:
        verbose_name = _("Route Plan")
        verbose_name_plural = _("Route Plans")
        ordering = ['data_planifikimit', 'kamion']
    
    def __str__(self):
        return f"Rruge {self.numri_rrugese}: {self.kamion.targa} - {self.data_planifikimit}"
    
    @property
    def pikuesi_efikasitetit(self):
        """Calculate route efficiency vs. plan (percentage)"""
        if self.kohezgjatja_e_planifikuar_ore and self.kohezgjatja_aktuale_ore:
            return (self.kohezgjatja_e_planifikuar_ore / self.kohezgjatja_aktuale_ore) * 100
        return None
    
    @property
    def sasia_totale_dorezuar(self):
        """Total quantity delivered on this route"""
        return sum(
            ngarkese.sasia for ndalese in self.ndalesat.all() 
            for ngarkese in ndalese.ngarkesat_shkarkeset.all()
            if ngarkese.sasia < 0  # Negative values are deliveries
        )
    
    def save(self, *args, **kwargs):
        # Auto-generate route number if not provided
        if not self.numri_rrugese:
            today = self.data_planifikimit.strftime('%Y%m%d')
            count = PlanRruge.objects.filter(data_planifikimit=self.data_planifikimit).count() + 1
            self.numri_rrugese = f"RT-{today}-{count:03d}"
            
        super().save(*args, **kwargs)


class Ndalese(models.Model):
    """Individual stops on a delivery route"""
    plan_rruge = models.ForeignKey(
        PlanRruge, 
        on_delete=models.CASCADE, 
        related_name='ndalesat',
        verbose_name=_("Route Plan")
    )
    stacion = models.ForeignKey(
        Stacion, 
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Station")
    )
    depo_qendrore = models.ForeignKey(
        DepoQendrore,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Central Depot")
    )
    sekuenca = models.IntegerField(_("Sequence Number"))
    
    # Planned timing
    koha_parashikuar_mberritjes = models.DateTimeField(_("Planned Arrival"))
    koha_e_parashikuar_sherbimit_min = models.IntegerField(
        _("Est. Service Time (min)"), 
        default=45
    )
    koha_parashikuar_nisjes = models.DateTimeField(_("Planned Departure"))
    
    # Actual timing (for performance tracking)
    koha_aktuale_mberritjes = models.DateTimeField(
        _("Actual Arrival"), 
        null=True, 
        blank=True
    )
    koha_aktuale_nisjes = models.DateTimeField(
        _("Actual Departure"), 
        null=True, 
        blank=True
    )
    
    # Delivery status
    eshte_perfunduar = models.BooleanField(_("Completed"), default=False)
    problemet_dorezimit = models.TextField(
        _("Delivery Issues"), 
        blank=True,
        help_text=_("Any problems encountered during delivery")
    )
    nenshkrimi_marresi = models.TextField(
        _("Recipient Signature"), 
        blank=True,
        help_text=_("Digital signature or recipient name")
    )
    
    # GPS coordinates at delivery (for verification)
    vendndodhja_gps = gis_models.PointField(
        _("GPS Location"), 
        null=True, 
        blank=True
    )
    
    class Meta:
        verbose_name = _("Stop")
        verbose_name_plural = _("Stops")
        unique_together = [['plan_rruge', 'sekuenca']]
        ordering = ['plan_rruge', 'sekuenca']
    
    def __str__(self):
        if self.stacion:
            return f"Ndalese {self.sekuenca}: {self.stacion.emri}"
        elif self.depo_qendrore:
            return f"Ndalese {self.sekuenca}: {self.depo_qendrore.emri}"
        else:
            return f"Ndalese {self.sekuenca}"
    
    @property
    def vonesimi_minuta(self):
        """Calculate delay compared to plan"""
        if self.koha_aktuale_mberritjes and self.koha_parashikuar_mberritjes:
            delta = self.koha_aktuale_mberritjes - self.koha_parashikuar_mberritjes
            return int(delta.total_seconds() / 60)
        return None
    
    @property
    def sasia_totale_dorezuar(self):
        """Total quantity delivered at this stop"""
        return sum(
            abs(ng.sasia) for ng in self.ngarkesat_shkarkeset.all() 
            if ng.sasia < 0  # Negative values are deliveries
        )


class NgarkeseShkarkese(models.Model):
    """Specific product loading/unloading operations within a stop"""
    ndalese = models.ForeignKey(
        Ndalese, 
        on_delete=models.CASCADE, 
        related_name='ngarkesat_shkarkeset',
        verbose_name=_("Stop")
    )
    porosi = models.ForeignKey(
        Porosi, 
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Order")
    )
    particion = models.ForeignKey(
        Particion, 
        on_delete=models.CASCADE,
        verbose_name=_("Compartment")
    )
    produkt = models.ForeignKey(
        Produkt, 
        on_delete=models.PROTECT,
        verbose_name=_("Product")
    )
    depozite = models.ForeignKey(
        Depozite, 
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name=_("Tank")
    )
    
    # Quantities (positive for loading, negative for unloading)
    sasia = models.FloatField(
        _("Quantity (L)"), 
        help_text=_("Positive for loading, negative for unloading")
    )
    sasia_e_dorezuar = models.FloatField(
        _("Delivered Quantity (L)"), 
        default=0
    )
    
    # Quality control
    temperatura_celsius = models.FloatField(
        _("Temperature (°C)"), 
        null=True, 
        blank=True
    )
    densiteti_i_matur = models.FloatField(
        _("Measured Density"), 
        null=True, 
        blank=True
    )
    
    # Delivery details
    leximi_fillimi_kontaliter = models.FloatField(
        _("Start Meter Reading"), 
        null=True, 
        blank=True
    )
    leximi_mbarimi_kontaliter = models.FloatField(
        _("End Meter Reading"), 
        null=True, 
        blank=True
    )
    koha_fillimi_dorezimit = models.DateTimeField(
        _("Delivery Start"), 
        null=True, 
        blank=True
    )
    koha_mbarimi_dorezimit = models.DateTimeField(
        _("Delivery End"), 
        null=True, 
        blank=True
    )
    
    # Status
    eshte_perfunduar = models.BooleanField(_("Completed"), default=False)
    ka_variacion = models.BooleanField(_("Has Variance"), default=False)
    arsyeja_variacionit = models.TextField(
        _("Variance Reason"), 
        blank=True,
        help_text=_("Reason for quantity variance if any")
    )
    
    class Meta:
        verbose_name = _("Loading/Unloading Operation")
        verbose_name_plural = _("Loading/Unloading Operations")
        ordering = ['ndalese', 'produkt']
    
    def __str__(self):
        operation = "Ngarkim" if self.sasia > 0 else "Shkarkim"
        return f"{operation}: {self.produkt.emri} ({abs(self.sasia)}L)"
    
    @property
    def variacion_sasia(self):
        """Calculate variance between planned and delivered"""
        if self.sasia < 0:  # Only for deliveries
            return self.sasia_e_dorezuar - abs(self.sasia)
        return 0
    
    @property
    def variacion_perqindja(self):
        """Calculate variance as percentage"""
        if self.sasia < 0 and abs(self.sasia) > 0:  # Only for deliveries
            return (self.variacion_sasia / abs(self.sasia)) * 100
        return 0
    
    def save(self, *args, **kwargs):
        # Check for significant variance (5% threshold)
        if abs(self.variacion_perqindja) > 5:
            self.ka_variacion = True
        else:
            self.ka_variacion = False
            
        super().save(*args, **kwargs)