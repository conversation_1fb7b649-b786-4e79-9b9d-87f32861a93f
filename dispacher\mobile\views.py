# mobile/views.py - Mobile-friendly views for drivers

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.views.decorators.http import require_POST

from logistics.models import PlanRruge, Ndalese, NgarkeseShkarkese


@login_required
def driver_dashboard(request):
    """Main dashboard for drivers - mobile optimized"""
    
    today = timezone.now().date()
    
    # Get today's routes for this driver
    # Note: In production, you'd need to properly link users to drivers
    driver_routes = PlanRruge.objects.filter(
        data_planifikimit=today,
        statusi__in=['miratuar', 'ne_progres']
    ).select_related('kamion').prefetch_related('ndalesat__stacion')
    
    context = {
        'page_title': _('Driver Dashboard'),
        'routes': driver_routes,
        'today': today,
    }
    
    return render(request, 'mobile/dashboard.html', context)


@login_required
def route_detail(request, route_id):
    """Detailed view of a specific route for drivers"""
    
    route = get_object_or_404(
        PlanRruge.objects.select_related('kamion', 'shofer').prefetch_related(
            'ndalesat__stacion',
            'ndalesat__ngarkesat_shkarkeset__produkt',
            'ndalesat__ngarkesat_shkarkeset__particion'
        ),
        id=route_id
    )
    
    # Calculate progress
    total_stops = route.ndalesat.count()
    completed_stops = route.ndalesat.filter(eshte_perfunduar=True).count()
    progress_percentage = (completed_stops / total_stops * 100) if total_stops > 0 else 0
    
    # Get current stop (next uncompleted stop)
    current_stop = route.ndalesat.filter(eshte_perfunduar=False).order_by('sekuenca').first()
    
    context = {
        'page_title': f"{_('Route')} {route.numri_rrugese}",
        'route': route,
        'progress_percentage': progress_percentage,
        'completed_stops': completed_stops,
        'total_stops': total_stops,
        'current_stop': current_stop,
    }
    
    return render(request, 'mobile/route_detail.html', context)


@login_required
def stop_detail(request, stop_id):
    """Detailed view of a specific stop"""
    
    stop = get_object_or_404(
        Ndalese.objects.select_related('stacion', 'plan_rruge').prefetch_related(
            'ngarkesat_shkarkeset__produkt',
            'ngarkesat_shkarkeset__particion',
            'ngarkesat_shkarkeset__porosi'
        ),
        id=stop_id
    )
    
    context = {
        'page_title': f"{_('Stop')}: {stop.stacion.emri}",
        'stop': stop,
    }
    
    return render(request, 'mobile/stop_detail.html', context)


@login_required
@require_POST
def complete_stop(request, stop_id):
    """Mark a stop as completed"""
    
    stop = get_object_or_404(Ndalese, id=stop_id)
    
    if stop.eshte_perfunduar:
        return JsonResponse({
            'success': False,
            'message': _('Stop is already completed')
        })
    
    # Mark stop as completed
    stop.eshte_perfunduar = True
    stop.koha_aktuale_mberritjes = timezone.now()
    stop.koha_aktuale_nisjes = timezone.now()
    
    # Optional: Get recipient signature
    recipient_signature = request.POST.get('recipient_signature', '')
    if recipient_signature:
        stop.nenshkrimi_marresi = recipient_signature
    
    stop.save()
    
    # Check if this was the last stop on the route
    route = stop.plan_rruge
    remaining_stops = route.ndalesat.filter(eshte_perfunduar=False).count()
    
    if remaining_stops == 0:
        # All stops completed, mark route as completed
        route.statusi = 'perfunduar'
        route.koha_aktuale_mbarimit = timezone.now()
        route.kamion.statusi = 'i_lire'  # Make truck available again
        route.kamion.save()
        route.save()
        
        message = _('Stop completed. Route finished!')
    else:
        message = _('Stop completed successfully')
    
    messages.success(request, message)
    
    return JsonResponse({
        'success': True,
        'message': str(message),
        'remaining_stops': remaining_stops
    })


@login_required
@require_POST
def update_delivery(request, delivery_id):
    """Update delivery quantities"""
    
    delivery = get_object_or_404(NgarkeseShkarkese, id=delivery_id)
    
    delivered_quantity = request.POST.get('delivered_quantity')
    
    if not delivered_quantity:
        return JsonResponse({
            'success': False,
            'message': _('Delivered quantity is required')
        })
    
    try:
        delivered_quantity = float(delivered_quantity)
        
        if delivered_quantity < 0:
            return JsonResponse({
                'success': False,
                'message': _('Quantity cannot be negative')
            })
        
        # Update delivery
        delivery.sasia_e_dorezuar = delivered_quantity
        delivery.eshte_perfunduar = True
        delivery.koha_mbarimi_dorezimit = timezone.now()
        delivery.save()
        
        # Update related order
        if delivery.porosi:
            delivery.porosi.sasia_e_dorezuar += delivered_quantity
            delivery.porosi.statusi = 'e_dorezuar'
            delivery.porosi.save()
        
        # Update tank quantity
        if delivery.depozite:
            delivery.depozite.sasia_aktuale += delivered_quantity
            delivery.depozite.save()
        
        return JsonResponse({
            'success': True,
            'message': _('Delivery updated successfully')
        })
        
    except (ValueError, TypeError):
        return JsonResponse({
            'success': False,
            'message': _('Invalid quantity value')
        })


@login_required
def help_page(request):
    """Help page for drivers"""
    
    context = {
        'page_title': _('Help'),
    }
    
    return render(request, 'mobile/help.html', context)


@login_required
@require_POST
def report_issue(request):
    """Report an issue or problem"""
    
    issue_type = request.POST.get('issue_type')
    description = request.POST.get('description')
    stop_id = request.POST.get('stop_id')
    
    if not description:
        return JsonResponse({
            'success': False,
            'message': _('Description is required')
        })
    
    # In a real implementation, you'd save this to a support ticket system
    # For now, just log it or email administrators
    
    # If related to a specific stop, update the stop
    if stop_id:
        try:
            stop = Ndalese.objects.get(id=stop_id)
            if stop.problemet_dorezimit:
                stop.problemet_dorezimit += f"\n---\n{description}"
            else:
                stop.problemet_dorezimit = description
            stop.save()
        except Ndalese.DoesNotExist:
            pass
    
    return JsonResponse({
        'success': True,
        'message': _('Issue reported successfully. Support will contact you soon.')
    })
