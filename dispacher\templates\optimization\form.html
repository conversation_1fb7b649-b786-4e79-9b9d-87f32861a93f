<!-- templates/optimization/form.html - Optimization Form -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .section-header {
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 1rem;
        margin-bottom: 1.5rem;
    }
    .order-item {
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
    }
    .order-item:hover {
        border-color: #007bff;
        box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    }
    .order-item.selected {
        border-color: #007bff;
        background-color: #f8f9ff;
    }
    .truck-item {
        border: 1px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 0.75rem;
        transition: all 0.3s ease;
    }
    .truck-item:hover {
        border-color: #28a745;
        box-shadow: 0 2px 8px rgba(40, 167, 69, 0.15);
    }
    .truck-item.selected {
        border-color: #28a745;
        background-color: #f8fff9;
    }
    .priority-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
    }
    .optimization-preview {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 2rem;
    }
    .metric-box {
        text-align: center;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    .metric-label {
        font-size: 0.8rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-cog text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'optimization:dashboard' %}">Optimization</a></li>
            <li class="breadcrumb-item active">New Optimization</li>
        </ol>
    </nav>

    <form id="optimizationForm" method="post">
        {% csrf_token %}
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Settings -->
                <div class="form-section">
                    <div class="section-header">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>Basic Settings
                        </h5>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Target Date</label>
                            <input type="date" class="form-control" name="target_date" 
                                   value="{{ target_date|date:'Y-m-d' }}" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Priority Level</label>
                            <select class="form-select" name="priority">
                                <option value="normal">Normal</option>
                                <option value="high">High Priority</option>
                                <option value="emergency">Emergency</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Optimization Type</label>
                            <select class="form-select" name="optimization_type">
                                <option value="distance">Minimize Distance</option>
                                <option value="time">Minimize Time</option>
                                <option value="balanced" selected>Balanced</option>
                                <option value="fuel">Minimize Fuel Cost</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Order Selection -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Select Orders
                            </h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllOrders()">
                                    Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearOrderSelection()">
                                    Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    {% if open_orders %}
                        {% for order in open_orders %}
                        <div class="order-item" onclick="toggleOrder(this, {{ order.id }})">
                            <input type="checkbox" name="orders" value="{{ order.id }}" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <h6 class="mb-1">{{ order.stacion.emri }}</h6>
                                    <small class="text-muted">{{ order.stacion.adresa|truncatechars:40 }}</small>
                                </div>
                                <div class="col-md-2">
                                    <strong>{{ order.produkt.emri }}</strong>
                                </div>
                                <div class="col-md-2">
                                    <strong>{{ order.sasia_e_kerkuar|floatformat:0 }}L</strong>
                                </div>
                                <div class="col-md-2">
                                    <span class="priority-badge bg-{{ order.priority_color }}">
                                        {{ order.get_prioriteti_display }}
                                    </span>
                                </div>
                                <div class="col-md-2">
                                    {% if order.data_afati %}
                                        <small class="text-muted">
                                            Due: {{ order.data_afati|date:"M d" }}
                                        </small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No open orders for this date</h6>
                            <p class="text-muted">Select a different date or create new orders</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Truck Selection -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-truck me-2"></i>Select Trucks
                            </h5>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllTrucks()">
                                    Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearTruckSelection()">
                                    Clear All
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    {% if available_trucks %}
                        {% for truck in available_trucks %}
                        <div class="truck-item" onclick="toggleTruck(this, {{ truck.id }})">
                            <input type="checkbox" name="trucks" value="{{ truck.id }}" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-3">
                                    <h6 class="mb-1">{{ truck.emri }}</h6>
                                    <small class="text-muted">{{ truck.numri_targave }}</small>
                                </div>
                                <div class="col-md-2">
                                    <strong>{{ truck.kapaciteti_total_litra|floatformat:0 }}L</strong>
                                    <br><small class="text-muted">Capacity</small>
                                </div>
                                <div class="col-md-2">
                                    <strong>{{ truck.particionet.count }}</strong>
                                    <br><small class="text-muted">Compartments</small>
                                </div>
                                <div class="col-md-3">
                                    {% if truck.shofer_aktual %}
                                        <strong>{{ truck.shofer_aktual.emri_i_plote }}</strong>
                                        <br><small class="text-muted">Driver</small>
                                    {% else %}
                                        <span class="text-warning">No driver assigned</span>
                                    {% endif %}
                                </div>
                                <div class="col-md-2">
                                    <span class="badge bg-success">Available</span>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">No available trucks</h6>
                            <p class="text-muted">All trucks are currently assigned or in maintenance</p>
                        </div>
                    {% endif %}
                </div>

                <!-- Advanced Options -->
                <div class="form-section">
                    <div class="section-header">
                        <h5 class="mb-0">
                            <i class="fas fa-sliders-h me-2"></i>Advanced Options
                        </h5>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="consider_traffic" checked>
                                <label class="form-check-label">Consider traffic patterns</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="balance_loads" checked>
                                <label class="form-check-label">Balance truck loads</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="minimize_empty_runs">
                                <label class="form-check-label">Minimize empty runs</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="respect_time_windows" checked>
                                <label class="form-check-label">Respect delivery time windows</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="cluster_by_region">
                                <label class="form-check-label">Cluster by geographic region</label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" name="allow_partial_loads">
                                <label class="form-check-label">Allow partial truck loads</label>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Additional Notes</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="Any special requirements or constraints..."></textarea>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Optimization Preview -->
                <div class="optimization-preview">
                    <h5 class="mb-3">
                        <i class="fas fa-eye me-2"></i>Optimization Preview
                    </h5>
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value" id="selectedOrders">0</div>
                                <div class="metric-label">Orders Selected</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value" id="selectedTrucks">0</div>
                                <div class="metric-label">Trucks Selected</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value" id="totalVolume">0</div>
                                <div class="metric-label">Total Volume (L)</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="metric-box">
                                <div class="metric-value" id="totalCapacity">0</div>
                                <div class="metric-label">Total Capacity (L)</div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="progress">
                            <div class="progress-bar" id="capacityUtilization" style="width: 0%"></div>
                        </div>
                        <small class="text-white-50">Capacity Utilization</small>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-4">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-play me-2"></i>Start Optimization
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewRoutes()">
                            <i class="fas fa-eye me-2"></i>Preview Routes
                        </button>
                        <a href="{% url 'optimization:dashboard' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>

                <!-- Optimization Tips -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-lightbulb me-2"></i>Tips
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Select orders with similar priorities together</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Ensure truck capacity matches order volume</small>
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Consider geographic clustering for efficiency</small>
                            </li>
                            <li class="mb-0">
                                <i class="fas fa-check text-success me-2"></i>
                                <small>Enable traffic consideration for better timing</small>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedOrders = new Set();
let selectedTrucks = new Set();
let orderData = {};
let truckData = {};

// Initialize order and truck data
{% for order in open_orders %}
orderData[{{ order.id }}] = {
    volume: {{ order.sasia_e_kerkuar }},
    priority: '{{ order.prioriteti }}'
};
{% endfor %}

{% for truck in available_trucks %}
truckData[{{ truck.id }}] = {
    capacity: {{ truck.kapaciteti_total_litra }},
    name: '{{ truck.emri }}'
};
{% endfor %}

function toggleOrder(element, orderId) {
    const checkbox = element.querySelector('input[type="checkbox"]');
    
    if (selectedOrders.has(orderId)) {
        selectedOrders.delete(orderId);
        element.classList.remove('selected');
        checkbox.checked = false;
    } else {
        selectedOrders.add(orderId);
        element.classList.add('selected');
        checkbox.checked = true;
    }
    
    updatePreview();
}

function toggleTruck(element, truckId) {
    const checkbox = element.querySelector('input[type="checkbox"]');
    
    if (selectedTrucks.has(truckId)) {
        selectedTrucks.delete(truckId);
        element.classList.remove('selected');
        checkbox.checked = false;
    } else {
        selectedTrucks.add(truckId);
        element.classList.add('selected');
        checkbox.checked = true;
    }
    
    updatePreview();
}

function selectAllOrders() {
    document.querySelectorAll('.order-item').forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const orderId = parseInt(checkbox.value);
        selectedOrders.add(orderId);
        item.classList.add('selected');
        checkbox.checked = true;
    });
    updatePreview();
}

function clearOrderSelection() {
    selectedOrders.clear();
    document.querySelectorAll('.order-item').forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        item.classList.remove('selected');
        checkbox.checked = false;
    });
    updatePreview();
}

function selectAllTrucks() {
    document.querySelectorAll('.truck-item').forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        const truckId = parseInt(checkbox.value);
        selectedTrucks.add(truckId);
        item.classList.add('selected');
        checkbox.checked = true;
    });
    updatePreview();
}

function clearTruckSelection() {
    selectedTrucks.clear();
    document.querySelectorAll('.truck-item').forEach(item => {
        const checkbox = item.querySelector('input[type="checkbox"]');
        item.classList.remove('selected');
        checkbox.checked = false;
    });
    updatePreview();
}

function updatePreview() {
    // Calculate totals
    let totalVolume = 0;
    let totalCapacity = 0;
    
    selectedOrders.forEach(orderId => {
        if (orderData[orderId]) {
            totalVolume += orderData[orderId].volume;
        }
    });
    
    selectedTrucks.forEach(truckId => {
        if (truckData[truckId]) {
            totalCapacity += truckData[truckId].capacity;
        }
    });
    
    // Update display
    document.getElementById('selectedOrders').textContent = selectedOrders.size;
    document.getElementById('selectedTrucks').textContent = selectedTrucks.size;
    document.getElementById('totalVolume').textContent = Math.round(totalVolume);
    document.getElementById('totalCapacity').textContent = Math.round(totalCapacity);
    
    // Update capacity utilization
    const utilization = totalCapacity > 0 ? (totalVolume / totalCapacity * 100) : 0;
    const utilizationBar = document.getElementById('capacityUtilization');
    utilizationBar.style.width = Math.min(utilization, 100) + '%';
    
    if (utilization > 100) {
        utilizationBar.classList.remove('bg-success', 'bg-warning');
        utilizationBar.classList.add('bg-danger');
    } else if (utilization > 80) {
        utilizationBar.classList.remove('bg-success', 'bg-danger');
        utilizationBar.classList.add('bg-warning');
    } else {
        utilizationBar.classList.remove('bg-warning', 'bg-danger');
        utilizationBar.classList.add('bg-success');
    }
}

function previewRoutes() {
    if (selectedOrders.size === 0 || selectedTrucks.size === 0) {
        alert('Please select at least one order and one truck to preview routes.');
        return;
    }
    
    // This would typically open a modal or redirect to a preview page
    alert('Route preview feature coming soon!');
}

// Form submission validation
document.getElementById('optimizationForm').addEventListener('submit', function(e) {
    if (selectedOrders.size === 0) {
        e.preventDefault();
        alert('Please select at least one order to optimize.');
        return;
    }
    
    if (selectedTrucks.size === 0) {
        e.preventDefault();
        alert('Please select at least one truck for optimization.');
        return;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Starting Optimization...';
    submitBtn.disabled = true;
});

// Initialize preview
updatePreview();
</script>
{% endblock %}
