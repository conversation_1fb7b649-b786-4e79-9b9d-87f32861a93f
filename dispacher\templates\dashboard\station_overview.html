<!-- templates/dashboard/station_overview.html - Station Overview Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .station-card {
        transition: transform 0.2s;
    }
    .station-card:hover {
        transform: translateY(-2px);
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .map-container {
        height: 400px;
        border-radius: 0.375rem;
    }
    .tank-indicator {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-gas-pump text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="get" class="d-flex gap-2">
                <input type="text" name="search" class="form-control" 
                       placeholder="Search stations by name, code, or address..." 
                       value="{{ search|default:'' }}">
                <select name="region" class="form-select" style="max-width: 200px;">
                    <option value="">All Regions</option>
                    <option value="Tirana" {% if region == 'Tirana' %}selected{% endif %}>Tirana</option>
                    <option value="Durres" {% if region == 'Durres' %}selected{% endif %}>Durres</option>
                    <option value="Vlore" {% if region == 'Vlore' %}selected{% endif %}>Vlore</option>
                    <option value="Shkoder" {% if region == 'Shkoder' %}selected{% endif %}>Shkoder</option>
                </select>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="{% url 'dashboard:station_overview' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </form>
        </div>
        <div class="col-md-4 text-end">
            <span class="text-muted">Total: {{ total_stations }} stations</span>
        </div>
    </div>

    <!-- Map Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marked-alt"></i> Station Locations
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="stationsMap" class="map-container"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stations Grid -->
    <div class="row">
        {% for station in stations %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card station-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="card-title mb-0">
                        <strong>{{ station.emri }}</strong>
                        <small class="text-muted">({{ station.kodi }})</small>
                    </h6>
                    {% if station.critical_tanks > 0 %}
                        <span class="badge bg-danger status-badge">Critical</span>
                    {% elif station.low_tanks > 0 %}
                        <span class="badge bg-warning status-badge">Low Stock</span>
                    {% else %}
                        <span class="badge bg-success status-badge">Normal</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-2">
                        <i class="fas fa-map-marker-alt"></i> {{ station.adresa|truncatechars:50 }}
                    </p>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="text-primary">
                                <i class="fas fa-gas-pump"></i>
                            </div>
                            <small class="text-muted">{{ station.total_tanks }} Tanks</small>
                        </div>
                        <div class="col-4">
                            <div class="text-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <small class="text-muted">{{ station.low_tanks }} Low</small>
                        </div>
                        <div class="col-4">
                            <div class="text-danger">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <small class="text-muted">{{ station.critical_tanks }} Critical</small>
                        </div>
                    </div>

                    <!-- Tank Status Indicators -->
                    <div class="mt-3">
                        <small class="text-muted">Tank Status:</small><br>
                        {% for tank in station.depozitat.all %}
                            {% if tank.sasia_aktuale <= tank.niveli_minimal_sigurise %}
                                <span class="tank-indicator bg-danger" title="{{ tank.produkt.emri }} - Critical"></span>
                            {% elif tank.sasia_aktuale <= tank.niveli_i_porosise %}
                                <span class="tank-indicator bg-warning" title="{{ tank.produkt.emri }} - Low"></span>
                            {% else %}
                                <span class="tank-indicator bg-success" title="{{ tank.produkt.emri }} - Normal"></span>
                            {% endif %}
                        {% empty %}
                            <span class="text-muted">No tanks configured</span>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="#" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-success">
                            <i class="fas fa-truck"></i> Schedule Delivery
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-gas-pump fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No stations found</h5>
                <p class="text-muted">Try adjusting your search criteria or add new stations.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if stations.has_other_pages %}
    <div class="row">
        <div class="col-12">
            <nav aria-label="Station pagination">
                <ul class="pagination justify-content-center">
                    {% if stations.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ stations.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if region %}&region={{ region }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    {% for num in stations.paginator.page_range %}
                        {% if stations.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if region %}&region={{ region }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if stations.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ stations.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if region %}&region={{ region }}{% endif %}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize map
var map = L.map('stationsMap').setView([41.3275, 19.8187], 8); // Centered on Albania

// Add tile layer
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors'
}).addTo(map);

// Station data from Django
var stationsData = {{ stations_map_data|safe }};

// Add markers for each station
stationsData.forEach(function(station) {
    var marker = L.circleMarker([station.lat, station.lng], {
        radius: 8,
        fillColor: station.color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    }).addTo(map);
    
    // Popup content
    var popupContent = `
        <div class="station-popup">
            <h6>${station.name}</h6>
            <p class="mb-1"><strong>Code:</strong> ${station.code}</p>
            <p class="mb-1"><strong>Status:</strong> <span class="badge bg-${station.status === 'critical' ? 'danger' : station.status === 'warning' ? 'warning' : 'success'}">${station.status}</span></p>
            <p class="mb-1"><strong>Tanks:</strong> ${station.total_tanks} total, ${station.critical_tanks} critical</p>
            <p class="mb-0"><small>${station.address}</small></p>
        </div>
    `;
    
    marker.bindPopup(popupContent);
});

// Fit map to show all markers
if (stationsData.length > 0) {
    var group = new L.featureGroup(map._layers);
    if (Object.keys(group._layers).length > 0) {
        map.fitBounds(group.getBounds().pad(0.1));
    }
}
</script>
{% endblock %}
