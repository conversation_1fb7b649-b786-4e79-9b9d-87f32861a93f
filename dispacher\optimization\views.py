# optimization/views.py - Views for route optimization interface

import json
from datetime import datetime, timedelta

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.views.decorators.http import require_http_methods, require_POST
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum

from celery.result import AsyncResult

from logistics.models import <PERSON><PERSON><PERSON>, Kamion, PlanRruge, Stacion
from .tasks import optimize_routes_for_date, generate_emergency_optimization


@login_required
def optimization_dashboard(request):
    """Main optimization dashboard"""
    
    # Get today's date and next few days
    today = timezone.now().date()
    date_range = [today + timedelta(days=i) for i in range(7)]
    
    # Get optimization statistics
    stats = {}
    for date in date_range:
        open_orders = Porosi.objects.filter(
            statusi='e_hapur',
            data_afati__date=date
        ).count()
        
        existing_routes = PlanRruge.objects.filter(
            data_planifikimit=date
        ).count()
        
        stats[date.isoformat()] = {
            'open_orders': open_orders,
            'existing_routes': existing_routes,
            'has_optimization': existing_routes > 0
        }
    
    # Get available trucks
    available_trucks = Kamion.objects.filter(
        statusi='i_lire',
        eshte_aktiv=True
    ).count()
    
    # Get recent optimization results
    recent_optimizations = PlanRruge.objects.filter(
        data_krijimit__gte=timezone.now() - timedelta(days=7)
    ).select_related('kamion', 'krijuar_nga').order_by('-data_krijimit')[:10]
    
    context = {
        'page_title': _('Route Optimization Dashboard'),
        'date_range': date_range,
        'optimization_stats': stats,
        'available_trucks': available_trucks,
        'recent_optimizations': recent_optimizations,
        'today': today,
    }
    
    return render(request, 'optimization/dashboard.html', context)


@login_required
def optimization_form(request):
    """Form for starting new optimization"""
    
    if request.method == 'POST':
        # Get form data
        target_date_str = request.POST.get('target_date')
        truck_ids = request.POST.getlist('trucks')
        order_ids = request.POST.getlist('orders')
        
        if not target_date_str:
            messages.error(request, _('Please select a target date'))
            return redirect('optimization:form')
        
        try:
            target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        except ValueError:
            messages.error(request, _('Invalid date format'))
            return redirect('optimization:form')
        
        # Check if optimization already exists for this date
        existing_routes = PlanRruge.objects.filter(
            data_planifikimit=target_date,
            statusi__in=['draft', 'miratuar', 'ne_progres']
        )
        
        if existing_routes.exists():
            messages.warning(
                request, 
                _('Routes already exist for {}. Please delete them first or choose a different date.').format(target_date)
            )
            return redirect('optimization:form')
        
        # Convert to integers
        truck_ids = [int(tid) for tid in truck_ids if tid.isdigit()]
        order_ids = [int(oid) for oid in order_ids if oid.isdigit()]
        
        # Start optimization task
        task = optimize_routes_for_date.delay(
            target_date_str,
            user_id=request.user.id,
            truck_ids=truck_ids if truck_ids else None,
            order_ids=order_ids if order_ids else None
        )
        
        # Store task ID in session for progress tracking
        request.session['optimization_task_id'] = task.id
        request.session['optimization_date'] = target_date_str
        
        messages.success(
            request, 
            _('Route optimization started. You can monitor progress on the dashboard.')
        )
        
        return redirect('optimization:progress', task_id=task.id)
    
    # GET request - show form
    # Get next 7 days for date selection
    today = timezone.now().date()
    date_options = [today + timedelta(days=i) for i in range(7)]
    
    # Get available trucks
    available_trucks = Kamion.objects.filter(
        statusi='i_lire',
        eshte_aktiv=True
    ).select_related('shofer_aktual')
    
    # Get open orders for next week
    open_orders = Porosi.objects.filter(
        statusi='e_hapur',
        data_afati__date__gte=today,
        data_afati__date__lte=today + timedelta(days=7)
    ).select_related('stacion', 'produkt', 'depozite').order_by('data_afati', 'prioriteti')
    
    context = {
        'page_title': _('Start Route Optimization'),
        'date_options': date_options,
        'available_trucks': available_trucks,
        'open_orders': open_orders,
        'today': today,
    }
    
    return render(request, 'optimization/form.html', context)


@login_required
def optimization_progress(request, task_id):
    """Show optimization progress"""
    
    task = AsyncResult(task_id)
    
    context = {
        'page_title': _('Optimization Progress'),
        'task_id': task_id,
        'task_state': task.state,
    }
    
    if task.state == 'PENDING':
        context.update({
            'status': _('Waiting to start...'),
            'current': 0,
            'total': 100
        })
    elif task.state == 'PROGRESS':
        context.update({
            'status': task.info.get('status', ''),
            'current': task.info.get('current', 0),
            'total': task.info.get('total', 100)
        })
    elif task.state == 'SUCCESS':
        result = task.result
        context.update({
            'status': _('Completed successfully'),
            'current': 100,
            'total': 100,
            'result': result
        })
    else:  # FAILURE
        context.update({
            'status': _('Optimization failed'),
            'current': 0,
            'total': 100,
            'error': str(task.info) if task.info else _('Unknown error')
        })
    
    return render(request, 'optimization/progress.html', context)


@login_required
def optimization_progress_api(request, task_id):
    """API endpoint for getting optimization progress"""
    
    task = AsyncResult(task_id)
    
    if task.state == 'PENDING':
        response = {
            'state': task.state,
            'status': _('Waiting to start...'),
            'current': 0,
            'total': 100
        }
    elif task.state == 'PROGRESS':
        response = {
            'state': task.state,
            'status': task.info.get('status', ''),
            'current': task.info.get('current', 0),
            'total': task.info.get('total', 100)
        }
    elif task.state == 'SUCCESS':
        response = {
            'state': task.state,
            'status': _('Completed successfully'),
            'current': 100,
            'total': 100,
            'result': task.result
        }
    else:  # FAILURE
        response = {
            'state': task.state,
            'status': _('Optimization failed'),
            'current': 0,
            'total': 100,
            'error': str(task.info) if task.info else _('Unknown error')
        }
    
    return JsonResponse(response)


@login_required
def route_list(request):
    """List optimized routes with filtering"""
    
    routes = PlanRruge.objects.select_related('kamion', 'shofer', 'krijuar_nga').order_by('-data_planifikimit', 'kamion')
    
    # Filter by date range
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            routes = routes.filter(data_planifikimit__gte=date_from)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            routes = routes.filter(data_planifikimit__lte=date_to)
        except ValueError:
            pass
    
    # Filter by truck
    truck_id = request.GET.get('truck')
    if truck_id and truck_id.isdigit():
        routes = routes.filter(kamion_id=truck_id)
    
    # Filter by status
    status = request.GET.get('status')
    if status:
        routes = routes.filter(statusi=status)
    
    # Pagination
    paginator = Paginator(routes, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Get filter options
    trucks = Kamion.objects.filter(eshte_aktiv=True).order_by('targa')
    status_choices = PlanRruge.STATUS_CHOICES
    
    context = {
        'page_title': _('Optimized Routes'),
        'routes': page_obj,
        'trucks': trucks,
        'status_choices': status_choices,
        'filters': {
            'date_from': date_from.strftime('%Y-%m-%d') if date_from else '',
            'date_to': date_to.strftime('%Y-%m-%d') if date_to else '',
            'truck': truck_id,
            'status': status,
        }
    }
    
    return render(request, 'optimization/route_list.html', context)


@login_required
def route_detail(request, route_id):
    """Detailed view of a specific route"""
    
    route = get_object_or_404(
        PlanRruge.objects.select_related('kamion', 'shofer', 'krijuar_nga'),
        id=route_id
    )
    
    # Get stops with deliveries
    stops = route.ndalesat.select_related('stacion').prefetch_related(
        'ngarkesat_shkarkeset__porosi',
        'ngarkesat_shkarkeset__particion',
        'ngarkesat_shkarkeset__produkt'
    ).order_by('sekuenca')
    
    # Calculate route statistics
    total_deliveries = sum(
        abs(ng.sasia) for stop in stops 
        for ng in stop.ngarkesat_shkarkeset.all() 
        if ng.sasia < 0
    )
    
    completed_stops = stops.filter(eshte_perfunduar=True).count()
    total_stops = stops.count()
    
    context = {
        'page_title': f"{_('Route')} {route.numri_rrugese}",
        'route': route,
        'stops': stops,
        'total_deliveries': total_deliveries,
        'completion_percentage': (completed_stops / total_stops * 100) if total_stops > 0 else 0,
    }
    
    return render(request, 'optimization/route_detail.html', context)


@login_required
@require_POST
def delete_route(request, route_id):
    """Delete a route plan"""
    
    route = get_object_or_404(PlanRruge, id=route_id)
    
    # Only allow deletion of draft routes
    if route.statusi != 'draft':
        messages.error(request, _('Only draft routes can be deleted'))
        return redirect('optimization:route_detail', route_id=route_id)
    
    # Update related orders back to 'open' status
    related_orders = Porosi.objects.filter(
        ngarkeseShkarkese__ndalese__plan_rruge=route
    ).distinct()
    
    related_orders.update(statusi='e_hapur')
    
    # Delete the route (cascades to stops and operations)
    route_number = route.numri_rrugese
    route.delete()
    
    messages.success(request, _('Route {} deleted successfully').format(route_number))
    
    return redirect('optimization:route_list')


@login_required
@require_POST
def approve_route(request, route_id):
    """Approve a draft route"""
    
    route = get_object_or_404(PlanRruge, id=route_id)
    
    if route.statusi != 'draft':
        messages.error(request, _('Route is not in draft status'))
        return redirect('optimization:route_detail', route_id=route_id)
    
    route.statusi = 'miratuar'
    route.save()
    
    messages.success(request, _('Route {} approved successfully').format(route.numri_rrugese))
    
    return redirect('optimization:route_detail', route_id=route_id)


@login_required
@require_POST
def start_emergency_optimization(request):
    """Start emergency optimization for critical orders"""
    
    task = generate_emergency_optimization.delay()
    
    messages.info(
        request, 
        _('Emergency optimization started. Task ID: {}').format(task.id)
    )
    
    return JsonResponse({
        'success': True,
        'task_id': task.id,
        'message': _('Emergency optimization started')
    })


@login_required
def optimization_settings(request):
    """Optimization settings and configuration"""
    
    if request.method == 'POST':
        # Handle settings update
        # This would update optimization parameters
        messages.success(request, _('Settings updated successfully'))
        return redirect('optimization:settings')
    
    context = {
        'page_title': _('Optimization Settings'),
        'current_settings': {
            'max_route_duration': 10,
            'max_working_hours': 8,
            'default_service_time': 45,
            'optimization_timeout': 300,
        }
    }
    
    return render(request, 'optimization/settings.html', context)


@login_required
def quick_optimize(request):
    """Quick optimization for today's urgent orders"""
    
    today = timezone.now().date()
    
    # Get urgent orders for today
    urgent_orders = Porosi.objects.filter(
        statusi='e_hapur',
        prioriteti__in=['e_larte', 'kritike'],
        data_afati__date=today
    ).values_list('id', flat=True)
    
    if not urgent_orders:
        return JsonResponse({
            'success': False,
            'message': _('No urgent orders found for today')
        })
    
    # Start optimization for urgent orders only
    task = optimize_routes_for_date.delay(
        today.strftime('%Y-%m-%d'),
        user_id=request.user.id,
        order_ids=list(urgent_orders)
    )
    
    return JsonResponse({
        'success': True,
        'task_id': task.id,
        'message': _('Quick optimization started for {} urgent orders').format(len(urgent_orders))
    })
