#!/usr/bin/env python
"""
Data viewer for Albanian OptiKarburant sample data
This script provides a simple interface to explore the created data
"""

import sqlite3
import os

def connect_to_database():
    """Connect to the Albanian sample data database"""
    db_path = 'albania_sample_data.db'
    if not os.path.exists(db_path):
        print("❌ Database file not found. Please run create_albania_data.py first.")
        return None
    
    return sqlite3.connect(db_path)

def show_products(conn):
    """Display all fuel products"""
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM products')
    products = cursor.fetchall()
    
    print("\n🛢️  FUEL PRODUCTS")
    print("=" * 50)
    for product in products:
        id, emri, densiteti, ngjyra_kodi, eshte_aktiv, data_krijimit = product
        status = "✅ Active" if eshte_aktiv else "❌ Inactive"
        print(f"• {emri}")
        print(f"  Density: {densiteti} kg/L | Color: {ngjyra_kodi} | {status}")

def show_stations(conn):
    """Display all fuel stations"""
    cursor = conn.cursor()
    cursor.execute('''
        SELECT emri, kodi, city, station_type, latitude, longitude, 
               orar_pranimi_nga, orar_pranimi_deri, kerkon_pompe, kerkon_kontaliter,
               max_kamione_njekohesisht, menaxher_emri, telefoni
        FROM stations 
        ORDER BY city, emri
    ''')
    stations = cursor.fetchall()
    
    print("\n⛽ FUEL STATIONS")
    print("=" * 80)
    current_city = None
    for station in stations:
        emri, kodi, city, station_type, lat, lng, start_time, end_time, pump, meter, max_trucks, manager, phone = station
        
        if city != current_city:
            print(f"\n📍 {city.upper()}")
            print("-" * 40)
            current_city = city
        
        pump_str = "🔧 Pump" if pump else ""
        meter_str = "📊 Meter" if meter else ""
        equipment = f" ({pump_str} {meter_str})".strip()
        
        print(f"• {emri} ({kodi}) - {station_type.title()}{equipment}")
        print(f"  📍 {lat:.4f}, {lng:.4f}")
        print(f"  🕐 {start_time} - {end_time} | Max trucks: {max_trucks}")
        print(f"  👤 {manager} | 📞 {phone}")

def show_drivers(conn):
    """Display all drivers"""
    cursor = conn.cursor()
    cursor.execute('''
        SELECT emri, mbiemri, telefoni, leje_drejtimi_numri, leje_adr, 
               experience_level, ore_punes_maksimale_ditor, ore_drejtimi_maksimale_ditor
        FROM drivers 
        ORDER BY experience_level DESC, emri
    ''')
    drivers = cursor.fetchall()
    
    print("\n🚛 DRIVERS")
    print("=" * 60)
    for driver in drivers:
        emri, mbiemri, telefoni, license, adr, experience, work_hours, drive_hours = driver
        adr_str = "🚨 ADR" if adr else ""
        
        print(f"• {emri} {mbiemri} ({experience.title()}) {adr_str}")
        print(f"  📞 {telefoni} | License: {license}")
        print(f"  ⏰ Work: {work_hours}h/day | Drive: {drive_hours}h/day")

def show_trucks(conn):
    """Display all trucks"""
    cursor = conn.cursor()
    cursor.execute('''
        SELECT t.targa, t.modeli, t.viti_prodhimit, t.truck_type, t.ka_pompe, t.ka_kontaliter,
               t.pesha_maksimale_bruto_ton, t.gjatesia_totale_m, t.odometri_aktual_km,
               d.emri, d.mbiemri
        FROM trucks t
        LEFT JOIN drivers d ON t.shofer_id = d.id
        ORDER BY t.truck_type, t.targa
    ''')
    trucks = cursor.fetchall()
    
    print("\n🚚 TRUCK FLEET")
    print("=" * 70)
    current_type = None
    for truck in trucks:
        targa, modeli, year, truck_type, pump, meter, weight, length, odometer, driver_name, driver_surname = truck
        
        if truck_type != current_type:
            print(f"\n🔧 {truck_type.upper()} TRUCKS")
            print("-" * 30)
            current_type = truck_type
        
        driver_info = f"{driver_name} {driver_surname}" if driver_name else "No driver assigned"
        pump_str = "🔧" if pump else ""
        meter_str = "📊" if meter else ""
        equipment = f"{pump_str}{meter_str}".strip()
        
        print(f"• {targa} - {modeli} ({year}) {equipment}")
        print(f"  👤 {driver_info}")
        print(f"  📏 {weight}t, {length}m | 🛣️ {odometer:,} km")

def show_tanks_summary(conn):
    """Display tank summary by station"""
    cursor = conn.cursor()
    cursor.execute('''
        SELECT s.emri, s.city, COUNT(t.id) as tank_count,
               SUM(t.kapaciteti_total) as total_capacity,
               SUM(t.sasia_aktuale) as current_stock,
               ROUND(AVG(t.sasia_aktuale * 100.0 / t.kapaciteti_total), 1) as avg_fill_percent
        FROM stations s
        LEFT JOIN tanks t ON s.id = t.station_id
        GROUP BY s.id, s.emri, s.city
        ORDER BY s.city, s.emri
    ''')
    tanks = cursor.fetchall()
    
    print("\n🛢️  TANK INVENTORY SUMMARY")
    print("=" * 80)
    current_city = None
    total_capacity = 0
    total_stock = 0
    
    for tank in tanks:
        emri, city, tank_count, capacity, stock, fill_percent = tank
        
        if city != current_city:
            print(f"\n📍 {city.upper()}")
            print("-" * 50)
            current_city = city
        
        capacity = capacity or 0
        stock = stock or 0
        fill_percent = fill_percent or 0
        
        total_capacity += capacity
        total_stock += stock
        
        print(f"• {emri}")
        print(f"  🛢️ {tank_count} tanks | 📊 {capacity:,.0f}L capacity | {stock:,.0f}L stock ({fill_percent}% full)")
    
    print(f"\n📊 TOTAL NETWORK:")
    print(f"   Capacity: {total_capacity:,.0f} L")
    print(f"   Stock: {total_stock:,.0f} L")
    print(f"   Fill Rate: {(total_stock/total_capacity*100):.1f}%")

def show_critical_tanks(conn):
    """Display tanks that need attention"""
    cursor = conn.cursor()
    cursor.execute('''
        SELECT s.emri, s.city, p.emri as product, t.numri_tankut,
               t.kapaciteti_total, t.sasia_aktuale, t.niveli_minimal_sigurise,
               ROUND(t.sasia_aktuale * 100.0 / t.kapaciteti_total, 1) as fill_percent
        FROM tanks t
        JOIN stations s ON t.station_id = s.id
        JOIN products p ON t.product_id = p.id
        WHERE t.sasia_aktuale <= t.niveli_i_porosise
        ORDER BY (t.sasia_aktuale / t.kapaciteti_total)
    ''')
    critical_tanks = cursor.fetchall()
    
    print("\n⚠️  TANKS NEEDING ATTENTION")
    print("=" * 70)
    
    if not critical_tanks:
        print("✅ All tanks are adequately stocked!")
        return
    
    for tank in critical_tanks:
        emri, city, product, tank_num, capacity, stock, safety_level, fill_percent = tank
        
        if stock <= safety_level:
            status = "🚨 CRITICAL"
        else:
            status = "⚠️ LOW"
        
        print(f"{status} {emri} ({city}) - {tank_num}")
        print(f"   Product: {product}")
        print(f"   Stock: {stock:,.0f}L / {capacity:,.0f}L ({fill_percent}% full)")

def main():
    """Main function to display data"""
    print("🇦🇱 OptiKarburant Albania - Data Viewer")
    print("=" * 60)
    
    conn = connect_to_database()
    if not conn:
        return
    
    try:
        while True:
            print("\n📋 MENU:")
            print("1. 🛢️  View Products")
            print("2. ⛽ View Stations")
            print("3. 🚛 View Drivers")
            print("4. 🚚 View Trucks")
            print("5. 🛢️  View Tank Summary")
            print("6. ⚠️  View Critical Tanks")
            print("7. 📊 View All Data")
            print("0. Exit")
            
            choice = input("\nSelect option (0-7): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                show_products(conn)
            elif choice == '2':
                show_stations(conn)
            elif choice == '3':
                show_drivers(conn)
            elif choice == '4':
                show_trucks(conn)
            elif choice == '5':
                show_tanks_summary(conn)
            elif choice == '6':
                show_critical_tanks(conn)
            elif choice == '7':
                show_products(conn)
                show_stations(conn)
                show_drivers(conn)
                show_trucks(conn)
                show_tanks_summary(conn)
                show_critical_tanks(conn)
            else:
                print("❌ Invalid option. Please try again.")
            
            input("\nPress Enter to continue...")
    
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
