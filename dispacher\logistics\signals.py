# logistics/signals.py - Django signals for automated operations

import logging
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import timedelta

from .models import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PlanRruge, Ndalese

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Depozite)
def check_tank_level_and_create_order(sender, instance, created, **kwargs):
    """
    Automatically create order when tank level drops below reorder point
    """
    if not created:  # Only check on updates, not initial creation
        tank = instance
        
        # Check if tank needs refill and doesn't already have an open order
        if tank.nevojitet_rifornizim:
            existing_order = Porosi.objects.filter(
                depozite=tank,
                statusi__in=['e_hapur', 'e_planifikuar', 'ne_transport']
            ).exists()
            
            if not existing_order:
                # Calculate optimal order quantity
                # Fill to 80% of capacity or enough for 7 days, whichever is larger
                days_supply = 7
                capacity_fill = tank.kapaciteti_total * 0.8
                
                if tank.konsumi_mesatar_ditor > 0:
                    supply_quantity = tank.konsumi_mesatar_ditor * days_supply
                    optimal_quantity = max(capacity_fill - tank.sasia_aktuale, supply_quantity)
                else:
                    optimal_quantity = capacity_fill - tank.sasia_aktuale
                
                # Ensure minimum delivery quantity
                optimal_quantity = max(optimal_quantity, tank.sasia_minimale_dorezimi)
                
                # Don't exceed available tank capacity
                optimal_quantity = min(optimal_quantity, tank.kapaciteti_i_disponueshem)
                
                if optimal_quantity > 0:
                    # Determine priority based on tank status
                    if tank.eshte_kritik:
                        priority = 'kritike'
                        due_date = timezone.now() + timedelta(hours=12)  # 12 hours for critical
                    elif tank.dite_deri_zbrazje <= 2:
                        priority = 'e_larte'
                        due_date = timezone.now() + timedelta(days=1)    # 1 day for high
                    else:
                        priority = 'normale'
                        due_date = timezone.now() + timedelta(days=3)    # 3 days for normal
                    
                    # Create automatic order
                    order = Porosi.objects.create(
                        stacion=tank.stacion,
                        depozite=tank,
                        produkt=tank.produkt,
                        sasia_e_kerkuar=optimal_quantity,
                        prioriteti=priority,
                        data_afati=due_date,
                        eshte_automatike=True,
                        eshte_emergjente=tank.eshte_kritik,
                        shenimet=f"Automatic order generated - Tank level: {tank.perqindja_mbushjes:.1f}%"
                    )
                    
                    logger.info(
                        f"Automatic order created: {order.numri_porosise} for "
                        f"{tank.stacion.emri} - {tank.produkt.emri} "
                        f"({optimal_quantity:.0f}L, Priority: {priority})"
                    )


@receiver(post_save, sender=Porosi)
def log_order_status_change(sender, instance, created, **kwargs):
    """
    Log order status changes for auditing
    """
    if created:
        logger.info(f"New order created: {instance.numri_porosise} for {instance.stacion.emri}")
    else:
        # Check if status changed
        if hasattr(instance, '_original_status'):
            if instance.statusi != instance._original_status:
                logger.info(
                    f"Order {instance.numri_porosise} status changed: "
                    f"{instance._original_status} -> {instance.statusi}"
                )


@receiver(pre_save, sender=Porosi)
def store_original_order_status(sender, instance, **kwargs):
    """
    Store original status before save to detect changes
    """
    if instance.pk:
        try:
            original = Porosi.objects.get(pk=instance.pk)
            instance._original_status = original.statusi
        except Porosi.DoesNotExist:
            pass


@receiver(post_save, sender=PlanRruge)
def update_truck_status_on_route_change(sender, instance, created, **kwargs):
    """
    Update truck status when route status changes
    """
    route = instance
    truck = route.kamion
    
    # Update truck status based on route status
    if route.statusi == 'miratuar':
        if truck.statusi == 'i_lire':
            truck.statusi = 'ne_ngarkim'  # Ready for loading
            truck.save()
    elif route.statusi == 'ne_progres':
        if truck.statusi != 'ne_rruge':
            truck.statusi = 'ne_rruge'
            truck.save()
    elif route.statusi == 'perfunduar':
        if truck.statusi != 'i_lire':
            truck.statusi = 'i_lire'  # Available for new routes
            truck.save()
    elif route.statusi == 'anulluar':
        if truck.statusi in ['ne_ngarkim', 'ne_rruge']:
            truck.statusi = 'i_lire'  # Free up truck
            truck.save()


@receiver(post_save, sender=Ndalese)
def check_route_completion(sender, instance, created, **kwargs):
    """
    Check if route is completed when all stops are finished
    """
    if not created and instance.eshte_perfunduar:
        route = instance.plan_rruge
        
        # Check if all stops are completed
        total_stops = route.ndalesat.count()
        completed_stops = route.ndalesat.filter(eshte_perfunduar=True).count()
        
        if total_stops > 0 and completed_stops == total_stops:
            # All stops completed, mark route as finished
            if route.statusi != 'perfunduar':
                route.statusi = 'perfunduar'
                route.koha_aktuale_mbarimit = timezone.now()
                route.save()
                
                logger.info(f"Route {route.numri_rrugese} automatically completed - all stops finished")


# Performance monitoring signals
@receiver(post_save, sender=PlanRruge)
def calculate_route_efficiency(sender, instance, created, **kwargs):
    """
    Calculate and log route efficiency metrics
    """
    if not created and instance.statusi == 'perfunduar':
        route = instance
        
        # Calculate efficiency metrics
        if route.distanca_e_planifikuar_km and route.distanca_aktuale_km:
            distance_variance = (
                (route.distanca_aktuale_km - route.distanca_e_planifikuar_km) 
                / route.distanca_e_planifikuar_km * 100
            )
        else:
            distance_variance = None
        
        if route.kohezgjatja_e_planifikuar_ore and route.kohezgjatja_aktuale_ore:
            time_variance = (
                (route.kohezgjatja_aktuale_ore - route.kohezgjatja_e_planifikuar_ore) 
                / route.kohezgjatja_e_planifikuar_ore * 100
            )
        else:
            time_variance = None
        
        # Log performance metrics
        logger.info(
            f"Route {route.numri_rrugese} performance: "
            f"Distance variance: {distance_variance:.1f}%, "
            f"Time variance: {time_variance:.1f}%"
            if distance_variance and time_variance else
            f"Route {route.numri_rrugese} completed with limited performance data"
        )
        
        # Alert on significant variances
        if distance_variance and abs(distance_variance) > 20:
            logger.warning(
                f"Route {route.numri_rrugese} had significant distance variance: {distance_variance:.1f}%"
            )
        
        if time_variance and abs(time_variance) > 30:
            logger.warning(
                f"Route {route.numri_rrugese} had significant time variance: {time_variance:.1f}%"
            )


# Data validation signals
@receiver(pre_save, sender=Depozite)
def validate_tank_data(sender, instance, **kwargs):
    """
    Validate tank data before saving
    """
    tank = instance
    
    # Ensure current quantity doesn't exceed capacity
    if tank.sasia_aktuale > tank.kapaciteti_total:
        logger.warning(
            f"Tank {tank} current quantity ({tank.sasia_aktuale}) exceeds capacity ({tank.kapaciteti_total})"
        )
        tank.sasia_aktuale = tank.kapaciteti_total
    
    # Ensure safety stock level is reasonable
    if tank.niveli_minimal_sigurise > tank.kapaciteti_total * 0.2:
        logger.warning(
            f"Tank {tank} safety stock level seems high: {tank.niveli_minimal_sigurise}L "
            f"({tank.niveli_minimal_sigurise/tank.kapaciteti_total*100:.1f}% of capacity)"
        )
    
    # Ensure reorder level is above safety level
    if tank.niveli_i_porosise <= tank.niveli_minimal_sigurise:
        logger.warning(
            f"Tank {tank} reorder level ({tank.niveli_i_porosise}) should be above "
            f"safety level ({tank.niveli_minimal_sigurise})"
        )
        tank.niveli_i_porosise = tank.niveli_minimal_sigurise * 1.5


# Cleanup signals
@receiver(post_save, sender=Porosi)
def cleanup_duplicate_orders(sender, instance, created, **kwargs):
    """
    Prevent duplicate orders for the same tank
    """
    if created and instance.eshte_automatike:
        # Check for other open automatic orders for the same tank
        duplicate_orders = Porosi.objects.filter(
            depozite=instance.depozite,
            statusi='e_hapur',
            eshte_automatike=True
        ).exclude(pk=instance.pk)
        
        if duplicate_orders.exists():
            # Cancel duplicate orders, keep the newest one
            for duplicate in duplicate_orders:
                duplicate.statusi = 'e_anulluar'
                duplicate.shenimet += f"\nCancelled due to newer automatic order {instance.numri_porosise}"
                duplicate.save()
            
            logger.info(
                f"Cancelled {duplicate_orders.count()} duplicate orders for tank {instance.depozite}"
            )
