# reports/views.py - Analytics and reporting views

import json
from datetime import datetime, timedelta
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import (
    Sum, Count, Avg, F, Q, FloatField, 
    Case, When, DecimalField
)
from django.db.models.functions import Extract, TruncDate, TruncWeek, TruncMonth

from logistics.models import (
    PlanRruge, Porosi, Depozite, Kamion, Stacion, 
    Ndalese, NgarkeseShkarkese, Produkt
)


@login_required
def reports_dashboard(request):
    """Main reports dashboard with key analytics"""
    
    # Date range (default: last 30 days)
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    # Get date range from request
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        try:
            start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    if date_to:
        try:
            end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    # Key Performance Indicators
    kpis = calculate_kpis(start_date, end_date)
    
    # Route performance analytics
    route_analytics = calculate_route_performance(start_date, end_date)
    
    # Product distribution
    product_analytics = calculate_product_distribution(start_date, end_date)
    
    # Station performance
    station_analytics = calculate_station_performance(start_date, end_date)
    
    # Fleet utilization
    fleet_analytics = calculate_fleet_utilization(start_date, end_date)
    
    context = {
        'page_title': _('Reports & Analytics'),
        'start_date': start_date,
        'end_date': end_date,
        'kpis': kpis,
        'route_analytics': route_analytics,
        'product_analytics': product_analytics,
        'station_analytics': station_analytics,
        'fleet_analytics': fleet_analytics,
        'date_range_days': (end_date - start_date).days,
    }
    
    return render(request, 'reports/dashboard.html', context)


@login_required
def route_performance_report(request):
    """Detailed route performance report"""
    
    # Date filtering
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=7)  # Default: last week
    
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        try:
            start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    if date_to:
        try:
            end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    # Route performance data
    routes = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).select_related('kamion', 'shofer').annotate(
        efficiency_score=Case(
            When(
                kohezgjatja_e_planifikuar_ore__gt=0,
                kohezgjatja_aktuale_ore__gt=0,
                then=F('kohezgjatja_e_planifikuar_ore') / F('kohezgjatja_aktuale_ore') * 100
            ),
            default=0,
            output_field=FloatField()
        ),
        distance_variance=Case(
            When(
                distanca_e_planifikuar_km__gt=0,
                then=(F('distanca_aktuale_km') - F('distanca_e_planifikuar_km')) / F('distanca_e_planifikuar_km') * 100
            ),
            default=0,
            output_field=FloatField()
        ),
        total_deliveries=Count('ndalesat__ngarkesat_shkarkeset', filter=Q(ndalesat__ngarkesat_shkarkeset__sasia__lt=0))
    ).order_by('-data_planifikimit')
    
    # Performance statistics
    performance_stats = routes.aggregate(
        avg_efficiency=Avg('efficiency_score'),
        avg_distance_variance=Avg('distance_variance'),
        total_routes=Count('id'),
        total_distance=Sum('distanca_aktuale_km'),
        total_duration=Sum('kohezgjatja_aktuale_ore'),
        on_time_routes=Count('id', filter=Q(efficiency_score__gte=90))
    )
    
    # Calculate on-time percentage
    if performance_stats['total_routes'] > 0:
        performance_stats['on_time_percentage'] = (
            performance_stats['on_time_routes'] / performance_stats['total_routes'] * 100
        )
    else:
        performance_stats['on_time_percentage'] = 0
    
    context = {
        'page_title': _('Route Performance Report'),
        'start_date': start_date,
        'end_date': end_date,
        'routes': routes,
        'performance_stats': performance_stats,
    }
    
    return render(request, 'reports/route_performance.html', context)


@login_required
def fuel_consumption_report(request):
    """Fuel consumption and cost analysis report"""
    
    # Date filtering
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        try:
            start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    if date_to:
        try:
            end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    # Fuel consumption by truck
    truck_consumption = Kamion.objects.filter(
        planrruge__data_planifikimit__range=[start_date, end_date],
        planrruge__statusi='perfunduar'
    ).annotate(
        total_distance=Sum('planrruge__distanca_aktuale_km'),
        estimated_fuel_consumption=Sum(
            F('planrruge__distanca_aktuale_km') * F('konsumi_mesatar_l_100km') / 100
        ),
        total_routes=Count('planrruge', filter=Q(planrruge__statusi='perfunduar')),
        avg_efficiency=Avg('planrruge__pikuesi_efikasitetit')
    ).filter(total_distance__gt=0).order_by('-total_distance')
    
    # Overall consumption statistics
    consumption_stats = truck_consumption.aggregate(
        total_fleet_distance=Sum('total_distance'),
        total_fleet_fuel=Sum('estimated_fuel_consumption'),
        avg_consumption_per_100km=Avg('konsumi_mesatar_l_100km'),
        total_active_trucks=Count('id')
    )
    
    # Calculate fuel cost (assuming 1.5 EUR per liter)
    fuel_price_per_liter = 1.5
    if consumption_stats['total_fleet_fuel']:
        consumption_stats['total_fuel_cost'] = consumption_stats['total_fleet_fuel'] * fuel_price_per_liter
    else:
        consumption_stats['total_fuel_cost'] = 0
    
    # Daily consumption trend
    daily_consumption = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).annotate(
        date=TruncDate('data_planifikimit')
    ).values('date').annotate(
        daily_distance=Sum('distanca_aktuale_km'),
        daily_fuel=Sum(F('distanca_aktuale_km') * F('kamion__konsumi_mesatar_l_100km') / 100),
        routes_count=Count('id')
    ).order_by('date')
    
    context = {
        'page_title': _('Fuel Consumption Report'),
        'start_date': start_date,
        'end_date': end_date,
        'truck_consumption': truck_consumption,
        'consumption_stats': consumption_stats,
        'daily_consumption': daily_consumption,
        'fuel_price_per_liter': fuel_price_per_liter,
    }
    
    return render(request, 'reports/fuel_consumption.html', context)


@login_required
def delivery_analytics_api(request):
    """API endpoint for delivery analytics data"""
    
    # Date range
    days = int(request.GET.get('days', 30))
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=days)
    
    # Daily delivery volumes
    daily_deliveries = Porosi.objects.filter(
        statusi='e_dorezuar',
        data_perditesimit__date__range=[start_date, end_date]
    ).annotate(
        date=TruncDate('data_perditesimit')
    ).values('date').annotate(
        total_quantity=Sum('sasia_e_dorezuar'),
        order_count=Count('id')
    ).order_by('date')
    
    # Product distribution
    product_distribution = Porosi.objects.filter(
        statusi='e_dorezuar',
        data_perditesimit__date__range=[start_date, end_date]
    ).values('produkt__emri').annotate(
        total_quantity=Sum('sasia_e_dorezuar'),
        order_count=Count('id')
    ).order_by('-total_quantity')
    
    # Station performance
    station_performance = Stacion.objects.filter(
        porosi__statusi='e_dorezuar',
        porosi__data_perditesimit__date__range=[start_date, end_date]
    ).annotate(
        total_deliveries=Count('porosi'),
        total_quantity=Sum('porosi__sasia_e_dorezuar')
    ).order_by('-total_quantity')[:10]
    
    data = {
        'daily_deliveries': [
            {
                'date': item['date'].strftime('%Y-%m-%d'),
                'quantity': float(item['total_quantity'] or 0),
                'orders': item['order_count']
            }
            for item in daily_deliveries
        ],
        'product_distribution': [
            {
                'product': item['produkt__emri'],
                'quantity': float(item['total_quantity'] or 0),
                'orders': item['order_count']
            }
            for item in product_distribution
        ],
        'station_performance': [
            {
                'station': station.emri,
                'deliveries': station.total_deliveries,
                'quantity': float(station.total_quantity or 0)
            }
            for station in station_performance
        ]
    }
    
    return JsonResponse(data)


@login_required
def export_report(request, report_type):
    """Export reports to CSV format"""
    
    import csv
    from django.http import HttpResponse
    
    # Date range
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=30)
    
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    if date_from:
        try:
            start_date = datetime.strptime(date_from, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    if date_to:
        try:
            end_date = datetime.strptime(date_to, '%Y-%m-%d').date()
        except ValueError:
            pass
    
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{report_type}_{start_date}_{end_date}.csv"'
    
    writer = csv.writer(response)
    
    if report_type == 'routes':
        # Route performance export
        writer.writerow([
            'Date', 'Route Number', 'Truck', 'Driver', 'Planned Distance (km)',
            'Actual Distance (km)', 'Planned Duration (h)', 'Actual Duration (h)',
            'Efficiency Score (%)', 'Status', 'Total Deliveries'
        ])
        
        routes = PlanRruge.objects.filter(
            data_planifikimit__range=[start_date, end_date]
        ).select_related('kamion', 'shofer')
        
        for route in routes:
            efficiency = 0
            if route.kohezgjatja_e_planifikuar_ore and route.kohezgjatja_aktuale_ore:
                efficiency = (route.kohezgjatja_e_planifikuar_ore / route.kohezgjatja_aktuale_ore) * 100
            
            writer.writerow([
                route.data_planifikimit,
                route.numri_rrugese,
                route.kamion.targa,
                route.shofer.emri_i_plote if route.shofer else '',
                route.distanca_e_planifikuar_km or 0,
                route.distanca_aktuale_km or 0,
                route.kohezgjatja_e_planifikuar_ore or 0,
                route.kohezgjatja_aktuale_ore or 0,
                round(efficiency, 2),
                route.get_statusi_display(),
                route.ndalesat.count()
            ])
    
    elif report_type == 'deliveries':
        # Deliveries export
        writer.writerow([
            'Date', 'Order Number', 'Station', 'Product', 'Requested Quantity (L)',
            'Delivered Quantity (L)', 'Priority', 'Status'
        ])
        
        orders = Porosi.objects.filter(
            data_krijimit__date__range=[start_date, end_date]
        ).select_related('stacion', 'produkt')
        
        for order in orders:
            writer.writerow([
                order.data_krijimit.date(),
                order.numri_porosise,
                order.stacion.emri,
                order.produkt.emri,
                order.sasia_e_kerkuar,
                order.sasia_e_dorezuar,
                order.get_prioriteti_display(),
                order.get_statusi_display()
            ])
    
    elif report_type == 'fuel':
        # Fuel consumption export
        writer.writerow([
            'Truck', 'Total Distance (km)', 'Estimated Fuel (L)', 'Routes Completed',
            'Avg Consumption (L/100km)', 'Estimated Cost (EUR)'
        ])
        
        fuel_price = 1.5
        trucks = Kamion.objects.filter(
            planrruge__data_planifikimit__range=[start_date, end_date],
            planrruge__statusi='perfunduar'
        ).annotate(
            total_distance=Sum('planrruge__distanca_aktuale_km'),
            total_routes=Count('planrruge', filter=Q(planrruge__statusi='perfunduar'))
        ).filter(total_distance__gt=0)
        
        for truck in trucks:
            estimated_fuel = (truck.total_distance * truck.konsumi_mesatar_l_100km) / 100
            estimated_cost = estimated_fuel * fuel_price
            
            writer.writerow([
                truck.targa,
                round(truck.total_distance, 2),
                round(estimated_fuel, 2),
                truck.total_routes,
                truck.konsumi_mesatar_l_100km,
                round(estimated_cost, 2)
            ])
    
    return response


# Helper functions for analytics calculations

def calculate_kpis(start_date, end_date):
    """Calculate key performance indicators"""
    
    # Route KPIs
    completed_routes = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).count()
    
    total_routes = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date]
    ).count()
    
    # Order KPIs
    delivered_orders = Porosi.objects.filter(
        statusi='e_dorezuar',
        data_perditesimit__date__range=[start_date, end_date]
    ).count()
    
    total_orders = Porosi.objects.filter(
        data_krijimit__date__range=[start_date, end_date]
    ).count()
    
    # Distance and efficiency
    total_distance = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).aggregate(Sum('distanca_aktuale_km'))['distanca_aktuale_km__sum'] or 0
    
    avg_efficiency = PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar',
        kohezgjatja_e_planifikuar_ore__gt=0,
        kohezgjatja_aktuale_ore__gt=0
    ).aggregate(
        avg_eff=Avg(F('kohezgjatja_e_planifikuar_ore') / F('kohezgjatja_aktuale_ore') * 100)
    )['avg_eff'] or 0
    
    return {
        'completed_routes': completed_routes,
        'route_completion_rate': (completed_routes / total_routes * 100) if total_routes > 0 else 0,
        'delivered_orders': delivered_orders,
        'order_fulfillment_rate': (delivered_orders / total_orders * 100) if total_orders > 0 else 0,
        'total_distance_km': total_distance,
        'avg_route_efficiency': avg_efficiency,
    }


def calculate_route_performance(start_date, end_date):
    """Calculate route performance metrics"""
    
    return PlanRruge.objects.filter(
        data_planifikimit__range=[start_date, end_date],
        statusi='perfunduar'
    ).aggregate(
        total_routes=Count('id'),
        total_distance=Sum('distanca_aktuale_km'),
        total_duration=Sum('kohezgjatja_aktuale_ore'),
        avg_stops_per_route=Avg('ndalesat__count'),
        avg_efficiency=Avg(
            Case(
                When(
                    kohezgjatja_e_planifikuar_ore__gt=0,
                    then=F('kohezgjatja_e_planifikuar_ore') / F('kohezgjatja_aktuale_ore') * 100
                ),
                default=0,
                output_field=FloatField()
            )
        )
    )


def calculate_product_distribution(start_date, end_date):
    """Calculate product distribution analytics"""
    
    return Porosi.objects.filter(
        statusi='e_dorezuar',
        data_perditesimit__date__range=[start_date, end_date]
    ).values('produkt__emri', 'produkt__ngjyra_kodi').annotate(
        total_quantity=Sum('sasia_e_dorezuar'),
        order_count=Count('id')
    ).order_by('-total_quantity')


def calculate_station_performance(start_date, end_date):
    """Calculate station performance metrics"""
    
    return Stacion.objects.filter(
        porosi__statusi='e_dorezuar',
        porosi__data_perditesimit__date__range=[start_date, end_date]
    ).annotate(
        total_deliveries=Count('porosi'),
        total_quantity=Sum('porosi__sasia_e_dorezuar'),
        avg_order_size=Avg('porosi__sasia_e_dorezuar')
    ).order_by('-total_quantity')[:10]


def calculate_fleet_utilization(start_date, end_date):
    """Calculate fleet utilization metrics"""
    
    active_trucks = Kamion.objects.filter(
        eshte_aktiv=True
    ).count()
    
    utilized_trucks = Kamion.objects.filter(
        planrruge__data_planifikimit__range=[start_date, end_date],
        eshte_aktiv=True
    ).distinct().count()
    
    utilization_rate = (utilized_trucks / active_trucks * 100) if active_trucks > 0 else 0
    
    return {
        'active_trucks': active_trucks,
        'utilized_trucks': utilized_trucks,
        'utilization_rate': utilization_rate,
        'avg_routes_per_truck': PlanRruge.objects.filter(
            data_planifikimit__range=[start_date, end_date]
        ).count() / active_trucks if active_trucks > 0 else 0
    }
