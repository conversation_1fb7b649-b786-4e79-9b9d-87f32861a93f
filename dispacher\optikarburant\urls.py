# optikarburant/urls.py - Main URL configuration

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.conf.urls.i18n import i18n_patterns
from django.views.generic import RedirectView

# Non-internationalized URLs (API, admin)
urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/', include('api.urls')),
    path('i18n/', include('django.conf.urls.i18n')),  # Language switching
    
    # Health check endpoint
    path('health/', lambda request: HttpResponse('OK')),
]

# Internationalized URLs (main application)
urlpatterns += i18n_patterns(
    # Root redirects to dashboard
    path('', RedirectView.as_view(pattern_name='dashboard:home', permanent=False)),
    
    # Main application URLs
    path('dashboard/', include('dashboard.urls')),
    path('logistics/', include('logistics.urls')),
    path('optimization/', include('optimization.urls')),
    path('mobile/', include('mobile.urls')),
    path('reports/', include('reports.urls')),
    
    # User authentication
    path('accounts/', include('django.contrib.auth.urls')),
    
    prefix_default_language=False,  # Don't add /en/ prefix for default language
)

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    
    # Add Django Debug Toolbar if available
    try:
        import debug_toolbar
        urlpatterns = [
            path('__debug__/', include(debug_toolbar.urls)),
        ] + urlpatterns
    except ImportError:
        pass

# Admin site customization
admin.site.site_header = "OptiKarburant Administration"
admin.site.site_title = "OptiKarburant Admin"
admin.site.index_title = "Welcome to OptiKarburant Administration"
