<!-- templates/optimization/progress.html - Optimization Progress -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .progress-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
    .progress-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        text-align: center;
        margin-bottom: 2rem;
    }
    .progress-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: conic-gradient(#28a745 0deg {{ progress_angle }}deg, #e9ecef {{ progress_angle }}deg 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        position: relative;
    }
    .progress-circle::before {
        content: '';
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background: white;
        position: absolute;
    }
    .progress-text {
        position: relative;
        z-index: 1;
        font-weight: bold;
        font-size: 1.2rem;
        color: #495057;
    }
    .step-indicator {
        display: flex;
        justify-content: space-between;
        margin-bottom: 2rem;
        position: relative;
    }
    .step-indicator::before {
        content: '';
        position: absolute;
        top: 20px;
        left: 0;
        right: 0;
        height: 2px;
        background: #e9ecef;
        z-index: 1;
    }
    .step-indicator .progress-line {
        position: absolute;
        top: 20px;
        left: 0;
        height: 2px;
        background: #28a745;
        z-index: 2;
        transition: width 0.5s ease;
    }
    .step {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 3;
        transition: all 0.3s ease;
    }
    .step.completed {
        border-color: #28a745;
        background: #28a745;
        color: white;
    }
    .step.active {
        border-color: #007bff;
        background: #007bff;
        color: white;
        animation: pulse 2s infinite;
    }
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
        100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
    }
    .step-label {
        position: absolute;
        top: 50px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.8rem;
        text-align: center;
        white-space: nowrap;
    }
    .log-container {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        max-height: 300px;
        overflow-y: auto;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }
    .log-entry {
        margin-bottom: 0.5rem;
        padding: 0.25rem 0;
    }
    .log-entry.info {
        color: #0066cc;
    }
    .log-entry.success {
        color: #28a745;
    }
    .log-entry.warning {
        color: #ffc107;
    }
    .log-entry.error {
        color: #dc3545;
    }
    .results-preview {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-top: 2rem;
    }
    .metric-card {
        text-align: center;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.25rem;
    }
    .metric-label {
        font-size: 0.8rem;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-cog fa-spin text-primary"></i>{% endblock %}

{% block content %}
<div class="progress-container">
    <!-- Progress Header -->
    <div class="progress-header">
        <div class="progress-circle">
            <div class="progress-text">{{ current }}%</div>
        </div>
        <h3 class="mb-2">{{ status_message }}</h3>
        <p class="mb-0 opacity-75">
            {% if task_status == 'PENDING' %}
                Optimization task is queued and waiting to start...
            {% elif task_status == 'PROGRESS' %}
                AI algorithms are analyzing routes and optimizing delivery sequences...
            {% elif task_status == 'SUCCESS' %}
                Optimization completed successfully! Review the results below.
            {% elif task_status == 'FAILURE' %}
                Optimization failed. Please check the error details and try again.
            {% else %}
                Processing optimization request...
            {% endif %}
        </p>
    </div>

    <!-- Step Indicator -->
    <div class="step-indicator">
        <div class="progress-line" style="width: {{ progress_percentage }}%"></div>
        
        <div class="step {% if current >= 0 %}completed{% endif %}">
            <i class="fas fa-play"></i>
            <div class="step-label">Initialize</div>
        </div>
        
        <div class="step {% if current >= 25 %}completed{% elif current >= 20 %}active{% endif %}">
            <i class="fas fa-map"></i>
            <div class="step-label">Load Data</div>
        </div>
        
        <div class="step {% if current >= 50 %}completed{% elif current >= 40 %}active{% endif %}">
            <i class="fas fa-brain"></i>
            <div class="step-label">Calculate</div>
        </div>
        
        <div class="step {% if current >= 75 %}completed{% elif current >= 70 %}active{% endif %}">
            <i class="fas fa-route"></i>
            <div class="step-label">Generate Routes</div>
        </div>
        
        <div class="step {% if current >= 100 %}completed{% elif current >= 90 %}active{% endif %}">
            <i class="fas fa-check"></i>
            <div class="step-label">Complete</div>
        </div>
    </div>

    <!-- Progress Details -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-info-circle me-2"></i>Optimization Details
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Task ID:</strong> {{ task_id }}</p>
                    <p><strong>Started:</strong> {{ start_time|date:"M d, Y H:i:s" }}</p>
                    <p><strong>Target Date:</strong> {{ target_date|date:"M d, Y" }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Orders:</strong> {{ total_orders }} orders</p>
                    <p><strong>Trucks:</strong> {{ total_trucks }} trucks</p>
                    <p><strong>Estimated Time:</strong> {{ estimated_duration }} minutes</p>
                </div>
            </div>
            
            {% if task_status == 'PROGRESS' %}
            <div class="progress mb-3">
                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                     style="width: {{ current }}%">{{ current }}%</div>
            </div>
            {% elif task_status == 'SUCCESS' %}
            <div class="progress mb-3">
                <div class="progress-bar bg-success" style="width: 100%">100% Complete</div>
            </div>
            {% elif task_status == 'FAILURE' %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Error:</strong> {{ error_message }}
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Live Log -->
    <div class="card mt-3">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-terminal me-2"></i>Live Log
            </h5>
        </div>
        <div class="card-body">
            <div class="log-container" id="logContainer">
                {% for log_entry in log_entries %}
                <div class="log-entry {{ log_entry.level }}">
                    [{{ log_entry.timestamp|date:"H:i:s" }}] {{ log_entry.message }}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Results Preview (shown when completed) -->
    {% if task_status == 'SUCCESS' and results %}
    <div class="results-preview">
        <h5 class="mb-3">
            <i class="fas fa-chart-line me-2"></i>Optimization Results
        </h5>
        
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card bg-primary text-white">
                    <div class="metric-value">{{ results.total_routes }}</div>
                    <div class="metric-label">Routes Created</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card bg-success text-white">
                    <div class="metric-value">{{ results.efficiency_improvement|floatformat:1 }}%</div>
                    <div class="metric-label">Efficiency Gain</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card bg-info text-white">
                    <div class="metric-value">{{ results.total_distance|floatformat:1 }}</div>
                    <div class="metric-label">Total Distance (km)</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card bg-warning text-white">
                    <div class="metric-value">${{ results.cost_savings|floatformat:0 }}</div>
                    <div class="metric-label">Estimated Savings</div>
                </div>
            </div>
        </div>

        <div class="mt-3 text-center">
            <a href="{% url 'optimization:route_list' %}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-eye me-2"></i>View Routes
            </a>
            <a href="{% url 'optimization:dashboard' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="text-center mt-4">
        {% if task_status == 'PENDING' or task_status == 'PROGRESS' %}
            <button class="btn btn-warning me-3" onclick="cancelOptimization()">
                <i class="fas fa-stop me-2"></i>Cancel Optimization
            </button>
        {% endif %}
        
        {% if task_status == 'FAILURE' %}
            <a href="{% url 'optimization:form' %}" class="btn btn-primary me-3">
                <i class="fas fa-redo me-2"></i>Try Again
            </a>
        {% endif %}
        
        <button class="btn btn-outline-secondary" onclick="refreshProgress()">
            <i class="fas fa-sync me-2"></i>Refresh
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let progressInterval;
let taskId = '{{ task_id }}';
let currentStatus = '{{ task_status }}';

document.addEventListener('DOMContentLoaded', function() {
    // Start auto-refresh if task is in progress
    if (currentStatus === 'PENDING' || currentStatus === 'PROGRESS') {
        startProgressMonitoring();
    }
    
    // Auto-scroll log to bottom
    scrollLogToBottom();
});

function startProgressMonitoring() {
    progressInterval = setInterval(function() {
        fetch(`/optimization/progress-api/${taskId}/`)
            .then(response => response.json())
            .then(data => {
                updateProgress(data);
                
                // Stop monitoring if task is complete
                if (data.status === 'SUCCESS' || data.status === 'FAILURE') {
                    clearInterval(progressInterval);
                    
                    // Reload page to show final results
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Error fetching progress:', error);
            });
    }, 2000); // Check every 2 seconds
}

function updateProgress(data) {
    // Update progress circle
    const progressText = document.querySelector('.progress-text');
    const progressCircle = document.querySelector('.progress-circle');
    
    if (progressText) {
        progressText.textContent = data.current + '%';
    }
    
    if (progressCircle) {
        const angle = (data.current / 100) * 360;
        progressCircle.style.background = `conic-gradient(#28a745 0deg ${angle}deg, #e9ecef ${angle}deg 360deg)`;
    }
    
    // Update progress bar
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = data.current + '%';
        progressBar.textContent = data.current + '%';
    }
    
    // Update status message
    const statusMessage = document.querySelector('.progress-header h3');
    if (statusMessage && data.message) {
        statusMessage.textContent = data.message;
    }
    
    // Update step indicators
    updateStepIndicators(data.current);
    
    // Add new log entries
    if (data.log_entries && data.log_entries.length > 0) {
        addLogEntries(data.log_entries);
    }
}

function updateStepIndicators(progress) {
    const steps = document.querySelectorAll('.step');
    const progressLine = document.querySelector('.progress-line');
    
    // Update progress line
    if (progressLine) {
        progressLine.style.width = progress + '%';
    }
    
    // Update step states
    steps.forEach((step, index) => {
        const threshold = index * 25;
        
        step.classList.remove('active', 'completed');
        
        if (progress >= threshold + 25) {
            step.classList.add('completed');
        } else if (progress >= threshold) {
            step.classList.add('active');
        }
    });
}

function addLogEntries(entries) {
    const logContainer = document.getElementById('logContainer');
    
    entries.forEach(entry => {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${entry.level}`;
        logEntry.textContent = `[${entry.timestamp}] ${entry.message}`;
        logContainer.appendChild(logEntry);
    });
    
    scrollLogToBottom();
}

function scrollLogToBottom() {
    const logContainer = document.getElementById('logContainer');
    if (logContainer) {
        logContainer.scrollTop = logContainer.scrollHeight;
    }
}

function refreshProgress() {
    location.reload();
}

function cancelOptimization() {
    if (confirm('Are you sure you want to cancel the optimization? This action cannot be undone.')) {
        fetch(`/optimization/cancel/${taskId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                clearInterval(progressInterval);
                alert('Optimization cancelled successfully.');
                window.location.href = '{% url "optimization:dashboard" %}';
            } else {
                alert('Error cancelling optimization: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error cancelling optimization');
        });
    }
}

// Cleanup interval on page unload
window.addEventListener('beforeunload', function() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }
});
</script>
{% endblock %}
