<!-- templates/dashboard/home.html - Main dashboard home page -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} - OptiKarburant{% endblock %}

{% block page_icon %}<i class="fas fa-tachometer-alt me-2"></i>{% endblock %}

{% block breadcrumb_items %}
<li class="breadcrumb-item active">{% trans "Dashboard" %}</li>
{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <a href="{% url 'optimization:form' %}" class="btn btn-primary">
        <i class="fas fa-magic me-1"></i> {% trans "Generate Routes" %}
    </a>
    <a href="{% url 'dashboard:order_management' %}" class="btn btn-outline-primary">
        <i class="fas fa-plus me-1"></i> {% trans "New Order" %}
    </a>
    <div class="form-check form-switch ms-3 align-self-center">
        <input class="form-check-input" type="checkbox" id="auto-refresh-toggle" checked>
        <label class="form-check-label" for="auto-refresh-toggle">
            {% trans "Auto Refresh" %}
        </label>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Key Metrics Row -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card h-100 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">{% trans "Active Stations" %}</h5>
                        <h2 class="mb-0" id="total-stations">{{ total_stations }}</h2>
                        <small class="opacity-75">{% trans "Operational stations" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-gas-pump fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-primary border-0 pt-0">
                <a href="{% url 'dashboard:station_overview' %}" class="text-white text-decoration-none">
                    {% trans "View All" %} <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card h-100 bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">{% trans "Available Trucks" %}</h5>
                        <h2 class="mb-0" id="active-trucks">{{ active_trucks }}</h2>
                        <small class="opacity-75">{% trans "Ready for deployment" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-truck fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-success border-0 pt-0">
                <a href="{% url 'dashboard:fleet_overview' %}" class="text-white text-decoration-none">
                    {% trans "View Fleet" %} <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card h-100 {% if open_orders > 10 %}bg-warning{% else %}bg-info{% endif %} text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">{% trans "Open Orders" %}</h5>
                        <h2 class="mb-0" id="open-orders">{{ open_orders }}</h2>
                        <small class="opacity-75">{% trans "Pending delivery" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clipboard-list fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer {% if open_orders > 10 %}bg-warning{% else %}bg-info{% endif %} border-0 pt-0">
                <a href="{% url 'dashboard:order_management' %}" class="text-white text-decoration-none">
                    {% trans "Manage Orders" %} <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card metric-card h-100 {% if critical_tanks > 0 %}bg-danger{% else %}bg-secondary{% endif %} text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-1">{% trans "Critical Tanks" %}</h5>
                        <h2 class="mb-0" id="critical-tanks">{{ critical_tanks }}</h2>
                        <small class="opacity-75">{% trans "Below safety level" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-3x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer {% if critical_tanks > 0 %}bg-danger{% else %}bg-secondary{% endif %} border-0 pt-0">
                <a href="{% url 'api:tanks-critical-tanks' %}" class="text-white text-decoration-none">
                    {% trans "View Critical" %} <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Today's Routes -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i> {% trans "Today's Routes" %}
                </h5>
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" id="today-routes">{{ todays_routes }}</span>
                    <small class="text-muted" id="last-updated">
                        {% trans "Last updated" %}: {{ "now"|date:"H:i" }}
                    </small>
                </div>
            </div>
            <div class="card-body">
                {% if todays_routes_detail %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% trans "Route" %}</th>
                                    <th>{% trans "Truck" %}</th>
                                    <th>{% trans "Driver" %}</th>
                                    <th>{% trans "Stops" %}</th>
                                    <th>{% trans "Status" %}</th>
                                    <th>{% trans "Progress" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for route in todays_routes_detail %}
                                <tr>
                                    <td>
                                        <a href="{% url 'optimization:route_detail' route.id %}" class="text-decoration-none fw-medium">
                                            {{ route.numri_rrugese }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ route.kamion.targa }}</span>
                                    </td>
                                    <td>
                                        {% if route.shofer %}
                                            {{ route.shofer.emri_i_plote }}
                                        {% else %}
                                            <span class="text-muted">{% trans "Not assigned" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ route.ndalesat.count }}</span>
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if route.statusi == 'perfunduar' %}bg-success
                                            {% elif route.statusi == 'ne_progres' %}bg-warning
                                            {% elif route.statusi == 'miratuar' %}bg-primary
                                            {% else %}bg-secondary{% endif %}">
                                            {{ route.get_statusi_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                <div class="progress-bar bg-success" 
                                                     style="width: {{ route.progress_percentage }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ route.progress_percentage|floatformat:0 }}%</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{% url 'optimization:route_detail' route.id %}" 
                                               class="btn btn-outline-primary" 
                                               data-bs-toggle="tooltip" 
                                               title="{% trans 'View Details' %}">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if route.statusi == 'draft' %}
                                            <button class="btn btn-outline-success" 
                                                    data-bs-toggle="tooltip" 
                                                    title="{% trans 'Approve Route' %}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-route fa-4x text-muted mb-3"></i>
                        <h5 class="text-muted">{% trans "No routes scheduled for today" %}</h5>
                        <p class="text-muted">{% trans "Generate optimized routes to get started" %}</p>
                        <a href="{% url 'optimization:form' %}" class="btn btn-primary">
                            <i class="fas fa-magic me-1"></i> {% trans "Generate Routes" %}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Alerts and Quick Actions -->
    <div class="col-lg-4 mb-4">
        <!-- Recent Alerts -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bell me-2"></i> {% trans "Recent Alerts" %}
                </h5>
            </div>
            <div class="card-body p-0" style="max-height: 300px; overflow-y: auto;">
                {% if recent_alerts %}
                    {% for alert in recent_alerts %}
                    <div class="alert-item alert-{{ alert.type }} border-0 m-0">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0 me-2">
                                {% if alert.type == 'critical' %}
                                    <i class="fas fa-exclamation-circle text-danger"></i>
                                {% elif alert.type == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-1 small">{{ alert.message }}</p>
                                <small class="text-muted">
                                    {{ alert.timestamp|timesince }} {% trans "ago" %}
                                </small>
                            </div>
                            {% if alert.url %}
                            <div class="flex-shrink-0">
                                <a href="{{ alert.url }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="p-4 text-center">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">{% trans "No alerts. System running smoothly!" %}</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i> {% trans "Quick Actions" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'optimization:quick' %}" class="btn btn-primary">
                        <i class="fas fa-magic me-2"></i> {% trans "Quick Optimization" %}
                    </a>
                    <a href="{% url 'optimization:emergency' %}" class="btn btn-warning">
                        <i class="fas fa-exclamation me-2"></i> {% trans "Emergency Routes" %}
                    </a>
                    <a href="{% url 'dashboard:analytics' %}" class="btn btn-info">
                        <i class="fas fa-chart-bar me-2"></i> {% trans "View Analytics" %}
                    </a>
                    <a href="{% url 'mobile:dashboard' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-mobile-alt me-2"></i> {% trans "Mobile View" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Weekly Statistics Row -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i> {% trans "This Week's Performance" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="border-end">
                            <h3 class="text-success">{{ weekly_stats.routes_completed }}</h3>
                            <p class="text-muted mb-0">{% trans "Routes Completed" %}</p>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="border-end">
                            <h3 class="text-primary">{{ weekly_stats.orders_delivered }}</h3>
                            <p class="text-muted mb-0">{% trans "Orders Delivered" %}</p>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-6 mb-3">
                        <h3 class="text-info">{{ weekly_stats.distance_traveled|floatformat:0 }} km</h3>
                        <p class="text-muted mb-0">{% trans "Distance Traveled" %}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize real-time updates
    const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
    if (autoRefreshToggle && autoRefreshToggle.checked) {
        OptiKarburant.RealTime.start();
    }
    
    // Quick optimization button
    document.querySelector('a[href*="quick"]')?.addEventListener('click', function(e) {
        e.preventDefault();
        
        if (confirm('{% trans "Start quick optimization for urgent orders?" %}')) {
            fetch(this.href, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        OptiKarburant.showNotification(data.message, 'success');
                        if (data.task_id) {
                            OptiKarburant.Optimization.trackProgress(data.task_id);
                        }
                    } else {
                        OptiKarburant.showNotification(data.message || 'Failed to start optimization', 'error');
                    }
                })
                .catch(error => {
                    OptiKarburant.showNotification('Network error', 'error');
                });
        }
    });
});
</script>
{% endblock %}
