<!-- templates/base.html - Base template for OptiKarburant -->
{% load static %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}

<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ page_title|default:"OptiKarburant" }}{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <!-- Custom CSS -->
    <link href="{% static 'css/main.css' %}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'favicon.ico' %}">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{% url 'dashboard:home' %}">
                <i class="fas fa-truck me-2"></i>
                OptiKarburant
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:home' %}">
                            <i class="fas fa-tachometer-alt me-1"></i> {% trans "Dashboard" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:station_overview' %}">
                            <i class="fas fa-gas-pump me-1"></i> {% trans "Stations" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:fleet_overview' %}">
                            <i class="fas fa-truck me-1"></i> {% trans "Fleet" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:order_management' %}">
                            <i class="fas fa-clipboard-list me-1"></i> {% trans "Orders" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-route me-1"></i> {% trans "Optimization" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'optimization:dashboard' %}">
                                <i class="fas fa-magic me-2"></i> {% trans "Dashboard" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'optimization:form' %}">
                                <i class="fas fa-plus me-2"></i> {% trans "Start Optimization" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'optimization:route_list' %}">
                                <i class="fas fa-list me-2"></i> {% trans "View Routes" %}
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'dashboard:analytics' %}">
                            <i class="fas fa-chart-bar me-1"></i> {% trans "Analytics" %}
                        </a>
                    </li>
                </ul>
                
                <!-- Language Selector -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            {% if LANGUAGE_CODE == 'sq' %}
                                Shqip
                            {% else %}
                                English
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            {% get_available_languages as languages %}
                            {% for lang_code, lang_name in languages %}
                                <li>
                                    <form action="{% url 'set_language' %}" method="post" class="d-inline">
                                        {% csrf_token %}
                                        <input name="next" type="hidden" value="{{ request.get_full_path|slice:'3:' }}" />
                                        <input name="language" type="hidden" value="{{ lang_code }}" />
                                        <button type="submit" class="dropdown-item {% if lang_code == LANGUAGE_CODE %}active{% endif %}">
                                            {{ lang_name }}
                                        </button>
                                    </form>
                                </li>
                            {% endfor %}
                        </ul>
                    </li>
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i> 
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-user-cog me-2"></i> {% trans "Profile" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}">
                                <i class="fas fa-cogs me-2"></i> {% trans "Admin Panel" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'mobile:dashboard' %}">
                                <i class="fas fa-mobile-alt me-2"></i> {% trans "Mobile View" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form method="post" action="{% url 'logout' %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i> {% trans "Logout" %}
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container-fluid py-4">
        <!-- Breadcrumb -->
        {% block breadcrumb %}
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'dashboard:home' %}">
                        <i class="fas fa-home"></i> {% trans "Home" %}
                    </a>
                </li>
                {% block breadcrumb_items %}{% endblock %}
            </ol>
        </nav>
        {% endblock %}

        <!-- Messages -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    <i class="fas {% if message.tags == 'success' %}fa-check-circle{% elif message.tags == 'error' %}fa-exclamation-triangle{% elif message.tags == 'warning' %}fa-exclamation-circle{% else %}fa-info-circle{% endif %} me-2"></i>
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <!-- Page Header -->
        {% block page_header %}
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                {% block page_icon %}{% endblock %}
                {{ page_title|default:"Page" }}
            </h1>
            {% block page_actions %}{% endblock %}
        </div>
        {% endblock %}

        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4 border-top">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; 2025 OptiKarburant - {% trans "Fuel Distribution Optimization System" %}
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="text-muted mb-0">
                        {% trans "Version" %} 1.0.0 | 
                        <a href="{% url 'api:system-health-check' %}" class="text-decoration-none">
                            {% trans "API Status" %}
                        </a> |
                        <a href="/admin/doc/" class="text-decoration-none">
                            {% trans "Documentation" %}
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Leaflet JS for maps -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    
    <!-- Page-specific JavaScript -->
    {% block extra_js %}{% endblock %}
    
    <!-- Initialize tooltips and popovers -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // Initialize Bootstrap popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
            
            // Add current page highlighting to navigation
            const currentPath = window.location.pathname;
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
