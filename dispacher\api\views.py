# api/views.py - Django REST Framework API views

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db.models import Q, F
from datetime import datetime, timedelta

from logistics.models import (
    Produkt, Stacion, Depozite, Kamion, Particion, 
    Porosi, PlanRruge, Ndalese, NgarkeseShkarkese, <PERSON><PERSON><PERSON>
)
from optimization.tasks import optimize_routes_for_date
from .serializers import (
    ProduktSerializer, StacionSerializer, DepoziteSerializer,
    KamionSerializer, ParticionSerializer, PorosiSerializer,
    PlanRrugeSerializer, NdaleseSerializer, NgarkeseShkarkeseSerializer,
    ShoferSerializer, MobilePlanRrugeSerializer
)


class ProduktViewSet(viewsets.ModelViewSet):
    """API viewset for Products"""
    
    queryset = Produkt.objects.all()
    serializer_class = ProduktSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ['emri']
    ordering_fields = ['emri', 'data_krijimit']
    ordering = ['emri']
    
    def get_queryset(self):
        queryset = super().get_queryset()
        if self.request.query_params.get('active_only') == 'true':
            queryset = queryset.filter(eshte_aktiv=True)
        return queryset


class StacionViewSet(viewsets.ModelViewSet):
    """API viewset for Stations"""
    
    queryset = Stacion.objects.select_related().prefetch_related('depozitat__produkt')
    serializer_class = StacionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [SearchFilter, DjangoFilterBackend, OrderingFilter]
    search_fields = ['emri', 'kodi', 'adresa']
    filterset_fields = ['eshte_aktiv', 'kerkon_pompe', 'kerkon_kontaliter']
    ordering_fields = ['emri', 'kodi', 'data_krijimit']
    ordering = ['emri']
    
    @action(detail=True, methods=['get'])
    def tank_status(self, request, pk=None):
        """Get real-time tank status for a station"""
        station = self.get_object()
        tanks = station.depozitat.select_related('produkt').all()
        
        tank_data = []
        for tank in tanks:
            tank_data.append({
                'id': tank.id,
                'product': tank.produkt.emri,
                'tank_number': tank.numri_tankut,
                'current_quantity': float(tank.sasia_aktuale),
                'total_capacity': float(tank.kapaciteti_total),
                'fill_percentage': tank.perqindja_mbushjes,
                'needs_refill': tank.nevojitet_rifornizim,
                'is_critical': tank.eshte_kritik,
                'last_updated': tank.data_perditesimit.isoformat()
            })
        
        return Response({
            'station_id': station.id,
            'station_name': station.emri,
            'tanks': tank_data,
            'timestamp': timezone.now().isoformat()
        })
    
    @action(detail=False, methods=['get'])
    def critical_stations(self, request):
        """Get stations with critical tank levels"""
        critical_stations = Stacion.objects.filter(
            depozitat__sasia_aktuale__lte=F('depozitat__niveli_minimal_sigurise'),
            eshte_aktiv=True
        ).distinct()
        
        serializer = self.get_serializer(critical_stations, many=True)
        return Response(serializer.data)


class DepoziteViewSet(viewsets.ModelViewSet):
    """API viewset for Tanks"""
    
    queryset = Depozite.objects.select_related('stacion', 'produkt')
    serializer_class = DepoziteSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['stacion', 'produkt']
    ordering_fields = ['sasia_aktuale', 'perqindja_mbushjes', 'data_perditesimit']
    ordering = ['stacion__emri', 'produkt__emri']
    
    @action(detail=False, methods=['get'])
    def critical_tanks(self, request):
        """Get tanks below safety level"""
        critical_tanks = self.queryset.filter(
            sasia_aktuale__lte=F('niveli_minimal_sigurise')
        )
        serializer = self.get_serializer(critical_tanks, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def low_tanks(self, request):
        """Get tanks that need refill"""
        low_tanks = self.queryset.filter(
            sasia_aktuale__lte=F('niveli_i_porosise')
        )
        serializer = self.get_serializer(low_tanks, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_quantity(self, request, pk=None):
        """Update tank quantity"""
        tank = self.get_object()
        new_quantity = request.data.get('quantity')
        
        if new_quantity is None:
            return Response(
                {'error': 'Quantity is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            new_quantity = float(new_quantity)
            if new_quantity < 0:
                return Response(
                    {'error': 'Quantity cannot be negative'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if new_quantity > tank.kapaciteti_total:
                return Response(
                    {'error': 'Quantity exceeds tank capacity'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            tank.sasia_aktuale = new_quantity
            tank.save()
            
            serializer = self.get_serializer(tank)
            return Response(serializer.data)
            
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid quantity value'}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class KamionViewSet(viewsets.ModelViewSet):
    """API viewset for Trucks"""
    
    queryset = Kamion.objects.select_related('shofer_aktual').prefetch_related('particionet')
    serializer_class = KamionSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [SearchFilter, DjangoFilterBackend, OrderingFilter]
    search_fields = ['targa', 'modeli']
    filterset_fields = ['statusi', 'eshte_aktiv', 'ka_pompe', 'ka_kontaliter']
    ordering_fields = ['targa', 'statusi', 'data_krijimit']
    ordering = ['targa']
    
    @action(detail=False, methods=['get'])
    def available_trucks(self, request):
        """Get available trucks for routing"""
        available = self.queryset.filter(
            statusi='i_lire',
            eshte_aktiv=True
        )
        serializer = self.get_serializer(available, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        """Update truck status"""
        truck = self.get_object()
        new_status = request.data.get('status')
        
        if new_status not in dict(Kamion.STATUS_CHOICES):
            return Response(
                {'error': 'Invalid status'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        truck.statusi = new_status
        truck.save()
        
        serializer = self.get_serializer(truck)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def update_location(self, request, pk=None):
        """Update truck GPS location"""
        truck = self.get_object()
        latitude = request.data.get('latitude')
        longitude = request.data.get('longitude')
        
        if not latitude or not longitude:
            return Response(
                {'error': 'Latitude and longitude are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            from django.contrib.gis.geos import Point
            truck.vendndodhja_aktuale = Point(float(longitude), float(latitude))
            truck.data_perditesimit_gps = timezone.now()
            truck.save()
            
            return Response({'message': 'Location updated successfully'})
            
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid coordinates'}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class PorosiViewSet(viewsets.ModelViewSet):
    """API viewset for Orders"""
    
    queryset = Porosi.objects.select_related('stacion', 'produkt', 'depozite', 'krijuar_nga')
    serializer_class = PorosiSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [SearchFilter, DjangoFilterBackend, OrderingFilter]
    search_fields = ['numri_porosise', 'stacion__emri']
    filterset_fields = ['statusi', 'prioriteti', 'eshte_automatike', 'stacion', 'produkt']
    ordering_fields = ['data_krijimit', 'data_afati', 'prioriteti']
    ordering = ['-data_krijimit']
    
    @action(detail=False, methods=['get'])
    def open_orders(self, request):
        """Get open orders ready for optimization"""
        open_orders = self.queryset.filter(statusi='e_hapur')
        serializer = self.get_serializer(open_orders, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def urgent_orders(self, request):
        """Get urgent orders"""
        urgent_orders = self.queryset.filter(
            Q(prioriteti__in=['e_larte', 'kritike']) |
            Q(eshte_emergjente=True) |
            Q(depozite__sasia_aktuale__lte=F('depozite__niveli_minimal_sigurise'))
        ).distinct()
        
        serializer = self.get_serializer(urgent_orders, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def mark_delivered(self, request, pk=None):
        """Mark order as delivered"""
        order = self.get_object()
        delivered_quantity = request.data.get('delivered_quantity')
        
        if delivered_quantity is None:
            return Response(
                {'error': 'Delivered quantity is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            delivered_quantity = float(delivered_quantity)
            order.sasia_e_dorezuar = delivered_quantity
            order.statusi = 'e_dorezuar'
            order.save()
            
            # Update tank quantity
            order.depozite.sasia_aktuale += delivered_quantity
            order.depozite.save()
            
            serializer = self.get_serializer(order)
            return Response(serializer.data)
            
        except (ValueError, TypeError):
            return Response(
                {'error': 'Invalid quantity value'}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class PlanRrugeViewSet(viewsets.ModelViewSet):
    """API viewset for Route Plans"""
    
    queryset = PlanRruge.objects.select_related('kamion', 'shofer', 'krijuar_nga').prefetch_related('ndalesat')
    serializer_class = PlanRrugeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['statusi', 'data_planifikimit', 'kamion']
    ordering_fields = ['data_planifikimit', 'data_krijimit']
    ordering = ['-data_planifikimit']
    
    @action(detail=False, methods=['post'])
    def start_optimization(self, request):
        """Start route optimization"""
        target_date = request.data.get('target_date')
        truck_ids = request.data.get('truck_ids', [])
        order_ids = request.data.get('order_ids', [])
        
        if not target_date:
            return Response(
                {'error': 'Target date is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Validate date format
            datetime.strptime(target_date, '%Y-%m-%d')
            
            # Start optimization task
            task = optimize_routes_for_date.delay(
                target_date,
                user_id=request.user.id,
                truck_ids=truck_ids if truck_ids else None,
                order_ids=order_ids if order_ids else None
            )
            
            return Response({
                'message': 'Optimization started',
                'task_id': task.id,
                'target_date': target_date
            })
            
        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=False, methods=['get'])
    def today_routes(self, request):
        """Get today's routes"""
        today = timezone.now().date()
        today_routes = self.queryset.filter(data_planifikimit=today)
        serializer = self.get_serializer(today_routes, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def approve_route(self, request, pk=None):
        """Approve a draft route"""
        route = self.get_object()
        
        if route.statusi != 'draft':
            return Response(
                {'error': 'Only draft routes can be approved'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        route.statusi = 'miratuar'
        route.save()
        
        serializer = self.get_serializer(route)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def start_route(self, request, pk=None):
        """Start executing a route"""
        route = self.get_object()
        
        if route.statusi != 'miratuar':
            return Response(
                {'error': 'Only approved routes can be started'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        route.statusi = 'ne_progres'
        route.koha_aktuale_nisjes = timezone.now()
        route.kamion.statusi = 'ne_rruge'
        route.kamion.save()
        route.save()
        
        serializer = self.get_serializer(route)
        return Response(serializer.data)


class MobileDriverViewSet(viewsets.ReadOnlyModelViewSet):
    """API viewset for mobile driver app"""
    
    queryset = PlanRruge.objects.select_related('kamion', 'shofer').prefetch_related('ndalesat__stacion')
    serializer_class = MobilePlanRrugeSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """Return only routes for the current driver"""
        # Assume the user has a related driver profile
        # This would need to be customized based on your user model setup
        user = self.request.user
        
        # For now, filter by date - in production you'd filter by driver
        today = timezone.now().date()
        return self.queryset.filter(
            data_planifikimit=today,
            statusi__in=['miratuar', 'ne_progres']
        )
    
    @action(detail=True, methods=['post'])
    def complete_stop(self, request, pk=None):
        """Mark a stop as completed"""
        route = self.get_object()
        stop_id = request.data.get('stop_id')
        
        if not stop_id:
            return Response(
                {'error': 'Stop ID is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            stop = route.ndalesat.get(id=stop_id)
            stop.eshte_perfunduar = True
            stop.koha_aktuale_mberritjes = timezone.now()
            stop.koha_aktuale_nisjes = timezone.now()
            stop.save()
            
            return Response({'message': 'Stop completed successfully'})
            
        except Ndalese.DoesNotExist:
            return Response(
                {'error': 'Stop not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )


# Additional utility views
class SystemStatusView(viewsets.ViewSet):
    """System status and health check endpoints"""
    
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def health_check(self, request):
        """System health check"""
        return Response({
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'version': '1.0.0'
        })
    
    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Dashboard statistics"""
        stats = {
            'total_stations': Stacion.objects.filter(eshte_aktiv=True).count(),
            'active_trucks': Kamion.objects.filter(statusi='i_lire', eshte_aktiv=True).count(),
            'open_orders': Porosi.objects.filter(statusi='e_hapur').count(),
            'critical_tanks': Depozite.objects.filter(
                sasia_aktuale__lte=F('niveli_minimal_sigurise')
            ).count(),
            'today_routes': PlanRruge.objects.filter(
                data_planifikimit=timezone.now().date()
            ).count(),
            'timestamp': timezone.now().isoformat()
        }
        
        return Response(stats)
