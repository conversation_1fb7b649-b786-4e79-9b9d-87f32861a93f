#!/bin/bash
# startup.sh - Quick startup script for OptiKarburant

echo "🚀 Starting OptiKarburant Development Environment..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "✅ Please edit .env file with your configuration"
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p media
mkdir -p staticfiles

# Start services
echo "🐳 Starting Docker services..."
docker-compose up -d --build

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 30

# Run migrations
echo "🔄 Running database migrations..."
docker-compose exec web python manage.py makemigrations
docker-compose exec web python manage.py migrate

# Create superuser (optional)
echo "👤 Creating superuser (optional)..."
echo "You can skip this by pressing Ctrl+C"
docker-compose exec web python manage.py createsuperuser || echo "Skipped superuser creation"

# Load sample data
echo "📊 Loading sample data..."
docker-compose exec web python manage.py load_sample_data

# Collect static files
echo "🎨 Collecting static files..."
docker-compose exec web python manage.py collectstatic --noinput

echo ""
echo "🎉 OptiKarburant is now running!"
echo ""
echo "📱 Access the application:"
echo "   Web App:         http://localhost:8000"
echo "   Admin Panel:     http://localhost:8000/admin"
echo "   API:             http://localhost:8000/api/"
echo "   Celery Monitor:  http://localhost:5555"
echo ""
echo "🛠️ Services:"
echo "   PostgreSQL:      localhost:5432"
echo "   Redis:           localhost:6379"
echo "   OSRM:            http://localhost:5000"
echo "   Nominatim:       http://localhost:8080"
echo ""
echo "📊 To monitor logs:"
echo "   docker-compose logs -f web"
echo "   docker-compose logs -f worker"
echo ""
echo "🛑 To stop all services:"
echo "   docker-compose down"
echo ""
