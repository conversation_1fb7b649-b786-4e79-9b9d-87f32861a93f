@echo off
REM startup.bat - Quick startup script for OptiKarburant (Windows)

echo 🚀 Starting OptiKarburant Development Environment...

REM Check if Docker is installed
where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

where docker-compose >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create .env file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file from template...
    copy .env.example .env
    echo ✅ Please edit .env file with your configuration
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist logs mkdir logs
if not exist media mkdir media
if not exist staticfiles mkdir staticfiles

REM Start services
echo 🐳 Starting Docker services...
docker-compose up -d --build

REM Wait for database to be ready
echo ⏳ Waiting for database to be ready...
timeout /t 30 /nobreak >nul

REM Run migrations
echo 🔄 Running database migrations...
docker-compose exec web python manage.py makemigrations
docker-compose exec web python manage.py migrate

REM Create superuser (optional)
echo 👤 Creating superuser (optional)...
echo You can skip this by pressing Ctrl+C
docker-compose exec web python manage.py createsuperuser

REM Load sample data
echo 📊 Loading sample data...
docker-compose exec web python manage.py load_sample_data

REM Collect static files
echo 🎨 Collecting static files...
docker-compose exec web python manage.py collectstatic --noinput

echo.
echo 🎉 OptiKarburant is now running!
echo.
echo 📱 Access the application:
echo    Web App:         http://localhost:8000
echo    Admin Panel:     http://localhost:8000/admin
echo    API:             http://localhost:8000/api/
echo    Celery Monitor:  http://localhost:5555
echo.
echo 🛠️ Services:
echo    PostgreSQL:      localhost:5432
echo    Redis:           localhost:6379
echo    OSRM:            http://localhost:5000
echo    Nominatim:       http://localhost:8080
echo.
echo 📊 To monitor logs:
echo    docker-compose logs -f web
echo    docker-compose logs -f worker
echo.
echo 🛑 To stop all services:
echo    docker-compose down
echo.
pause
