# Generated by Django 4.2.23 on 2025-06-30 01:24

from django.conf import settings
import django.contrib.gis.db.models.fields
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="DepoQendrore",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "emri",
                    models.CharField(
                        default="Depo Qendrore",
                        max_length=100,
                        verbose_name="Depot Name",
                    ),
                ),
                (
                    "vendndodhja",
                    django.contrib.gis.db.models.fields.PointField(
                        srid=4326, verbose_name="Location"
                    ),
                ),
                ("adresa", models.TextField(blank=True, verbose_name="Address")),
                (
                    "kapaciteti_ngarkimi",
                    models.IntegerField(
                        default=10,
                        help_text="Number of trucks that can be loaded simultaneously",
                        verbose_name="Loading Capacity",
                    ),
                ),
                (
                    "koha_mesatare_ngarkimi",
                    models.IntegerField(
                        default=90,
                        help_text="Average time to load one truck",
                        verbose_name="Average Loading Time (minutes)",
                    ),
                ),
                (
                    "orar_punes_nga",
                    models.TimeField(
                        default="06:00", verbose_name="Working Hours From"
                    ),
                ),
                (
                    "orar_punes_deri",
                    models.TimeField(default="22:00", verbose_name="Working Hours To"),
                ),
                (
                    "eshte_aktiv",
                    models.BooleanField(default=True, verbose_name="Active"),
                ),
            ],
            options={
                "verbose_name": "Central Depot",
                "verbose_name_plural": "Central Depots",
            },
        ),
        migrations.CreateModel(
            name="Depozite",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "numri_tankut",
                    models.CharField(max_length=10, verbose_name="Tank Number"),
                ),
                (
                    "kapaciteti_total",
                    models.FloatField(
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Total Capacity (L)",
                    ),
                ),
                (
                    "sasia_aktuale",
                    models.FloatField(
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Current Quantity (L)",
                    ),
                ),
                (
                    "niveli_minimal_sigurise",
                    models.FloatField(
                        help_text="Critical level - generates emergency order",
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Safety Stock Level (L)",
                    ),
                ),
                (
                    "niveli_i_porosise",
                    models.FloatField(
                        help_text="Level when automatic order should be generated",
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Reorder Level (L)",
                    ),
                ),
                (
                    "konsumi_mesatar_ditor",
                    models.FloatField(
                        default=0,
                        help_text="Used for demand forecasting",
                        verbose_name="Average Daily Consumption (L)",
                    ),
                ),
                (
                    "sasia_minimale_dorezimi",
                    models.FloatField(
                        default=1000,
                        help_text="Minimum economical delivery quantity",
                        verbose_name="Minimum Delivery Quantity (L)",
                    ),
                ),
                (
                    "perqindja_maksimale_mbushjes",
                    models.FloatField(
                        default=95,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="Max Fill Percentage",
                    ),
                ),
                (
                    "data_dorezimit_fundit",
                    models.DateField(
                        blank=True, null=True, verbose_name="Last Delivery"
                    ),
                ),
                (
                    "data_perditesimit",
                    models.DateTimeField(auto_now=True, verbose_name="Last Updated"),
                ),
            ],
            options={
                "verbose_name": "Tank",
                "verbose_name_plural": "Tanks",
                "ordering": ["stacion", "produkt", "numri_tankut"],
            },
        ),
        migrations.CreateModel(
            name="Kamion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "targa",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="License Plate"
                    ),
                ),
                (
                    "modeli",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Truck Model"
                    ),
                ),
                (
                    "viti_prodhimit",
                    models.IntegerField(blank=True, null=True, verbose_name="Year"),
                ),
                (
                    "pesha_maksimale_bruto_ton",
                    models.FloatField(
                        help_text="Maximum legal weight including cargo",
                        verbose_name="Max Gross Weight (tons)",
                    ),
                ),
                (
                    "gjatesia_totale_m",
                    models.FloatField(verbose_name="Total Length (m)"),
                ),
                (
                    "eshte_trailer",
                    models.BooleanField(
                        default=True,
                        help_text="Truck head can be detached",
                        verbose_name="Is Trailer",
                    ),
                ),
                (
                    "ka_pompe",
                    models.BooleanField(
                        default=False,
                        help_text="Equipped with unloading pump",
                        verbose_name="Has Pump",
                    ),
                ),
                (
                    "ka_kontaliter",
                    models.BooleanField(
                        default=False,
                        help_text="Equipped with flow meter",
                        verbose_name="Has Flow Meter",
                    ),
                ),
                (
                    "ka_gps",
                    models.BooleanField(default=False, verbose_name="Has GPS Tracking"),
                ),
                (
                    "statusi",
                    models.CharField(
                        choices=[
                            ("i_lire", "Available"),
                            ("ne_ngarkim", "Loading"),
                            ("ne_rruge", "En Route"),
                            ("ne_shkarkim", "Unloading"),
                            ("jashte_sherbimi", "Out of Service"),
                            ("mirembajtje", "Maintenance"),
                        ],
                        default="i_lire",
                        max_length=20,
                        verbose_name="Status",
                    ),
                ),
                (
                    "vendndodhja_aktuale",
                    django.contrib.gis.db.models.fields.PointField(
                        blank=True,
                        null=True,
                        srid=4326,
                        verbose_name="Current Location",
                    ),
                ),
                (
                    "data_perditesimit_gps",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last GPS Update"
                    ),
                ),
                (
                    "data_mirembajtjes_fundit",
                    models.DateField(
                        blank=True, null=True, verbose_name="Last Maintenance"
                    ),
                ),
                (
                    "km_mirembajtjes_rradheses",
                    models.IntegerField(
                        blank=True, null=True, verbose_name="Next Maintenance (km)"
                    ),
                ),
                (
                    "odometri_aktual_km",
                    models.IntegerField(
                        default=0, verbose_name="Current Odometer (km)"
                    ),
                ),
                (
                    "konsumi_mesatar_l_100km",
                    models.FloatField(
                        default=35.0, verbose_name="Average Fuel Consumption (L/100km)"
                    ),
                ),
                (
                    "eshte_aktiv",
                    models.BooleanField(default=True, verbose_name="Active"),
                ),
                (
                    "data_krijimit",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created"),
                ),
            ],
            options={
                "verbose_name": "Truck",
                "verbose_name_plural": "Trucks",
                "ordering": ["targa"],
            },
        ),
        migrations.CreateModel(
            name="Ndalese",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sekuenca", models.IntegerField(verbose_name="Sequence Number")),
                (
                    "koha_parashikuar_mberritjes",
                    models.DateTimeField(verbose_name="Planned Arrival"),
                ),
                (
                    "koha_e_parashikuar_sherbimit_min",
                    models.IntegerField(
                        default=45, verbose_name="Est. Service Time (min)"
                    ),
                ),
                (
                    "koha_parashikuar_nisjes",
                    models.DateTimeField(verbose_name="Planned Departure"),
                ),
                (
                    "koha_aktuale_mberritjes",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual Arrival"
                    ),
                ),
                (
                    "koha_aktuale_nisjes",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual Departure"
                    ),
                ),
                (
                    "eshte_perfunduar",
                    models.BooleanField(default=False, verbose_name="Completed"),
                ),
                (
                    "problemet_dorezimit",
                    models.TextField(
                        blank=True,
                        help_text="Any problems encountered during delivery",
                        verbose_name="Delivery Issues",
                    ),
                ),
                (
                    "nenshkrimi_marresi",
                    models.TextField(
                        blank=True,
                        help_text="Digital signature or recipient name",
                        verbose_name="Recipient Signature",
                    ),
                ),
                (
                    "vendndodhja_gps",
                    django.contrib.gis.db.models.fields.PointField(
                        blank=True, null=True, srid=4326, verbose_name="GPS Location"
                    ),
                ),
                (
                    "depo_qendrore",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.depoqendrore",
                        verbose_name="Central Depot",
                    ),
                ),
            ],
            options={
                "verbose_name": "Stop",
                "verbose_name_plural": "Stops",
                "ordering": ["plan_rruge", "sekuenca"],
            },
        ),
        migrations.CreateModel(
            name="Shofer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("emri", models.CharField(max_length=100, verbose_name="Driver Name")),
                ("mbiemri", models.CharField(max_length=100, verbose_name="Surname")),
                (
                    "telefoni",
                    models.CharField(blank=True, max_length=20, verbose_name="Phone"),
                ),
                (
                    "email",
                    models.EmailField(blank=True, max_length=254, verbose_name="Email"),
                ),
                (
                    "leje_drejtimi_numri",
                    models.CharField(
                        max_length=50, unique=True, verbose_name="License Number"
                    ),
                ),
                (
                    "leje_drejtimi_skadon",
                    models.DateField(verbose_name="License Expires"),
                ),
                (
                    "leje_adr",
                    models.BooleanField(
                        default=False,
                        help_text="License for dangerous goods transport",
                        verbose_name="ADR License",
                    ),
                ),
                (
                    "ore_punes_maksimale_ditor",
                    models.IntegerField(
                        default=8, verbose_name="Max Daily Working Hours"
                    ),
                ),
                (
                    "ore_drejtimi_maksimale_ditor",
                    models.IntegerField(
                        default=6, verbose_name="Max Daily Driving Hours"
                    ),
                ),
                (
                    "eshte_aktiv",
                    models.BooleanField(default=True, verbose_name="Active"),
                ),
                (
                    "data_punesimit",
                    models.DateField(
                        blank=True, null=True, verbose_name="Employment Date"
                    ),
                ),
            ],
            options={
                "verbose_name": "Driver",
                "verbose_name_plural": "Drivers",
                "ordering": ["emri", "mbiemri"],
            },
        ),
        migrations.CreateModel(
            name="Stacion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("emri", models.CharField(max_length=200, verbose_name="Station Name")),
                (
                    "kodi",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="Station Code"
                    ),
                ),
                (
                    "vendndodhja",
                    django.contrib.gis.db.models.fields.PointField(
                        srid=4326, verbose_name="Location"
                    ),
                ),
                ("adresa", models.TextField(blank=True, verbose_name="Address")),
                (
                    "orar_pranimi_nga",
                    models.TimeField(
                        default="06:00", verbose_name="Delivery Start Time"
                    ),
                ),
                (
                    "orar_pranimi_deri",
                    models.TimeField(default="18:00", verbose_name="Delivery End Time"),
                ),
                (
                    "kerkon_pompe",
                    models.BooleanField(
                        default=False,
                        help_text="Station requires truck with pump for unloading",
                        verbose_name="Requires Pump",
                    ),
                ),
                (
                    "kerkon_kontaliter",
                    models.BooleanField(
                        default=False,
                        help_text="Station requires truck with flow meter",
                        verbose_name="Requires Flow Meter",
                    ),
                ),
                (
                    "max_kamione_njekohesisht",
                    models.IntegerField(
                        default=1,
                        help_text="Maximum trucks that can unload simultaneously",
                        verbose_name="Max Simultaneous Trucks",
                    ),
                ),
                (
                    "koha_mesatare_shkarkimi",
                    models.IntegerField(
                        default=45, verbose_name="Average Unloading Time (minutes)"
                    ),
                ),
                (
                    "max_pesha_kamioni_ton",
                    models.FloatField(
                        blank=True,
                        help_text="Maximum truck weight allowed",
                        null=True,
                        verbose_name="Max Truck Weight (tons)",
                    ),
                ),
                (
                    "max_gjatesia_kamioni_m",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Max Truck Length (m)"
                    ),
                ),
                (
                    "menaxher_emri",
                    models.CharField(
                        blank=True, max_length=100, verbose_name="Manager Name"
                    ),
                ),
                (
                    "telefoni",
                    models.CharField(blank=True, max_length=20, verbose_name="Phone"),
                ),
                (
                    "email",
                    models.EmailField(blank=True, max_length=254, verbose_name="Email"),
                ),
                (
                    "eshte_aktiv",
                    models.BooleanField(default=True, verbose_name="Active"),
                ),
                (
                    "data_krijimit",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created"),
                ),
                (
                    "data_perditesimit",
                    models.DateTimeField(auto_now=True, verbose_name="Updated"),
                ),
            ],
            options={
                "verbose_name": "Station",
                "verbose_name_plural": "Stations",
                "ordering": ["emri"],
            },
        ),
        migrations.CreateModel(
            name="Produkt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "emri",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="Product Name"
                    ),
                ),
                (
                    "densiteti",
                    models.FloatField(
                        default=0.85,
                        help_text="Density for volume/weight conversions",
                        verbose_name="Density (kg/L)",
                    ),
                ),
                (
                    "ngjyra_kodi",
                    models.CharField(
                        default="#3498db",
                        help_text="Hex color code for UI display",
                        max_length=7,
                        verbose_name="Color Code",
                    ),
                ),
                (
                    "eshte_aktiv",
                    models.BooleanField(default=True, verbose_name="Active"),
                ),
                (
                    "data_krijimit",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created"),
                ),
                (
                    "produkte_kompatible",
                    models.ManyToManyField(
                        blank=True,
                        help_text="Products that can share the same compartment",
                        to="logistics.produkt",
                        verbose_name="Compatible Products",
                    ),
                ),
            ],
            options={
                "verbose_name": "Product",
                "verbose_name_plural": "Products",
                "ordering": ["emri"],
            },
        ),
        migrations.CreateModel(
            name="Porosi",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "numri_porosise",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        unique=True,
                        verbose_name="Order Number",
                    ),
                ),
                (
                    "sasia_e_kerkuar",
                    models.FloatField(
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Requested Quantity (L)",
                    ),
                ),
                (
                    "sasia_e_miratuar",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Approved Quantity (L)"
                    ),
                ),
                (
                    "sasia_e_dorezuar",
                    models.FloatField(default=0, verbose_name="Delivered Quantity (L)"),
                ),
                (
                    "prioriteti",
                    models.CharField(
                        choices=[
                            ("e_ulet", "Low"),
                            ("normale", "Normal"),
                            ("e_larte", "High"),
                            ("kritike", "Critical"),
                        ],
                        default="normale",
                        max_length=10,
                        verbose_name="Priority",
                    ),
                ),
                (
                    "data_afati",
                    models.DateTimeField(
                        help_text="Latest acceptable delivery time",
                        verbose_name="Due Date",
                    ),
                ),
                (
                    "koha_preferuar_fillimi",
                    models.TimeField(
                        blank=True, null=True, verbose_name="Preferred Time Start"
                    ),
                ),
                (
                    "koha_preferuar_mbarimi",
                    models.TimeField(
                        blank=True, null=True, verbose_name="Preferred Time End"
                    ),
                ),
                (
                    "statusi",
                    models.CharField(
                        choices=[
                            ("e_hapur", "Open"),
                            ("e_planifikuar", "Planned"),
                            ("ne_transport", "In Transport"),
                            ("e_dorezuar", "Delivered"),
                            ("e_anulluar", "Cancelled"),
                        ],
                        default="e_hapur",
                        max_length=15,
                        verbose_name="Status",
                    ),
                ),
                (
                    "eshte_automatike",
                    models.BooleanField(
                        default=True,
                        help_text="Order generated automatically by safety stock rules",
                        verbose_name="Automatic Order",
                    ),
                ),
                (
                    "eshte_emergjente",
                    models.BooleanField(default=False, verbose_name="Emergency Order"),
                ),
                ("shenimet", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "data_krijimit",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created"),
                ),
                (
                    "data_perditesimit",
                    models.DateTimeField(auto_now=True, verbose_name="Updated"),
                ),
                (
                    "depozite",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.depozite",
                        verbose_name="Tank",
                    ),
                ),
                (
                    "krijuar_nga",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "produkt",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="logistics.produkt",
                        verbose_name="Product",
                    ),
                ),
                (
                    "stacion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.stacion",
                        verbose_name="Station",
                    ),
                ),
            ],
            options={
                "verbose_name": "Order",
                "verbose_name_plural": "Orders",
                "ordering": ["-prioriteti", "data_afati", "data_krijimit"],
            },
        ),
        migrations.CreateModel(
            name="PlanRruge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "numri_rrugese",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        unique=True,
                        verbose_name="Route Number",
                    ),
                ),
                ("data_planifikimit", models.DateField(verbose_name="Route Date")),
                (
                    "koha_nisjes_nga_depo",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Departure Time from Depot"
                    ),
                ),
                (
                    "distanca_e_planifikuar_km",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Planned Distance (km)"
                    ),
                ),
                (
                    "kohezgjatja_e_planifikuar_ore",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Planned Duration (hours)"
                    ),
                ),
                (
                    "kostoja_e_parashikuar",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Estimated Cost"
                    ),
                ),
                (
                    "distanca_aktuale_km",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Actual Distance (km)"
                    ),
                ),
                (
                    "kohezgjatja_aktuale_ore",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Actual Duration (hours)"
                    ),
                ),
                (
                    "kostoja_aktuale",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Actual Cost"
                    ),
                ),
                (
                    "koha_aktuale_nisjes",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual Start Time"
                    ),
                ),
                (
                    "koha_aktuale_mbarimit",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Actual End Time"
                    ),
                ),
                (
                    "statusi",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("miratuar", "Approved"),
                            ("ne_progres", "In Progress"),
                            ("perfunduar", "Completed"),
                            ("anulluar", "Cancelled"),
                        ],
                        default="draft",
                        max_length=15,
                        verbose_name="Status",
                    ),
                ),
                (
                    "pikuesi_optimizimit",
                    models.FloatField(
                        blank=True,
                        help_text="Algorithm optimization score (lower is better)",
                        null=True,
                        verbose_name="Optimization Score",
                    ),
                ),
                ("shenimet", models.TextField(blank=True, verbose_name="Notes")),
                (
                    "data_krijimit",
                    models.DateTimeField(auto_now_add=True, verbose_name="Created"),
                ),
                (
                    "data_perditesimit",
                    models.DateTimeField(auto_now=True, verbose_name="Updated"),
                ),
                (
                    "kamion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.kamion",
                        verbose_name="Truck",
                    ),
                ),
                (
                    "krijuar_nga",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="Created By",
                    ),
                ),
                (
                    "shofer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="logistics.shofer",
                        verbose_name="Driver",
                    ),
                ),
            ],
            options={
                "verbose_name": "Route Plan",
                "verbose_name_plural": "Route Plans",
                "ordering": ["data_planifikimit", "kamion"],
            },
        ),
        migrations.CreateModel(
            name="Particion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "numri_i_dhomes",
                    models.IntegerField(verbose_name="Compartment Number"),
                ),
                (
                    "kapaciteti",
                    models.FloatField(
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Capacity (L)",
                    ),
                ),
                (
                    "sasia_heel_litra",
                    models.FloatField(
                        default=50,
                        help_text="Quantity that cannot be pumped out",
                        verbose_name="Heel Stock (L)",
                    ),
                ),
                (
                    "eshte_i_pastruar",
                    models.BooleanField(default=True, verbose_name="Is Clean"),
                ),
                (
                    "data_pastrimit",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Last Cleaned"
                    ),
                ),
                (
                    "kerkon_pastrimin",
                    models.BooleanField(default=False, verbose_name="Needs Cleaning"),
                ),
                (
                    "sasia_aktuale",
                    models.FloatField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="Current Quantity (L)",
                    ),
                ),
                (
                    "kamion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="particionet",
                        to="logistics.kamion",
                        verbose_name="Truck",
                    ),
                ),
                (
                    "produkt_i_dedikuar",
                    models.ForeignKey(
                        blank=True,
                        help_text="Product exclusively assigned to this compartment",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="particionet_te_dedikuar",
                        to="logistics.produkt",
                        verbose_name="Dedicated Product",
                    ),
                ),
                (
                    "produkti_aktual",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="particionet_aktuale",
                        to="logistics.produkt",
                        verbose_name="Current Product",
                    ),
                ),
                (
                    "produkti_i_fundit",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="particionet_te_fundit",
                        to="logistics.produkt",
                        verbose_name="Last Product",
                    ),
                ),
            ],
            options={
                "verbose_name": "Compartment",
                "verbose_name_plural": "Compartments",
                "ordering": ["kamion", "numri_i_dhomes"],
                "unique_together": {("kamion", "numri_i_dhomes")},
            },
        ),
        migrations.CreateModel(
            name="NgarkeseShkarkese",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "sasia",
                    models.FloatField(
                        help_text="Positive for loading, negative for unloading",
                        verbose_name="Quantity (L)",
                    ),
                ),
                (
                    "sasia_e_dorezuar",
                    models.FloatField(default=0, verbose_name="Delivered Quantity (L)"),
                ),
                (
                    "temperatura_celsius",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Temperature (°C)"
                    ),
                ),
                (
                    "densiteti_i_matur",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Measured Density"
                    ),
                ),
                (
                    "leximi_fillimi_kontaliter",
                    models.FloatField(
                        blank=True, null=True, verbose_name="Start Meter Reading"
                    ),
                ),
                (
                    "leximi_mbarimi_kontaliter",
                    models.FloatField(
                        blank=True, null=True, verbose_name="End Meter Reading"
                    ),
                ),
                (
                    "koha_fillimi_dorezimit",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Delivery Start"
                    ),
                ),
                (
                    "koha_mbarimi_dorezimit",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Delivery End"
                    ),
                ),
                (
                    "eshte_perfunduar",
                    models.BooleanField(default=False, verbose_name="Completed"),
                ),
                (
                    "ka_variacion",
                    models.BooleanField(default=False, verbose_name="Has Variance"),
                ),
                (
                    "arsyeja_variacionit",
                    models.TextField(
                        blank=True,
                        help_text="Reason for quantity variance if any",
                        verbose_name="Variance Reason",
                    ),
                ),
                (
                    "depozite",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.depozite",
                        verbose_name="Tank",
                    ),
                ),
                (
                    "ndalese",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ngarkesat_shkarkeset",
                        to="logistics.ndalese",
                        verbose_name="Stop",
                    ),
                ),
                (
                    "particion",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.particion",
                        verbose_name="Compartment",
                    ),
                ),
                (
                    "porosi",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="logistics.porosi",
                        verbose_name="Order",
                    ),
                ),
                (
                    "produkt",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to="logistics.produkt",
                        verbose_name="Product",
                    ),
                ),
            ],
            options={
                "verbose_name": "Loading/Unloading Operation",
                "verbose_name_plural": "Loading/Unloading Operations",
                "ordering": ["ndalese", "produkt"],
            },
        ),
        migrations.AddField(
            model_name="ndalese",
            name="plan_rruge",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="ndalesat",
                to="logistics.planrruge",
                verbose_name="Route Plan",
            ),
        ),
        migrations.AddField(
            model_name="ndalese",
            name="stacion",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="logistics.stacion",
                verbose_name="Station",
            ),
        ),
        migrations.AddField(
            model_name="kamion",
            name="shofer_aktual",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="kamioni_aktual",
                to="logistics.shofer",
                verbose_name="Current Driver",
            ),
        ),
        migrations.AddField(
            model_name="depozite",
            name="produkt",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to="logistics.produkt",
                verbose_name="Product",
            ),
        ),
        migrations.AddField(
            model_name="depozite",
            name="stacion",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="depozitat",
                to="logistics.stacion",
                verbose_name="Station",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="ndalese",
            unique_together={("plan_rruge", "sekuenca")},
        ),
        migrations.AlterUniqueTogether(
            name="depozite",
            unique_together={("stacion", "produkt", "numri_tankut")},
        ),
    ]
