<!-- templates/optimization/dashboard.html - Route Optimization Dashboard -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .optimization-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
        border-radius: 0.75rem;
    }
    .optimization-card:hover {
        transform: translateY(-2px);
    }
    .date-card {
        border-left: 4px solid #dee2e6;
        transition: all 0.3s ease;
    }
    .date-card.has-optimization {
        border-left-color: #28a745;
        background-color: #f8fff9;
    }
    .date-card.needs-optimization {
        border-left-color: #ffc107;
        background-color: #fffdf5;
    }
    .date-card.no-orders {
        border-left-color: #6c757d;
        background-color: #f8f9fa;
    }
    .optimization-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 1rem;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .metric-box {
        text-align: center;
        padding: 1.5rem;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .route-item {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 0;
    }
    .route-item:last-child {
        border-bottom: none;
    }
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
    }
    .optimization-btn {
        border-radius: 50px;
        padding: 0.75rem 2rem;
        font-weight: bold;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .quick-actions {
        background: #f8f9fa;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-route text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Optimization Header -->
    <div class="optimization-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">
                    <i class="fas fa-brain me-3"></i>Route Optimization Center
                </h2>
                <p class="mb-0 opacity-75">
                    AI-powered route optimization for maximum efficiency and cost savings
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="metric-box bg-white bg-opacity-20">
                    <div class="metric-value">{{ available_trucks }}</div>
                    <div class="metric-label">Available Trucks</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-2">
                    <i class="fas fa-bolt text-warning me-2"></i>Quick Actions
                </h5>
                <p class="text-muted mb-0">Start optimization for today or schedule for future dates</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="btn-group">
                    <button class="btn btn-primary optimization-btn" onclick="startTodayOptimization()">
                        <i class="fas fa-play me-2"></i>Optimize Today
                    </button>
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#optimizationModal">
                        <i class="fas fa-calendar-plus me-2"></i>Schedule
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Weekly Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card optimization-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-week me-2"></i>7-Day Optimization Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for date in date_range %}
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="card date-card h-100 needs-optimization">
                                <div class="card-body text-center">
                                    <h6 class="card-title">
                                        {{ date|date:"M d" }}
                                        {% if date == today %}
                                            <span class="badge bg-primary ms-2">Today</span>
                                        {% endif %}
                                    </h6>
                                    <p class="text-muted small">{{ date|date:"l" }}</p>

                                    <div class="row">
                                        <div class="col-6">
                                            <div class="metric-value text-primary" style="font-size: 1.5rem;">
                                                0
                                            </div>
                                            <div class="metric-label">Orders</div>
                                        </div>
                                        <div class="col-6">
                                            <div class="metric-value text-success" style="font-size: 1.5rem;">
                                                0
                                            </div>
                                            <div class="metric-label">Routes</div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <button class="btn btn-sm btn-warning" onclick="optimizeDate('{{ date.isoformat }}')">
                                            <i class="fas fa-cog me-1"></i>Optimize
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Optimizations -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card optimization-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Recent Optimizations
                    </h5>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>View All
                    </a>
                </div>
                <div class="card-body">
                    {% for route in recent_optimizations %}
                    <div class="route-item">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="mb-1">{{ route.emri }}</h6>
                                <small class="text-muted">
                                    {{ route.data_krijimit|date:"M d, Y H:i" }}
                                </small>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="metric-value text-primary" style="font-size: 1.2rem;">
                                        {{ route.ndaleset.count }}
                                    </div>
                                    <div class="metric-label">Stops</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    {% if route.kamion %}
                                        <strong>{{ route.kamion.emri }}</strong>
                                        <br><small class="text-muted">{{ route.kamion.numri_targave }}</small>
                                    {% else %}
                                        <span class="text-muted">No truck assigned</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <span class="status-badge bg-{{ route.status_color }}">
                                        {{ route.get_statusi_display }}
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-2 text-end">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewRouteDetails({{ route.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-success" onclick="reoptimizeRoute({{ route.id }})">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-route fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">No recent optimizations</h6>
                        <p class="text-muted">Start your first optimization to see results here</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Optimization Statistics -->
            <div class="card optimization-card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Optimization Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="metric-box bg-primary text-white">
                        <div class="metric-value">87.5%</div>
                        <div class="metric-label">Average Efficiency</div>
                    </div>
                    <div class="metric-box bg-success text-white">
                        <div class="metric-value">23%</div>
                        <div class="metric-label">Cost Reduction</div>
                    </div>
                    <div class="metric-box bg-info text-white">
                        <div class="metric-value">156</div>
                        <div class="metric-label">Routes Optimized</div>
                    </div>
                </div>
            </div>

            <!-- Optimization Tips -->
            <div class="card optimization-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Optimization Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock text-warning me-3"></i>
                                <div>
                                    <h6 class="mb-1">Best Time to Optimize</h6>
                                    <p class="mb-0 text-muted small">Run optimizations early morning for best results</p>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-truck text-primary me-3"></i>
                                <div>
                                    <h6 class="mb-1">Truck Availability</h6>
                                    <p class="mb-0 text-muted small">Ensure trucks are marked as available before optimizing</p>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marked-alt text-success me-3"></i>
                                <div>
                                    <h6 class="mb-1">Geographic Clustering</h6>
                                    <p class="mb-0 text-muted small">Group nearby stations for maximum efficiency</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Optimization Modal -->
<div class="modal fade" id="optimizationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>Schedule Route Optimization
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="optimizationForm">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Target Date</label>
                            <input type="date" class="form-control" name="target_date" 
                                   value="{{ today|date:'Y-m-d' }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Priority</label>
                            <select class="form-select" name="priority">
                                <option value="normal">Normal</option>
                                <option value="high">High Priority</option>
                                <option value="emergency">Emergency</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Optimization Options</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="minimize_distance" checked>
                            <label class="form-check-label">Minimize total distance</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="minimize_time" checked>
                            <label class="form-check-label">Minimize delivery time</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="balance_loads">
                            <label class="form-check-label">Balance truck loads</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="consider_traffic">
                            <label class="form-check-label">Consider traffic patterns</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Additional Notes</label>
                        <textarea class="form-control" name="notes" rows="3" 
                                  placeholder="Any special requirements or constraints..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitOptimization()">
                    <i class="fas fa-play me-2"></i>Start Optimization
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog fa-spin me-2"></i>Optimizing Routes...
                </h5>
            </div>
            <div class="modal-body text-center">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         id="optimizationProgress" style="width: 0%"></div>
                </div>
                <p id="progressText">Initializing optimization engine...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function startTodayOptimization() {
    if (confirm('Start optimization for today? This will create new routes based on pending orders.')) {
        optimizeDate('{{ today|date:"Y-m-d" }}');
    }
}

function optimizeDate(date) {
    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // Start optimization
    fetch('/optimization/start/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify({
            target_date: date,
            priority: 'normal'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Monitor optimization progress
            monitorOptimization(data.task_id);
        } else {
            progressModal.hide();
            alert('Error starting optimization: ' + data.error);
        }
    })
    .catch(error => {
        progressModal.hide();
        console.error('Error:', error);
        alert('Error starting optimization');
    });
}

function submitOptimization() {
    const form = document.getElementById('optimizationForm');
    const formData = new FormData(form);
    
    // Hide modal and show progress
    bootstrap.Modal.getInstance(document.getElementById('optimizationModal')).hide();
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // Submit optimization request
    fetch('/optimization/start/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            monitorOptimization(data.task_id);
        } else {
            progressModal.hide();
            alert('Error starting optimization: ' + data.error);
        }
    })
    .catch(error => {
        progressModal.hide();
        console.error('Error:', error);
        alert('Error starting optimization');
    });
}

function monitorOptimization(taskId) {
    const progressBar = document.getElementById('optimizationProgress');
    const progressText = document.getElementById('progressText');
    
    const checkProgress = () => {
        fetch(`/optimization/status/${taskId}/`)
            .then(response => response.json())
            .then(data => {
                progressBar.style.width = data.progress + '%';
                progressText.textContent = data.message;
                
                if (data.status === 'completed') {
                    setTimeout(() => {
                        bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
                        alert('Optimization completed successfully!');
                        location.reload();
                    }, 1000);
                } else if (data.status === 'failed') {
                    bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
                    alert('Optimization failed: ' + data.error);
                } else {
                    setTimeout(checkProgress, 2000);
                }
            })
            .catch(error => {
                console.error('Error checking progress:', error);
                setTimeout(checkProgress, 5000);
            });
    };
    
    checkProgress();
}

function viewRouteDetails(routeId) {
    window.location.href = `/dashboard/routes/${routeId}/`;
}

function reoptimizeRoute(routeId) {
    if (confirm('Re-optimize this route? This will create a new optimized version.')) {
        fetch(`/optimization/reoptimize/${routeId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Route re-optimization started!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        });
    }
}

// Auto-refresh every 60 seconds
setInterval(() => {
    if (!document.querySelector('.modal.show')) {
        location.reload();
    }
}, 60000);
</script>
{% endblock %}
