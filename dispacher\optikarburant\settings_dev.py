# Development settings for OptiKarburant - Uses SQLite instead of PostGIS
# This file provides a simpler setup for development and testing

from .settings import *
import os

# Override database settings to use SQLite with spatial support
DATABASES = {
    'default': {
        'ENGINE': 'django.contrib.gis.db.backends.spatialite',
        'NAME': BASE_DIR / 'db_dev.sqlite3',
    }
}

# Disable some PostGIS-specific features for SQLite
SPATIALITE_LIBRARY_PATH = 'mod_spatialite'

# Development-specific settings
DEBUG = True
ALLOWED_HOSTS = ['*']

# Simplified logging for development
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

print("🔧 Using development settings with SQLite spatial database")
