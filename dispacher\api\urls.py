# api/urls.py - URL configuration for API module

from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from rest_framework.authtoken.views import obtain_auth_token
from . import views

app_name = 'api'

# Create router and register viewsets
router = DefaultRouter()
router.register(r'products', views.ProduktViewSet, basename='products')
router.register(r'stations', views.StacionViewSet, basename='stations')
router.register(r'tanks', views.DepoziteViewSet, basename='tanks')
router.register(r'trucks', views.KamionViewSet, basename='trucks')
router.register(r'orders', views.PorosiViewSet, basename='orders')
router.register(r'routes', views.PlanRrugeViewSet, basename='routes')
router.register(r'mobile', views.MobileDriverViewSet, basename='mobile')
router.register(r'system', views.SystemStatusView, basename='system')

urlpatterns = [
    # Authentication
    path('auth/token/', obtain_auth_token, name='api_token_auth'),
    
    # API root
    path('', include(router.urls)),
    
    # API documentation (if using DRF browsable API)
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),
]
