INFO 2025-06-30 03:12:05,943 autoreload 20 140045845322624 Watching for file changes with StatReloader
INFO 2025-06-30 03:13:12,800 autoreload 20 140045845322624 /code/optimization/engine.py changed, reloading.
INFO 2025-06-30 03:13:16,581 autoreload 35 140315087166336 Watching for file changes with StatReloader
INFO 2025-06-30 03:13:40,487 autoreload 21 140015608003456 Watching for file changes with StatReloader
INFO 2025-06-30 03:19:52,811 autoreload 20 140480766720896 Watching for file changes with StatReloader
INFO 2025-06-30 03:20:41,515 autoreload 20 139891829328768 Watching for file changes with StatReloader
INFO 2025-06-30 03:21:44,135 autoreload 20 139645549501312 Watching for file changes with StatReloader
INFO 2025-06-30 03:22:19,300 autoreload 20 139645549501312 /code/logistics/views.py changed, reloading.
INFO 2025-06-30 03:22:21,396 autoreload 35 140332184628096 Watching for file changes with StatReloader
INFO 2025-06-30 03:22:36,661 autoreload 20 139842291735424 Watching for file changes with StatReloader
INFO 2025-06-30 03:23:16,838 autoreload 21 140070985087872 Watching for file changes with StatReloader
INFO 2025-06-30 03:23:34,879 basehttp 21 *************** "GET / HTTP/1.1" 302 0
ERROR 2025-06-30 03:23:34,934 log 21 *************** Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/contrib/sessions/backends/base.py", line 187, in _get_session
    return self._session_cache
           ^^^^^^^^^^^^^^^^^^^
AttributeError: 'SessionStore' object has no attribute '_session_cache'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
psycopg2.errors.UndefinedTable: relation "django_session" does not exist
LINE 1: ...ession_data", "django_session"."expire_date" FROM "django_se...
                                                             ^


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 22, in _wrapper_view
    if test_func(request.user):
       ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 51, in <lambda>
    lambda u: u.is_authenticated,
              ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/functional.py", line 266, in inner
    self._setup()
  File "/usr/local/lib/python3.11/site-packages/django/utils/functional.py", line 419, in _setup
    self._wrapped = self._setupfunc()
                    ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/middleware.py", line 25, in <lambda>
    request.user = SimpleLazyObject(lambda: get_user(request))
                                            ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/middleware.py", line 11, in get_user
    request._cached_user = auth.get_user(request)
                           ^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/__init__.py", line 191, in get_user
    user_id = _get_user_session_key(request)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/__init__.py", line 60, in _get_user_session_key
    return get_user_model()._meta.pk.to_python(request.session[SESSION_KEY])
                                               ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/sessions/backends/base.py", line 53, in __getitem__
    return self._session[key]
           ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/sessions/backends/base.py", line 192, in _get_session
    self._session_cache = self.load()
                          ^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/sessions/backends/db.py", line 42, in load
    s = self._get_session_from_db()
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/sessions/backends/db.py", line 32, in _get_session_from_db
    return self.model.objects.get(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 633, in get
    num = len(clone)
          ^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 380, in __len__
    self._fetch_all()
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 91, in __iter__
    results = compiler.execute_sql(
              ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 102, in execute
    return super().execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "/usr/local/lib/python3.11/site-packages/django/db/utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "/usr/local/lib/python3.11/site-packages/django/db/backends/utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
django.db.utils.ProgrammingError: relation "django_session" does not exist
LINE 1: ...ession_data", "django_session"."expire_date" FROM "django_se...
                                                             ^

ERROR 2025-06-30 03:23:34,938 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" 500 175034
INFO 2025-06-30 03:24:52,366 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:24:52,462 log 21 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:24:52,464 basehttp 21 *************** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 500 77436
INFO 2025-06-30 03:25:51,231 basehttp 21 *************** "GET / HTTP/1.1" 302 0
INFO 2025-06-30 03:25:51,248 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:25:51,332 log 21 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:25:51,336 basehttp 21 *************** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 500 76698
WARNING 2025-06-30 03:25:51,915 log 21 *************** Not Found: /favicon.ico
WARNING 2025-06-30 03:25:51,916 basehttp 21 *************** "GET /favicon.ico HTTP/1.1" 404 3772
ERROR 2025-06-30 03:25:58,828 log 21 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:25:58,830 basehttp 21 *************** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 500 77436
INFO 2025-06-30 03:26:18,827 basehttp 21 *************** "GET / HTTP/1.1" 302 0
INFO 2025-06-30 03:26:18,876 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:26:18,956 log 21 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:26:18,958 basehttp 21 *************** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 500 77299
INFO 2025-06-30 03:26:27,994 basehttp 21 *************** "GET /admin HTTP/1.1" 301 0
INFO 2025-06-30 03:26:28,016 basehttp 21 *************** "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-30 03:26:28,061 basehttp 21 *************** "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4190
INFO 2025-06-30 03:26:28,096 basehttp 21 *************** "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-30 03:26:28,097 basehttp 21 *************** "GET /static/admin/css/base.css HTTP/1.1" 200 21310
INFO 2025-06-30 03:26:28,114 basehttp 21 *************** "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-30 03:26:28,114 basehttp 21 *************** "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-06-30 03:26:28,116 basehttp 21 *************** "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-06-30 03:26:28,117 basehttp 21 *************** "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-30 03:26:28,117 basehttp 21 *************** "GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
INFO 2025-06-30 03:26:33,579 basehttp 21 *************** "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-06-30 03:26:33,614 basehttp 21 *************** "GET /admin/ HTTP/1.1" 200 10731
INFO 2025-06-30 03:26:33,645 basehttp 21 *************** "GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
INFO 2025-06-30 03:26:33,701 basehttp 21 *************** "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
INFO 2025-06-30 03:26:33,701 basehttp 21 *************** "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
WARNING 2025-06-30 03:26:46,900 log 21 *************** Not Found: /i
WARNING 2025-06-30 03:26:46,902 basehttp 21 *************** "GET /i HTTP/1.1" 404 3742
INFO 2025-06-30 03:26:53,492 basehttp 21 *************** "GET / HTTP/1.1" 302 0
ERROR 2025-06-30 03:26:53,634 log 21 *************** Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 821, in __init__
    self.literal = int(var)
                   ^^^^^^^^
ValueError: invalid literal for int() with base 10: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 832, in __init__
    self.literal = mark_safe(unescape_string_literal(var))
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/functional.py", line 246, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/text.py", line 483, in unescape_string_literal
    raise ValueError("Not a string literal: %r" % s)
ValueError: Not a string literal: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 91, in home_dashboard
    return render(request, 'dashboard/home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 697, in __init__
    args.append((True, Variable(var_arg)))
                       ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 837, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Variables and attributes may not begin with underscores: '_'
ERROR 2025-06-30 03:26:53,652 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" **********
ERROR 2025-06-30 03:27:44,428 log 21 *************** Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 821, in __init__
    self.literal = int(var)
                   ^^^^^^^^
ValueError: invalid literal for int() with base 10: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 832, in __init__
    self.literal = mark_safe(unescape_string_literal(var))
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/functional.py", line 246, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/text.py", line 483, in unescape_string_literal
    raise ValueError("Not a string literal: %r" % s)
ValueError: Not a string literal: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 91, in home_dashboard
    return render(request, 'dashboard/home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 697, in __init__
    args.append((True, Variable(var_arg)))
                       ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 837, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Variables and attributes may not begin with underscores: '_'
ERROR 2025-06-30 03:27:44,438 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" **********
ERROR 2025-06-30 03:27:45,391 log 21 *************** Internal Server Error: /dashboard/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 821, in __init__
    self.literal = int(var)
                   ^^^^^^^^
ValueError: invalid literal for int() with base 10: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 832, in __init__
    self.literal = mark_safe(unescape_string_literal(var))
                             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/functional.py", line 246, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/utils/text.py", line 483, in unescape_string_literal
    raise ValueError("Not a string literal: %r" % s)
ValueError: Not a string literal: '_'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 91, in home_dashboard
    return render(request, 'dashboard/home.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 132, in render
    compiled_parent = self.get_parent(context)
                      ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 129, in get_parent
    return self.find_template(parent, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 107, in find_template
    template, origin = context.template.engine.find_template(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 697, in __init__
    args.append((True, Variable(var_arg)))
                       ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 837, in __init__
    raise TemplateSyntaxError(
django.template.exceptions.TemplateSyntaxError: Variables and attributes may not begin with underscores: '_'
ERROR 2025-06-30 03:27:45,397 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" **********
INFO 2025-06-30 03:27:46,203 basehttp 21 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 03:27:46,252 basehttp 21 *************** "GET /static/css/main.css HTTP/1.1" 200 11927
INFO 2025-06-30 03:27:46,255 basehttp 21 *************** "GET /static/js/main.js HTTP/1.1" 200 26148
WARNING 2025-06-30 03:27:46,553 basehttp 21 *************** "GET /static/favicon.ico HTTP/1.1" 404 1893
ERROR 2025-06-30 03:27:59,465 log 21 *************** Internal Server Error: /dashboard/stations/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 168, in station_overview
    return render(request, 'dashboard/station_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/station_overview.html
ERROR 2025-06-30 03:27:59,471 basehttp 21 *************** "GET /dashboard/stations/ HTTP/1.1" 500 85045
INFO 2025-06-30 03:28:24,222 autoreload 20 *************** Watching for file changes with StatReloader
INFO 2025-06-30 03:28:27,367 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:28:27,412 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:28:27,418 basehttp 20 *************** "GET /accounts/login/?next=/dashboard/ HTTP/1.1" 500 76698
ERROR 2025-06-30 03:28:36,419 log 20 *************** Internal Server Error: /dashboard/stations/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 168, in station_overview
    return render(request, 'dashboard/station_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/station_overview.html
ERROR 2025-06-30 03:28:36,424 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 500 85182
ERROR 2025-06-30 03:28:54,104 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:28:54,111 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87518
ERROR 2025-06-30 03:29:02,413 log 20 *************** Internal Server Error: /optimization/start/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 153, in optimization_form
    return render(request, 'optimization/form.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/form.html
ERROR 2025-06-30 03:29:02,426 basehttp 20 *************** "GET /optimization/start/ HTTP/1.1" 500 85495
ERROR 2025-06-30 03:29:27,838 log 20 *************** Internal Server Error: /dashboard/stations/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 168, in station_overview
    return render(request, 'dashboard/station_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/station_overview.html
ERROR 2025-06-30 03:29:27,845 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 500 85045
INFO 2025-06-30 03:30:04,859 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 200 15568
ERROR 2025-06-30 03:30:12,157 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:30:12,163 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87527
INFO 2025-06-30 03:30:12,175 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:30:12,212 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:30:12,214 basehttp 20 *************** "GET /accounts/login/?next=/dashboard/stations/ HTTP/1.1" 500 76752
ERROR 2025-06-30 03:30:22,825 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:30:22,832 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87664
WARNING 2025-06-30 03:30:32,321 log 20 *************** Not Found: /rep
WARNING 2025-06-30 03:30:32,322 basehttp 20 *************** "GET /rep HTTP/1.1" 404 3748
INFO 2025-06-30 03:30:36,319 basehttp 20 *************** "GET / HTTP/1.1" 302 0
INFO 2025-06-30 03:30:36,391 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
ERROR 2025-06-30 03:30:38,073 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:30:38,082 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87518
ERROR 2025-06-30 03:30:41,485 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:30:41,495 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 91979
ERROR 2025-06-30 03:30:46,349 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/route_list.html
ERROR 2025-06-30 03:30:46,355 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 86978
ERROR 2025-06-30 03:30:49,206 log 20 *************** Internal Server Error: /dashboard/analytics/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 332, in analytics_dashboard
    product_stats = Produkt.objects.annotate(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1590, in annotate
    return self._annotate(args, kwargs, select=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1638, in _annotate
    clone.query.add_annotation(
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1133, in add_annotation
    annotation = annotation.resolve_expression(self, allow_joins=True, reuse=None)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/aggregates.py", line 65, in resolve_expression
    c.filter = c.filter and c.filter.resolve_expression(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query_utils.py", line 87, in resolve_expression
    clause, joins = query._add_q(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1313, in build_lookup
    lhs = self.try_transform(lhs, lookup_name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1357, in try_transform
    raise FieldError(
django.core.exceptions.FieldError: Unsupported lookup 'eshte_aktiv' for ManyToOneRel or join on the field not permitted.
ERROR 2025-06-30 03:30:49,215 basehttp 20 *************** "GET /dashboard/analytics/ HTTP/1.1" 500 125022
ERROR 2025-06-30 03:31:30,988 log 20 *************** Internal Server Error: /dashboard/analytics/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 332, in analytics_dashboard
    product_stats = Produkt.objects.annotate(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1590, in annotate
    return self._annotate(args, kwargs, select=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1638, in _annotate
    clone.query.add_annotation(
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1133, in add_annotation
    annotation = annotation.resolve_expression(self, allow_joins=True, reuse=None)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/aggregates.py", line 65, in resolve_expression
    c.filter = c.filter and c.filter.resolve_expression(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query_utils.py", line 87, in resolve_expression
    clause, joins = query._add_q(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1313, in build_lookup
    lhs = self.try_transform(lhs, lookup_name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1357, in try_transform
    raise FieldError(
django.core.exceptions.FieldError: Unsupported lookup 'eshte_aktiv' for ManyToOneRel or join on the field not permitted.
ERROR 2025-06-30 03:31:30,994 basehttp 20 *************** "GET /dashboard/analytics/ HTTP/1.1" 500 125022
INFO 2025-06-30 03:31:43,687 basehttp 20 *************** "POST /i18n/setlang/ HTTP/1.1" 302 0
WARNING 2025-06-30 03:31:43,747 log 20 *************** Not Found: /i18n/setlang/shboard/
WARNING 2025-06-30 03:31:43,749 basehttp 20 *************** "GET /i18n/setlang/shboard/ HTTP/1.1" 404 3878
ERROR 2025-06-30 03:31:51,957 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:31:51,970 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
INFO 2025-06-30 03:32:03,702 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 03:32:03,738 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
INFO 2025-06-30 03:32:03,739 basehttp 20 139834077304512 "GET /static/js/main.js HTTP/1.1" 304 0
ERROR 2025-06-30 03:32:05,760 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:32:05,765 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87655
ERROR 2025-06-30 03:32:41,092 log 20 *************** Internal Server Error: /dashboard/fleet/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 220, in fleet_overview
    return render(request, 'dashboard/fleet_overview.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/fleet_overview.html
ERROR 2025-06-30 03:32:41,097 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 500 87655
INFO 2025-06-30 03:34:14,126 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 200 16695
INFO 2025-06-30 03:34:25,322 basehttp 20 *************** "GET /api/system/health_check/ HTTP/1.1" 200 11007
INFO 2025-06-30 03:34:25,398 basehttp 20 *************** "GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
INFO 2025-06-30 03:34:25,399 basehttp 20 139834077304512 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3426
INFO 2025-06-30 03:34:25,401 basehttp 20 139833867499200 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
INFO 2025-06-30 03:34:25,405 basehttp 20 139833841272512 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3796
INFO 2025-06-30 03:34:25,411 basehttp 20 139833867499200 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
INFO 2025-06-30 03:34:25,414 basehttp 20 *************** "GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1793
INFO 2025-06-30 03:34:25,418 basehttp 20 139833841272512 "GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
INFO 2025-06-30 03:34:25,419 basehttp 20 139834077304512 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
INFO 2025-06-30 03:34:25,420 basehttp 20 139833849665216 "GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 200 87533
INFO 2025-06-30 03:34:25,421 basehttp 20 *************** "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 200 121457
INFO 2025-06-30 03:34:25,423 basehttp 20 *************** "GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 200 59
INFO 2025-06-30 03:34:25,436 basehttp 20 *************** "GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
WARNING 2025-06-30 03:34:34,315 log 20 *************** Not Found: /admin/doc/
WARNING 2025-06-30 03:34:34,317 basehttp 20 *************** "GET /admin/doc/ HTTP/1.1" 404 6222
ERROR 2025-06-30 03:34:40,146 log 20 *************** Internal Server Error: /dashboard/orders/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 289, in order_management
    return render(request, 'dashboard/order_management.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/order_management.html
ERROR 2025-06-30 03:34:40,152 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 500 88594
ERROR 2025-06-30 03:34:51,649 log 20 *************** Internal Server Error: /dashboard/orders/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 289, in order_management
    return render(request, 'dashboard/order_management.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/order_management.html
ERROR 2025-06-30 03:34:51,656 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 500 88731
ERROR 2025-06-30 03:34:52,696 log 20 *************** Internal Server Error: /dashboard/orders/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 289, in order_management
    return render(request, 'dashboard/order_management.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: dashboard/order_management.html
ERROR 2025-06-30 03:34:52,703 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 500 88731
INFO 2025-06-30 03:35:25,780 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:35:45,539 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:35:45,546 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 92123
ERROR 2025-06-30 03:36:32,091 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:36:32,099 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 92260
ERROR 2025-06-30 03:36:33,339 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:36:33,345 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 92260
INFO 2025-06-30 03:36:34,558 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
INFO 2025-06-30 03:36:34,592 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
INFO 2025-06-30 03:36:34,592 basehttp 20 139833867499200 "GET /static/js/main.js HTTP/1.1" 304 0
ERROR 2025-06-30 03:36:39,063 log 20 *************** Internal Server Error: /optimization/start/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 153, in optimization_form
    return render(request, 'optimization/form.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/form.html
ERROR 2025-06-30 03:36:39,069 basehttp 20 *************** "GET /optimization/start/ HTTP/1.1" 500 85639
ERROR 2025-06-30 03:36:42,622 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/route_list.html
ERROR 2025-06-30 03:36:42,632 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 87122
ERROR 2025-06-30 03:36:45,451 log 20 *************** Internal Server Error: /dashboard/analytics/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 332, in analytics_dashboard
    product_stats = Produkt.objects.annotate(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1590, in annotate
    return self._annotate(args, kwargs, select=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1638, in _annotate
    clone.query.add_annotation(
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1133, in add_annotation
    annotation = annotation.resolve_expression(self, allow_joins=True, reuse=None)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/aggregates.py", line 65, in resolve_expression
    c.filter = c.filter and c.filter.resolve_expression(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query_utils.py", line 87, in resolve_expression
    clause, joins = query._add_q(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1313, in build_lookup
    lhs = self.try_transform(lhs, lookup_name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1357, in try_transform
    raise FieldError(
django.core.exceptions.FieldError: Unsupported lookup 'eshte_aktiv' for ManyToOneRel or join on the field not permitted.
ERROR 2025-06-30 03:36:45,458 basehttp 20 *************** "GET /dashboard/analytics/ HTTP/1.1" 500 125166
INFO 2025-06-30 03:37:35,742 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
INFO 2025-06-30 03:38:12,660 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 03:38:25,996 basehttp 20 *************** "GET /optimization/quick/ HTTP/1.1" 200 65
WARNING 2025-06-30 03:38:29,511 log 20 *************** Method Not Allowed (GET): /optimization/emergency/
WARNING 2025-06-30 03:38:29,512 basehttp 20 *************** "GET /optimization/emergency/ HTTP/1.1" 405 0
INFO 2025-06-30 03:38:45,455 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 03:38:48,606 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:38:51,237 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:38:51,242 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 92123
INFO 2025-06-30 03:40:40,173 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:40:41,348 log 20 *************** Internal Server Error: /dashboard/analytics/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 332, in analytics_dashboard
    product_stats = Produkt.objects.annotate(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1590, in annotate
    return self._annotate(args, kwargs, select=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1638, in _annotate
    clone.query.add_annotation(
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1133, in add_annotation
    annotation = annotation.resolve_expression(self, allow_joins=True, reuse=None)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/aggregates.py", line 65, in resolve_expression
    c.filter = c.filter and c.filter.resolve_expression(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query_utils.py", line 87, in resolve_expression
    clause, joins = query._add_q(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1313, in build_lookup
    lhs = self.try_transform(lhs, lookup_name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1357, in try_transform
    raise FieldError(
django.core.exceptions.FieldError: Unsupported lookup 'eshte_aktiv' for ManyToOneRel or join on the field not permitted.
ERROR 2025-06-30 03:40:41,353 basehttp 20 *************** "GET /dashboard/analytics/ HTTP/1.1" 500 125166
INFO 2025-06-30 03:41:09,269 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 03:41:09,316 basehttp 20 139833867499200 "GET /static/js/main.js HTTP/1.1" 304 0
INFO 2025-06-30 03:41:09,318 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
ERROR 2025-06-30 03:41:36,984 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:41:36,996 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
INFO 2025-06-30 03:41:59,850 basehttp 20 *************** "GET /admin/logistics/stacion/ HTTP/1.1" 200 12065
INFO 2025-06-30 03:41:59,896 basehttp 20 *************** "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-30 03:41:59,905 basehttp 20 139833841272512 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-30 03:41:59,906 basehttp 20 139833867499200 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-30 03:41:59,908 basehttp 20 139833849665216 "GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
INFO 2025-06-30 03:41:59,916 basehttp 20 139833867499200 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-06-30 03:41:59,917 basehttp 20 139833841272512 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-30 03:41:59,920 basehttp 20 139833849665216 "GET /static/admin/js/core.js HTTP/1.1" 200 5682
INFO 2025-06-30 03:41:59,925 basehttp 20 139834077304512 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 03:41:59,926 basehttp 20 *************** "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 292458
INFO 2025-06-30 03:41:59,931 basehttp 20 139833849665216 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
INFO 2025-06-30 03:41:59,933 basehttp 20 139834077304512 "GET /static/admin/js/actions.js HTTP/1.1" 200 7872
INFO 2025-06-30 03:41:59,934 basehttp 20 *************** "GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
INFO 2025-06-30 03:41:59,939 basehttp 20 139834077304512 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-06-30 03:41:59,942 basehttp 20 *************** "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-06-30 03:41:59,949 basehttp 20 139834077304512 "GET /static/admin/img/search.svg HTTP/1.1" 200 458
INFO 2025-06-30 03:42:00,001 basehttp 20 139834077304512 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-30 03:42:00,073 basehttp 20 139834077304512 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-30 03:42:00,131 basehttp 20 139834077304512 "GET /static/admin/js/filters.js HTTP/1.1" 200 978
INFO 2025-06-30 03:42:00,152 basehttp 20 139833867499200 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
INFO 2025-06-30 03:42:00,153 basehttp 20 139833849665216 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
INFO 2025-06-30 03:42:02,406 basehttp 20 139834077304512 "GET /admin/logistics/stacion/add/ HTTP/1.1" 200 33533
INFO 2025-06-30 03:42:02,470 basehttp 20 *************** "GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
INFO 2025-06-30 03:42:02,477 basehttp 20 139833841272512 "GET /static/admin/js/collapse.js HTTP/1.1" 200 1803
INFO 2025-06-30 03:42:02,477 basehttp 20 139834077304512 "GET /static/admin/css/forms.css HTTP/1.1" 200 9090
INFO 2025-06-30 03:42:02,479 basehttp 20 139833867499200 "GET /static/admin/js/calendar.js HTTP/1.1" 200 8466
INFO 2025-06-30 03:42:02,481 basehttp 20 *************** "GET /static/admin/js/admin/DateTimeShortcuts.js HTTP/1.1" 200 19319
INFO 2025-06-30 03:42:02,485 basehttp 20 139833849665216 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 03:42:02,488 basehttp 20 *************** "GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
INFO 2025-06-30 03:42:02,496 basehttp 20 *************** "GET /static/admin/js/change_form.js HTTP/1.1" 200 606
INFO 2025-06-30 03:42:02,558 basehttp 20 *************** "GET /static/admin/img/gis/move_vertex_off.svg HTTP/1.1" 200 1129
INFO 2025-06-30 03:42:02,962 basehttp 20 *************** "GET /static/admin/img/icon-clock.svg HTTP/1.1" 200 677
INFO 2025-06-30 03:44:29,165 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:44:31,006 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/dashboard.html
ERROR 2025-06-30 03:44:31,011 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 500 92123
ERROR 2025-06-30 03:45:05,509 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:45:05,518 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
INFO 2025-06-30 03:46:59,453 autoreload 20 *************** Watching for file changes with StatReloader
INFO 2025-06-30 03:47:01,437 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:47:01,476 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:47:01,478 basehttp 20 *************** "GET /accounts/login/?next=/dashboard/fleet/ HTTP/1.1" 500 76734
INFO 2025-06-30 03:47:07,835 basehttp 20 *************** "GET /mobile/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:47:07,898 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:47:07,900 basehttp 20 *************** "GET /accounts/login/?next=/mobile/ HTTP/1.1" 500 76680
WARNING 2025-06-30 03:47:13,404 log 20 *************** Not Found: /reports/
WARNING 2025-06-30 03:47:13,405 basehttp 20 *************** "GET /reports/ HTTP/1.1" 404 3608
ERROR 2025-06-30 03:47:21,376 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:47:21,378 basehttp 20 *************** "GET /accounts/login/?next=/dashboard/fleet/ HTTP/1.1" 500 76871
INFO 2025-06-30 03:47:29,813 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
INFO 2025-06-30 03:47:29,861 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
INFO 2025-06-30 03:47:29,863 basehttp 20 *************** "GET /static/js/main.js HTTP/1.1" 304 0
ERROR 2025-06-30 03:47:34,591 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:47:34,608 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
ERROR 2025-06-30 03:48:55,369 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:48:55,376 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
ERROR 2025-06-30 03:48:57,086 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:48:57,093 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
ERROR 2025-06-30 03:48:59,177 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:48:59,186 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
ERROR 2025-06-30 03:49:25,666 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:49:25,674 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
INFO 2025-06-30 03:50:39,587 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:50:41,790 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:50:41,803 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
ERROR 2025-06-30 03:50:58,165 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:50:58,176 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
INFO 2025-06-30 03:51:04,297 autoreload 20 *************** Watching for file changes with StatReloader
INFO 2025-06-30 03:51:07,040 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 302 0
ERROR 2025-06-30 03:51:07,108 log 20 *************** Internal Server Error: /accounts/login/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 90, in rendered_content
    template = self.resolve_template(self.template_name)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/response.py", line 72, in resolve_template
    return select_template(template, using=self.using)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 47, in select_template
    raise TemplateDoesNotExist(", ".join(template_name_list), chain=chain)
django.template.exceptions.TemplateDoesNotExist: registration/login.html
ERROR 2025-06-30 03:51:07,112 basehttp 20 *************** "GET /accounts/login/?next=/optimization/ HTTP/1.1" 500 76716
ERROR 2025-06-30 03:51:15,669 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:51:15,690 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
ERROR 2025-06-30 03:51:17,536 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:51:17,543 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
ERROR 2025-06-30 03:51:21,483 log 20 *************** Internal Server Error: /mobile/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/mobile/views.py", line 33, in driver_dashboard
    return render(request, 'mobile/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 953, in do_if
    nodelist = parser.parse(("elif", "else", "endif"))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 484, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'sub'
ERROR 2025-06-30 03:51:21,500 basehttp 20 *************** "GET /mobile/ HTTP/1.1" **********
INFO 2025-06-30 03:52:15,918 basehttp 20 *************** "GET /mobile/ HTTP/1.1" 200 21577
INFO 2025-06-30 03:52:24,017 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 03:52:27,003 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 03:52:28,707 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:52:28,714 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
ERROR 2025-06-30 03:52:43,991 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:52:43,999 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
ERROR 2025-06-30 03:52:45,229 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 03:52:45,237 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
INFO 2025-06-30 03:53:13,955 basehttp 20 *************** "GET /optimization/start/ HTTP/1.1" 200 30247
INFO 2025-06-30 03:53:14,021 basehttp 20 140115207710400 "GET /static/js/main.js HTTP/1.1" 304 0
INFO 2025-06-30 03:53:14,022 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
ERROR 2025-06-30 03:53:23,146 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/route_list.html
ERROR 2025-06-30 03:53:23,155 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 87124
ERROR 2025-06-30 03:54:30,899 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: optimization/route_list.html
ERROR 2025-06-30 03:54:30,903 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 87261
ERROR 2025-06-30 03:55:00,506 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 880, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/context.py", line 83, in __getitem__
    raise KeyError(key)
KeyError: 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 886, in _resolve_lookup
    if isinstance(current, BaseContext) and getattr(
                                            ^^^^^^^^
AttributeError: type object 'RequestContext' has no attribute 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 896, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1064, in render
    output = self.filter_expression.resolve(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 736, in resolve
    arg_vals.append(arg.resolve(context))
                    ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 847, in resolve
    value = self._resolve_lookup(context)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 903, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [today] in [{'True': True, 'False': False, 'None': None}, {}, {}, {'page_title': 'Optimized Routes', 'routes': <Page 1 of 1>, 'trucks': <QuerySet []>, 'status_choices': [('draft', 'Draft'), ('miratuar', 'Approved'), ('ne_progres', 'In Progress'), ('perfunduar', 'Completed'), ('anulluar', 'Cancelled')], 'filters': {'date_from': '', 'date_to': '', 'truck': None, 'status': None}, 'LANGUAGE_CODE': 'en', 'languages': [('en', 'English'), ('sq', 'Albanian')]}]
ERROR 2025-06-30 03:55:00,514 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 222190
INFO 2025-06-30 03:56:44,170 basehttp 20 *************** "GET /optimization/start/ HTTP/1.1" 200 30247
ERROR 2025-06-30 03:56:48,749 log 20 *************** Internal Server Error: /optimization/routes/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 880, in _resolve_lookup
    current = current[bit]
              ~~~~~~~^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/context.py", line 83, in __getitem__
    raise KeyError(key)
KeyError: 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 886, in _resolve_lookup
    if isinstance(current, BaseContext) and getattr(
                                            ^^^^^^^^
AttributeError: type object 'RequestContext' has no attribute 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 896, in _resolve_lookup
    current = current[int(bit)]
                      ^^^^^^^^
ValueError: invalid literal for int() with base 10: 'today'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 294, in route_list
    return render(request, 'optimization/route_list.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 61, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 175, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 167, in _render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 966, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1064, in render
    output = self.filter_expression.resolve(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 736, in resolve
    arg_vals.append(arg.resolve(context))
                    ^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 847, in resolve
    value = self._resolve_lookup(context)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 903, in _resolve_lookup
    raise VariableDoesNotExist(
django.template.base.VariableDoesNotExist: Failed lookup for key [today] in [{'True': True, 'False': False, 'None': None}, {}, {}, {'page_title': 'Optimized Routes', 'routes': <Page 1 of 1>, 'trucks': <QuerySet []>, 'status_choices': [('draft', 'Draft'), ('miratuar', 'Approved'), ('ne_progres', 'In Progress'), ('perfunduar', 'Completed'), ('anulluar', 'Cancelled')], 'filters': {'date_from': '', 'date_to': '', 'truck': None, 'status': None}, 'LANGUAGE_CODE': 'en', 'languages': [('en', 'English'), ('sq', 'Albanian')]}]
ERROR 2025-06-30 03:56:48,758 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 500 222053
INFO 2025-06-30 22:50:17,736 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 200 17560
INFO 2025-06-30 22:50:17,791 basehttp 20 *************** "GET /static/css/main.css HTTP/1.1" 304 0
INFO 2025-06-30 22:50:17,798 basehttp 20 140115207710400 "GET /static/js/main.js HTTP/1.1" 304 0
INFO 2025-06-30 22:50:41,545 basehttp 20 *************** "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 22:50:42,357 basehttp 20 *************** "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 22:50:44,543 basehttp 20 *************** "GET /dashboard/fleet/ HTTP/1.1" 200 16695
INFO 2025-06-30 22:50:46,761 basehttp 20 *************** "GET /dashboard/orders/ HTTP/1.1" 200 20856
ERROR 2025-06-30 22:50:50,419 log 20 *************** Internal Server Error: /optimization/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/optimization/views.py", line 68, in optimization_dashboard
    return render(request, 'optimization/dashboard.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/backends/django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loaders/base.py", line 28, in get_template
    return Template(
           ^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 200, in compile_nodelist
    return parser.parse()
           ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
               ^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 853, in do_for
    nodelist_loop = parser.parse(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 513, in parse
    raise self.error(token, e)
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 511, in parse
    compiled_result = compile_func(self, token)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/defaulttags.py", line 1482, in do_with
    extra_context = token_kwargs(remaining_bits, parser, support_legacy=True)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 1116, in token_kwargs
    kwargs[key] = parser.compile_filter(value)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 600, in compile_filter
    return FilterExpression(token, self)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 698, in __init__
    filter_func = parser.find_filter(filter_name)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/template/base.py", line 606, in find_filter
    raise TemplateSyntaxError("Invalid filter: '%s'" % filter_name)
django.template.exceptions.TemplateSyntaxError: Invalid filter: 'get_item'
ERROR 2025-06-30 22:50:50,425 basehttp 20 *************** "GET /optimization/ HTTP/1.1" **********
INFO 2025-06-30 22:51:31,957 basehttp 20 *************** "GET /optimization/ HTTP/1.1" 200 46022
INFO 2025-06-30 22:51:55,849 basehttp 20 *************** "GET /optimization/start/ HTTP/1.1" 200 30247
INFO 2025-06-30 22:51:58,600 basehttp 20 *************** "GET /optimization/routes/ HTTP/1.1" 200 17560
ERROR 2025-06-30 22:52:02,803 log 20 *************** Internal Server Error: /dashboard/analytics/
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/core/handlers/base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/contrib/auth/decorators.py", line 23, in _wrapper_view
    return view_func(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/code/dashboard/views.py", line 332, in analytics_dashboard
    product_stats = Produkt.objects.annotate(
                    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1590, in annotate
    return self._annotate(args, kwargs, select=True)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query.py", line 1638, in _annotate
    clone.query.add_annotation(
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1133, in add_annotation
    annotation = annotation.resolve_expression(self, allow_joins=True, reuse=None)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/aggregates.py", line 65, in resolve_expression
    c.filter = c.filter and c.filter.resolve_expression(
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/query_utils.py", line 87, in resolve_expression
    clause, joins = query._add_q(
                    ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1577, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1492, in build_filter
    condition = self.build_lookup(lookups, col, value)
                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1313, in build_lookup
    lhs = self.try_transform(lhs, lookup_name)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/django/db/models/sql/query.py", line 1357, in try_transform
    raise FieldError(
django.core.exceptions.FieldError: Unsupported lookup 'eshte_aktiv' for ManyToOneRel or join on the field not permitted.
ERROR 2025-06-30 22:52:02,810 basehttp 20 *************** "GET /dashboard/analytics/ HTTP/1.1" 500 125169
INFO 2025-06-30 22:52:45,696 autoreload 20 *************** /code/dashboard/views.py changed, reloading.
INFO 2025-06-30 22:52:49,783 autoreload 41 140019806546816 Watching for file changes with StatReloader
INFO 2025-06-30 22:52:56,595 basehttp 41 140018895726272 "GET /dashboard/analytics/ HTTP/1.1" 200 22389
INFO 2025-06-30 22:53:16,952 basehttp 41 140018895726272 "GET /dashboard/analytics/ HTTP/1.1" 200 22389
INFO 2025-06-30 22:53:22,836 basehttp 41 140018895726272 "GET /optimization/routes/ HTTP/1.1" 200 17560
INFO 2025-06-30 22:54:55,519 basehttp 41 140018895726272 "GET /dashboard/fleet/ HTTP/1.1" 200 16695
INFO 2025-06-30 22:55:05,387 basehttp 41 140018895726272 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 22:55:24,658 basehttp 41 140018895726272 "GET /mobile/ HTTP/1.1" 200 21577
WARNING 2025-06-30 22:55:35,744 log 41 140018895726272 Method Not Allowed (GET): /optimization/emergency/
WARNING 2025-06-30 22:55:35,746 basehttp 41 140018895726272 "GET /optimization/emergency/ HTTP/1.1" 405 0
INFO 2025-06-30 22:55:46,766 basehttp 41 140018895726272 "GET /optimization/quick/ HTTP/1.1" 200 65
INFO 2025-06-30 23:00:02,014 basehttp 41 140018895726272 "GET /admin/logistics/depoqendrore/ HTTP/1.1" 200 11467
INFO 2025-06-30 23:00:02,081 basehttp 41 140018816382656 "GET /static/admin/css/dark_mode.css HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,086 basehttp 41 140018895726272 "GET /static/admin/css/base.css HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,087 basehttp 41 140018807989952 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,091 basehttp 41 140018799597248 "GET /static/admin/css/changelists.css HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,100 basehttp 41 140018791204544 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,102 basehttp 41 140018816382656 "GET /static/admin/css/responsive.css HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,103 basehttp 41 140018807989952 "GET /static/admin/js/jquery.init.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,103 basehttp 41 140018895726272 "GET /static/admin/js/core.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,107 basehttp 41 140018799597248 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,110 basehttp 41 140018791204544 "GET /static/admin/js/actions.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,121 basehttp 41 140018782811840 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:02,128 basehttp 41 140018782811840 "GET /static/admin/js/urlify.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,155 basehttp 41 140018807989952 "GET /static/admin/js/prepopulate.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,156 basehttp 41 140018816382656 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,158 basehttp 41 140018895726272 "GET /static/admin/img/search.svg HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,231 basehttp 41 140018816382656 "GET /static/admin/js/theme.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,232 basehttp 41 140018895726272 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,258 basehttp 41 140018807989952 "GET /static/admin/js/filters.js HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,271 basehttp 41 140018807989952 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 304 0
INFO 2025-06-30 23:00:02,273 basehttp 41 140018782811840 "GET /static/admin/img/tooltag-add.svg HTTP/1.1" 304 0
INFO 2025-06-30 23:00:04,884 basehttp 41 140018807989952 "GET /admin/logistics/particion/ HTTP/1.1" 200 11688
INFO 2025-06-30 23:00:04,941 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:06,293 basehttp 41 140018807989952 "GET /admin/logistics/shofer/ HTTP/1.1" 200 12306
INFO 2025-06-30 23:00:06,365 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:08,794 basehttp 41 140018807989952 "GET /admin/logistics/ngarkeseshkarkese/ HTTP/1.1" 200 11774
INFO 2025-06-30 23:00:08,863 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:10,141 basehttp 41 140018807989952 "GET /admin/logistics/porosi/ HTTP/1.1" 200 13441
INFO 2025-06-30 23:00:10,209 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:11,991 basehttp 41 140018807989952 "GET /admin/logistics/produkt/ HTTP/1.1" 200 12129
INFO 2025-06-30 23:00:12,137 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:13,842 basehttp 41 140018807989952 "GET /admin/logistics/planrruge/ HTTP/1.1" 200 12229
INFO 2025-06-30 23:00:13,906 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:15,279 basehttp 41 140018807989952 "GET /admin/logistics/stacion/ HTTP/1.1" 200 12065
INFO 2025-06-30 23:00:15,352 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:16,594 basehttp 41 140018807989952 "GET /admin/logistics/ndalese/ HTTP/1.1" 200 12080
INFO 2025-06-30 23:00:16,656 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:18,412 basehttp 41 140018807989952 "GET /admin/logistics/depozite/ HTTP/1.1" 200 11866
INFO 2025-06-30 23:00:18,504 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:19,751 basehttp 41 140018807989952 "GET /admin/logistics/kamion/ HTTP/1.1" 200 12834
INFO 2025-06-30 23:00:19,815 basehttp 41 140018807989952 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:00:43,842 basehttp 41 140018807989952 "GET /optimization/ HTTP/1.1" 200 41836
INFO 2025-06-30 23:00:51,543 basehttp 41 140018807989952 "POST /optimization/start/ HTTP/1.1" 302 0
INFO 2025-06-30 23:00:51,628 basehttp 41 140018807989952 "GET /optimization/start/ HTTP/1.1" 200 30605
INFO 2025-06-30 23:01:05,026 basehttp 41 140018807989952 "GET /optimization/ HTTP/1.1" 200 41836
INFO 2025-06-30 23:01:21,746 basehttp 41 140018807989952 "GET /dashboard/analytics/ HTTP/1.1" 200 22389
INFO 2025-06-30 23:01:59,605 basehttp 41 140018807989952 "GET /api/system/health_check/ HTTP/1.1" 200 11007
INFO 2025-06-30 23:01:59,673 basehttp 41 140018807989952 "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,674 basehttp 41 140018816382656 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,683 basehttp 41 140018782811840 "GET /static/rest_framework/css/default.css HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,687 basehttp 41 140018807989952 "GET /static/rest_framework/js/csrf.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,689 basehttp 41 140018791204544 "GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,691 basehttp 41 140018816382656 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,691 basehttp 41 140018895726272 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,694 basehttp 41 140018782811840 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,696 basehttp 41 140018799597248 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,699 basehttp 41 140018791204544 "GET /static/rest_framework/js/default.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,700 basehttp 41 140018895726272 "GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 304 0
INFO 2025-06-30 23:01:59,704 basehttp 41 140018799597248 "GET /static/rest_framework/img/grid.png HTTP/1.1" 304 0
WARNING 2025-06-30 23:02:12,260 log 41 140018799597248 Not Found: /admin/doc/
WARNING 2025-06-30 23:02:12,261 basehttp 41 140018799597248 "GET /admin/doc/ HTTP/1.1" 404 6222
INFO 2025-06-30 23:02:25,466 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:02:33,654 basehttp 41 140018799597248 "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 23:05:18,708 basehttp 41 140018799597248 "GET /dashboard/stations/?search=&region=Tirana HTTP/1.1" 200 15628
INFO 2025-06-30 23:05:23,395 basehttp 41 140018799597248 "GET /dashboard/stations/?search=&region=Shkoder HTTP/1.1" 200 15630
INFO 2025-06-30 23:05:26,823 basehttp 41 140018799597248 "GET /dashboard/stations/?search=&region=Durres HTTP/1.1" 200 15628
INFO 2025-06-30 23:05:31,660 basehttp 41 140018799597248 "GET /dashboard/stations/?search=&region=Vlore HTTP/1.1" 200 15626
INFO 2025-06-30 23:05:33,584 basehttp 41 140018799597248 "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 23:05:48,322 basehttp 41 140018799597248 "GET /dashboard/stations/?search=new+york&region= HTTP/1.1" 200 15632
INFO 2025-06-30 23:05:49,562 basehttp 41 140018799597248 "GET /dashboard/stations/?search=new+york&region= HTTP/1.1" 200 15632
INFO 2025-06-30 23:05:51,075 basehttp 41 140018799597248 "GET /dashboard/stations/?search=new+york&region= HTTP/1.1" 200 15632
INFO 2025-06-30 23:05:52,149 basehttp 41 140018799597248 "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 23:06:30,283 basehttp 41 140018799597248 "GET /dashboard/analytics/ HTTP/1.1" 200 22389
INFO 2025-06-30 23:06:41,604 basehttp 41 140018799597248 "GET /dashboard/orders/ HTTP/1.1" 200 20856
INFO 2025-06-30 23:06:44,263 basehttp 41 140018799597248 "GET /dashboard/fleet/ HTTP/1.1" 200 16695
INFO 2025-06-30 23:06:45,433 basehttp 41 140018799597248 "GET /dashboard/stations/ HTTP/1.1" 200 15568
INFO 2025-06-30 23:06:49,480 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:06:52,088 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:06:58,227 basehttp 41 140018799597248 "GET /dashboard/analytics/ HTTP/1.1" 200 22389
WARNING 2025-06-30 23:07:02,783 log 41 140018799597248 Method Not Allowed (GET): /optimization/emergency/
WARNING 2025-06-30 23:07:02,786 basehttp 41 140018799597248 "GET /optimization/emergency/ HTTP/1.1" 405 0
INFO 2025-06-30 23:07:06,533 basehttp 41 140018799597248 "GET /optimization/quick/ HTTP/1.1" 200 65
INFO 2025-06-30 23:34:16,774 basehttp 41 140018799597248 "GET /admin/logistics/kamion/ HTTP/1.1" 200 12834
INFO 2025-06-30 23:34:16,853 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:35:59,027 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:36:03,457 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:36:04,650 basehttp 41 140018799597248 "GET /dashboard/ HTTP/1.1" 200 21594
INFO 2025-06-30 23:36:09,831 basehttp 41 140018799597248 "GET /admin/logistics/kamion/ HTTP/1.1" 200 12834
INFO 2025-06-30 23:36:09,877 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:12,347 basehttp 41 140018799597248 "GET /admin/logistics/depoqendrore/ HTTP/1.1" 200 11467
INFO 2025-06-30 23:36:12,411 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:13,753 basehttp 41 140018799597248 "GET /admin/logistics/particion/ HTTP/1.1" 200 11688
INFO 2025-06-30 23:36:13,823 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:14,769 basehttp 41 140018799597248 "GET /admin/logistics/shofer/ HTTP/1.1" 200 12306
INFO 2025-06-30 23:36:14,831 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:16,537 basehttp 41 140018799597248 "GET /admin/logistics/porosi/ HTTP/1.1" 200 13441
INFO 2025-06-30 23:36:16,603 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:17,258 basehttp 41 140018799597248 "GET /admin/logistics/produkt/ HTTP/1.1" 200 12129
INFO 2025-06-30 23:36:17,318 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:18,488 basehttp 41 140018799597248 "GET /admin/logistics/planrruge/ HTTP/1.1" 200 12229
INFO 2025-06-30 23:36:18,548 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:19,389 basehttp 41 140018799597248 "GET /admin/logistics/stacion/ HTTP/1.1" 200 12065
INFO 2025-06-30 23:36:19,453 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:20,948 basehttp 41 140018799597248 "GET /admin/logistics/depozite/ HTTP/1.1" 200 11866
INFO 2025-06-30 23:36:21,025 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:36:22,329 basehttp 41 140018799597248 "GET /admin/auth/group/ HTTP/1.1" 200 10895
INFO 2025-06-30 23:36:22,436 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:37:16,376 basehttp 41 140018799597248 "GET /admin/auth/group/ HTTP/1.1" 200 10895
INFO 2025-06-30 23:37:16,445 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:37:17,393 basehttp 41 140018799597248 "GET /admin/auth/group/ HTTP/1.1" 200 10895
INFO 2025-06-30 23:37:17,493 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:37:18,337 basehttp 41 140018799597248 "GET /admin/auth/group/ HTTP/1.1" 200 10895
INFO 2025-06-30 23:37:18,405 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:37:19,316 basehttp 41 140018799597248 "GET /admin/auth/group/ HTTP/1.1" 200 10895
INFO 2025-06-30 23:37:19,391 basehttp 41 140018799597248 "GET /admin/jsi18n/ HTTP/1.1" 200 3343
INFO 2025-06-30 23:37:42,293 autoreload 31036 35352 Watching for file changes with StatReloader
