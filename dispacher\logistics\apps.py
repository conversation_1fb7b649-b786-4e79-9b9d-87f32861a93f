# logistics/apps.py
from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class LogisticsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'logistics'
    verbose_name = _('Logistics Management')
    
    def ready(self):
        """
        Initialize the app when Django starts
        """
        # Import signal handlers
        import logistics.signals
