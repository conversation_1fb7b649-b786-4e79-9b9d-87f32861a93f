<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#007bff">
    
    <title>{% block title %}OptiKarburant Mobile{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Leaflet CSS for maps -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8f9fa;
            padding-bottom: 80px; /* Space for bottom navigation */
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        /* Mobile-optimized header */
        .mobile-header {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 1rem;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .mobile-header h1 {
            font-size: 1.25rem;
            margin: 0;
            font-weight: 600;
        }
        
        .mobile-header .subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }
        
        /* Content area */
        .mobile-content {
            padding: 1rem;
            min-height: calc(100vh - 140px);
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 0.5rem 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .bottom-nav .nav-item {
            flex: 1;
            text-align: center;
        }
        
        .bottom-nav .nav-link {
            color: var(--secondary-color);
            text-decoration: none;
            padding: 0.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
            transition: color 0.2s;
        }
        
        .bottom-nav .nav-link.active {
            color: var(--primary-color);
        }
        
        .bottom-nav .nav-link i {
            font-size: 1.2rem;
            margin-bottom: 0.25rem;
        }
        
        /* Mobile-optimized cards */
        .mobile-card {
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }
        
        /* Touch-friendly buttons */
        .btn {
            min-height: 44px;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        
        .btn-lg {
            min-height: 56px;
            font-size: 1.1rem;
        }
        
        /* Form controls */
        .form-control, .form-select {
            min-height: 44px;
            border-radius: 0.5rem;
            font-size: 1rem;
        }
        
        /* Status indicators */
        .status-online {
            color: var(--success-color);
        }
        
        .status-offline {
            color: var(--danger-color);
        }
        
        .status-warning {
            color: var(--warning-color);
        }
        
        /* Loading spinner */
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        /* Responsive utilities */
        @media (max-width: 576px) {
            .mobile-content {
                padding: 0.75rem;
            }
            
            .mobile-header {
                padding: 0.75rem;
            }
            
            .mobile-header h1 {
                font-size: 1.1rem;
            }
        }
        
        /* PWA styles */
        .pwa-install-banner {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem;
            text-align: center;
            position: relative;
        }
        
        .pwa-install-banner .close-btn {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
        }
        
        /* Offline indicator */
        .offline-indicator {
            background: var(--warning-color);
            color: #212529;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.9rem;
            display: none;
        }
        
        /* Custom scrollbar for webkit browsers */
        ::-webkit-scrollbar {
            width: 4px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 2px;
        }
        
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-up {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- PWA Install Banner -->
    <div id="pwaInstallBanner" class="pwa-install-banner" style="display: none;">
        <i class="fas fa-download me-2"></i>
        Install OptiKarburant for better experience
        <button class="close-btn" onclick="closePWABanner()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    
    <!-- Offline Indicator -->
    <div id="offlineIndicator" class="offline-indicator">
        <i class="fas fa-wifi-slash me-2"></i>
        You are currently offline. Some features may not be available.
    </div>
    
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>{% block page_title %}OptiKarburant{% endblock %}</h1>
                <p class="subtitle">{% block page_subtitle %}Mobile Driver App{% endblock %}</p>
            </div>
            <div class="d-flex align-items-center gap-3">
                <!-- Connection Status -->
                <div id="connectionStatus" class="status-online">
                    <i class="fas fa-wifi"></i>
                </div>
                
                <!-- User Menu -->
                <div class="dropdown">
                    <button class="btn btn-link text-white p-0" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle fa-lg"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><h6 class="dropdown-header">
                            {% if user.is_authenticated %}
                                {{ user.get_full_name|default:user.username }}
                            {% else %}
                                Guest User
                            {% endif %}
                        </h6></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{% url 'mobile:help' %}">
                            <i class="fas fa-question-circle me-2"></i> Help
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="toggleNotifications()">
                            <i class="fas fa-bell me-2"></i> Notifications
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showSettings()">
                            <i class="fas fa-cog me-2"></i> Settings
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        {% if user.is_authenticated %}
                            <li><a class="dropdown-item" href="{% url 'logout' %}">
                                <i class="fas fa-sign-out-alt me-2"></i> Logout
                            </a></li>
                        {% else %}
                            <li><a class="dropdown-item" href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-2"></i> Login
                            </a></li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Main Content -->
    <main class="mobile-content">
        {% block content %}
        <div class="text-center py-5">
            <i class="fas fa-mobile-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Welcome to OptiKarburant Mobile</h5>
            <p class="text-muted">Your mobile companion for fuel delivery operations</p>
        </div>
        {% endblock %}
    </main>
    
    <!-- Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="d-flex">
            <div class="nav-item">
                <a href="{% url 'mobile:dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="showRoutes()">
                    <i class="fas fa-route"></i>
                    <span>Routes</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="#" class="nav-link" onclick="showDeliveries()">
                    <i class="fas fa-truck"></i>
                    <span>Deliveries</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="{% url 'mobile:help' %}" class="nav-link {% if request.resolver_match.url_name == 'help' %}active{% endif %}">
                    <i class="fas fa-question-circle"></i>
                    <span>Help</span>
                </a>
            </div>
        </div>
    </nav>
    
    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Leaflet JS for maps -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    
    <script>
        // Mobile app initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize mobile features
            initializeMobileApp();
            
            // Check for PWA install prompt
            checkPWAInstall();
            
            // Monitor connection status
            monitorConnection();
            
            // Enable touch gestures
            enableTouchGestures();
        });
        
        function initializeMobileApp() {
            // Prevent zoom on double tap
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
            
            // Add loading states to buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (!this.disabled) {
                        showLoading();
                        setTimeout(hideLoading, 2000); // Hide after 2 seconds max
                    }
                });
            });
        }
        
        function checkPWAInstall() {
            // Check if app is already installed
            if (window.matchMedia('(display-mode: standalone)').matches) {
                return; // Already installed
            }
            
            // Show install banner after 30 seconds
            setTimeout(() => {
                const banner = document.getElementById('pwaInstallBanner');
                if (banner && !localStorage.getItem('pwa-banner-dismissed')) {
                    banner.style.display = 'block';
                }
            }, 30000);
        }
        
        function closePWABanner() {
            document.getElementById('pwaInstallBanner').style.display = 'none';
            localStorage.setItem('pwa-banner-dismissed', 'true');
        }
        
        function monitorConnection() {
            const indicator = document.getElementById('connectionStatus');
            const offlineIndicator = document.getElementById('offlineIndicator');
            
            function updateConnectionStatus() {
                if (navigator.onLine) {
                    indicator.className = 'status-online';
                    indicator.innerHTML = '<i class="fas fa-wifi"></i>';
                    offlineIndicator.style.display = 'none';
                } else {
                    indicator.className = 'status-offline';
                    indicator.innerHTML = '<i class="fas fa-wifi-slash"></i>';
                    offlineIndicator.style.display = 'block';
                }
            }
            
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
            updateConnectionStatus();
        }
        
        function enableTouchGestures() {
            // Add swipe gestures for navigation
            let startX, startY;
            
            document.addEventListener('touchstart', function(e) {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });
            
            document.addEventListener('touchend', function(e) {
                if (!startX || !startY) return;
                
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                
                const diffX = startX - endX;
                const diffY = startY - endY;
                
                // Horizontal swipe
                if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                    if (diffX > 0) {
                        // Swipe left - next page
                        console.log('Swipe left detected');
                    } else {
                        // Swipe right - previous page
                        console.log('Swipe right detected');
                    }
                }
                
                startX = startY = null;
            });
        }
        
        function showLoading() {
            document.querySelector('.loading-spinner').style.display = 'block';
        }
        
        function hideLoading() {
            document.querySelector('.loading-spinner').style.display = 'none';
        }
        
        // Navigation functions
        function showRoutes() {
            // This would typically navigate to routes page
            alert('Routes feature - navigate to routes list');
        }
        
        function showDeliveries() {
            // This would typically navigate to deliveries page
            alert('Deliveries feature - navigate to active deliveries');
        }
        
        function toggleNotifications() {
            alert('Notifications feature coming soon!');
        }
        
        function showSettings() {
            alert('Settings feature coming soon!');
        }
        
        // Utility functions for child templates
        window.mobileUtils = {
            showLoading: showLoading,
            hideLoading: hideLoading,
            showToast: function(message, type = 'info') {
                // Simple toast notification
                const toast = document.createElement('div');
                toast.className = `alert alert-${type} position-fixed top-0 start-50 translate-middle-x mt-3`;
                toast.style.zIndex = '9999';
                toast.textContent = message;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }
        };
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
