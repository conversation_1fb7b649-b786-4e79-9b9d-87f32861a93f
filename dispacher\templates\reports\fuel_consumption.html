<!-- templates/reports/fuel_consumption.html - Fuel Consumption Report -->
{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .consumption-card {
        border: none;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }
    .consumption-card:hover {
        transform: translateY(-2px);
    }
    .fuel-metric {
        text-align: center;
        padding: 1.5rem;
        border-radius: 0.75rem;
        margin-bottom: 1rem;
        color: white;
    }
    .fuel-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .fuel-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .fuel-change {
        font-size: 0.8rem;
        margin-top: 0.5rem;
    }
    .chart-container {
        position: relative;
        height: 350px;
    }
    .truck-row {
        border-bottom: 1px solid #e9ecef;
        padding: 1rem 0;
    }
    .truck-row:last-child {
        border-bottom: none;
    }
    .efficiency-indicator {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.8rem;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    .cost-breakdown {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
    }
</style>
{% endblock %}

{% block page_icon %}<i class="fas fa-gas-pump text-primary"></i>{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'reports:dashboard' %}">Reports</a></li>
            <li class="breadcrumb-item active">Fuel Consumption</li>
        </ol>
    </nav>

    <!-- Filter Section -->
    <div class="filter-section">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-2">
                <label class="form-label">Start Date</label>
                <input type="date" name="start_date" class="form-control" 
                       value="{{ start_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">End Date</label>
                <input type="date" name="end_date" class="form-control" 
                       value="{{ end_date|date:'Y-m-d' }}">
            </div>
            <div class="col-md-2">
                <label class="form-label">Truck</label>
                <select name="truck_filter" class="form-select">
                    <option value="">All Trucks</option>
                    {% for truck in available_trucks %}
                        <option value="{{ truck.id }}" {% if truck_filter == truck.id|stringformat:"s" %}selected{% endif %}>
                            {{ truck.emri }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Product Type</label>
                <select name="product_filter" class="form-select">
                    <option value="">All Products</option>
                    {% for product in available_products %}
                        <option value="{{ product.id }}" {% if product_filter == product.id|stringformat:"s" %}selected{% endif %}>
                            {{ product.emri }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">View By</label>
                <select name="group_by" class="form-select">
                    <option value="day" {% if group_by == 'day' %}selected{% endif %}>Daily</option>
                    <option value="week" {% if group_by == 'week' %}selected{% endif %}>Weekly</option>
                    <option value="month" {% if group_by == 'month' %}selected{% endif %}>Monthly</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter"></i> Apply
                </button>
            </div>
        </form>
    </div>

    <!-- Fuel Consumption Overview -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="fuel-metric bg-primary">
                <div class="fuel-value">{{ consumption_stats.total_fuel_delivered|floatformat:0 }}</div>
                <div class="fuel-label">Total Liters Delivered</div>
                <div class="fuel-change">
                    <i class="fas fa-arrow-{{ consumption_stats.fuel_trend_direction }}"></i>
                    {{ consumption_stats.fuel_change_percentage|floatformat:1 }}% vs last period
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="fuel-metric bg-success">
                <div class="fuel-value">${{ consumption_stats.total_revenue|floatformat:0 }}</div>
                <div class="fuel-label">Total Revenue</div>
                <div class="fuel-change">
                    <i class="fas fa-arrow-{{ consumption_stats.revenue_trend_direction }}"></i>
                    {{ consumption_stats.revenue_change_percentage|floatformat:1 }}% vs last period
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="fuel-metric bg-warning">
                <div class="fuel-value">${{ consumption_stats.avg_cost_per_liter|floatformat:2 }}</div>
                <div class="fuel-label">Avg. Cost per Liter</div>
                <div class="fuel-change">
                    <i class="fas fa-arrow-{{ consumption_stats.cost_trend_direction }}"></i>
                    {{ consumption_stats.cost_change_percentage|floatformat:1 }}% vs last period
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="fuel-metric bg-info">
                <div class="fuel-value">{{ consumption_stats.avg_efficiency|floatformat:1 }}</div>
                <div class="fuel-label">Avg. L/km Efficiency</div>
                <div class="fuel-change">
                    <i class="fas fa-arrow-{{ consumption_stats.efficiency_trend_direction }}"></i>
                    {{ consumption_stats.efficiency_change_percentage|floatformat:1 }}% vs last period
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card consumption-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line"></i> Fuel Consumption Trends
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="consumptionTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card consumption-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie"></i> Product Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="productDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cost Breakdown -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="cost-breakdown">
                <h5 class="mb-3">
                    <i class="fas fa-calculator"></i> Cost Breakdown Analysis
                </h5>
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="fuel-value">${{ cost_breakdown.fuel_cost|floatformat:0 }}</div>
                        <div class="fuel-label">Fuel Costs</div>
                        <small>{{ cost_breakdown.fuel_percentage|floatformat:1 }}% of total</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="fuel-value">${{ cost_breakdown.transport_cost|floatformat:0 }}</div>
                        <div class="fuel-label">Transport Costs</div>
                        <small>{{ cost_breakdown.transport_percentage|floatformat:1 }}% of total</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="fuel-value">${{ cost_breakdown.maintenance_cost|floatformat:0 }}</div>
                        <div class="fuel-label">Maintenance</div>
                        <small>{{ cost_breakdown.maintenance_percentage|floatformat:1 }}% of total</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="fuel-value">${{ cost_breakdown.other_cost|floatformat:0 }}</div>
                        <div class="fuel-label">Other Costs</div>
                        <small>{{ cost_breakdown.other_percentage|floatformat:1 }}% of total</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Consumption Details -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card consumption-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> Daily Consumption Breakdown
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dailyConsumptionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Fleet Efficiency Analysis -->
    <div class="row">
        <div class="col-12">
            <div class="card consumption-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-truck"></i> Fleet Fuel Efficiency
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="exportFleetData('excel')">
                            <i class="fas fa-file-excel"></i> Excel
                        </button>
                        <button class="btn btn-outline-danger" onclick="exportFleetData('pdf')">
                            <i class="fas fa-file-pdf"></i> PDF
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% for truck in fleet_consumption %}
                    <div class="truck-row">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="mb-1">{{ truck.name }}</h6>
                                <small class="text-muted">{{ truck.plate_number }}</small>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="fuel-value text-primary" style="font-size: 1.2rem;">
                                        {{ truck.total_fuel_delivered|floatformat:0 }}
                                    </div>
                                    <div class="fuel-label">Liters</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="fuel-value text-success" style="font-size: 1.2rem;">
                                        {{ truck.total_distance|floatformat:0 }}
                                    </div>
                                    <div class="fuel-label">Distance (km)</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="fuel-value text-info" style="font-size: 1.2rem;">
                                        ${{ truck.total_cost|floatformat:0 }}
                                    </div>
                                    <div class="fuel-label">Total Cost</div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="text-center">
                                    <div class="efficiency-indicator bg-{{ truck.efficiency_color }} text-white">
                                        {{ truck.efficiency_rating|floatformat:1 }}
                                    </div>
                                    <div class="fuel-label">L/km</div>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                            data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="viewTruckDetails({{ truck.id }})">
                                            <i class="fas fa-eye"></i> View Details
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="analyzeTruckEfficiency({{ truck.id }})">
                                            <i class="fas fa-chart-line"></i> Analyze Efficiency
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="scheduleMaintenance({{ truck.id }})">
                                            <i class="fas fa-wrench"></i> Schedule Maintenance
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Efficiency Trend -->
                        <div class="row mt-2">
                            <div class="col-12">
                                <small class="text-muted">Recent efficiency trend:</small>
                                <div class="progress" style="height: 6px;">
                                    <div class="progress-bar bg-{{ truck.trend_color }}" 
                                         style="width: {{ truck.efficiency_percentage }}%"></div>
                                </div>
                                <small class="text-{{ truck.trend_color }}">
                                    <i class="fas fa-arrow-{{ truck.trend_direction }}"></i>
                                    {{ truck.efficiency_change|floatformat:1 }}% vs last period
                                </small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-5">
                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No consumption data found</h5>
                        <p class="text-muted">Try adjusting your filter criteria.</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Consumption Trends Chart
const trendsCtx = document.getElementById('consumptionTrendsChart').getContext('2d');
const trendsChart = new Chart(trendsCtx, {
    type: 'line',
    data: {
        labels: {{ consumption_chart_labels|safe }},
        datasets: [{
            label: 'Fuel Delivered (L)',
            data: {{ consumption_chart_data|safe }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true,
            yAxisID: 'y'
        }, {
            label: 'Cost ($)',
            data: {{ cost_chart_data|safe }},
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Liters'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Cost ($)'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Product Distribution Chart
const productCtx = document.getElementById('productDistributionChart').getContext('2d');
const productChart = new Chart(productCtx, {
    type: 'doughnut',
    data: {
        labels: {{ product_chart_labels|safe }},
        datasets: [{
            data: {{ product_chart_data|safe }},
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#dc3545',
                '#6c757d',
                '#17a2b8'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Daily Consumption Chart
const dailyCtx = document.getElementById('dailyConsumptionChart').getContext('2d');
const dailyChart = new Chart(dailyCtx, {
    type: 'bar',
    data: {
        labels: {{ daily_chart_labels|safe }},
        datasets: {{ daily_consumption|safe }}
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            x: {
                stacked: true
            },
            y: {
                stacked: true,
                title: {
                    display: true,
                    text: 'Liters'
                }
            }
        }
    }
});

// Export functions
function exportFleetData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.open(`${window.location.pathname}?${params.toString()}`, '_blank');
}

// Truck analysis functions
function viewTruckDetails(truckId) {
    window.location.href = `/reports/trucks/${truckId}/details/`;
}

function analyzeTruckEfficiency(truckId) {
    window.location.href = `/reports/trucks/${truckId}/efficiency/`;
}

function scheduleMaintenance(truckId) {
    alert('Maintenance scheduling feature coming soon!');
}
</script>
{% endblock %}
