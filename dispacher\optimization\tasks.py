# optimization/tasks.py - Celery tasks for route optimization

import logging
from datetime import datetime, timedelta
from typing import List, Dict

from celery import shared_task
from django.utils import timezone
from django.conf import settings
from django.contrib.auth.models import User

from logistics.models import (
    <PERSON><PERSON><PERSON>, <PERSON>, PlanRruge, Ndalese, NgarkeseShkarkese,
    Particion, Stacion, DepoQendrore
)
from .engine import OptimizationRequest, run_optimization

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def optimize_routes_for_date(self, target_date_str: str, user_id: int = None, 
                           truck_ids: List[int] = None, order_ids: List[int] = None):
    """
    Main task for optimizing routes for a specific date
    
    Args:
        target_date_str: Date in YYYY-MM-DD format
        user_id: User who requested optimization
        truck_ids: List of truck IDs to use (optional, uses all available if None)
        order_ids: List of order IDs to include (optional, uses all open if None)
    """
    try:
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': 100, 'status': 'Preparing data...'}
        )
        
        # Parse target date
        target_date = datetime.strptime(target_date_str, '%Y-%m-%d').date()
        
        # Get user if provided
        user = None
        if user_id:
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                pass
        
        logger.info(f"Starting route optimization for {target_date} by user {user}")
        
        # Prepare optimization request
        request = _prepare_optimization_request(
            target_date, truck_ids, order_ids
        )
        
        if not request.available_trucks:
            return {
                'success': False,
                'message': 'No available trucks found',
                'routes': []
            }
        
        if not request.open_orders:
            return {
                'success': False,
                'message': 'No open orders found',
                'routes': []
            }
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': 'Running optimization...'}
        )
        
        # Run optimization
        result = run_optimization(request)
        
        if not result.success:
            return {
                'success': False,
                'message': result.message,
                'routes': []
            }
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 80, 'total': 100, 'status': 'Saving routes...'}
        )
        
        # Save optimization results
        route_plans = _save_optimization_results(result, target_date, user)
        
        # Update order statuses
        _update_order_statuses(result)
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'current': 100, 'total': 100, 'status': 'Completed'}
        )
        
        logger.info(f"Optimization completed successfully: {len(route_plans)} routes created")
        
        return {
            'success': True,
            'message': f'Successfully created {len(route_plans)} routes',
            'routes': [{'id': rp.id, 'number': rp.numri_rrugese} for rp in route_plans],
            'total_distance_km': result.total_distance_km,
            'total_duration_hours': result.total_duration_hours,
            'total_cost': result.total_cost,
            'unassigned_orders': result.unassigned_orders,
            'optimization_score': result.optimization_score
        }
        
    except Exception as e:
        logger.error(f"Optimization task failed: {e}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        return {
            'success': False,
            'message': f'Optimization failed: {str(e)}',
            'routes': []
        }


def _prepare_optimization_request(target_date, truck_ids=None, order_ids=None) -> OptimizationRequest:
    """Prepare optimization request with data validation"""
    
    # Get available trucks
    available_trucks_query = Kamion.objects.filter(
        statusi='i_lire',
        eshte_aktiv=True
    )
    
    if truck_ids:
        available_trucks_query = available_trucks_query.filter(id__in=truck_ids)
    
    available_truck_ids = list(available_trucks_query.values_list('id', flat=True))
    
    # Get open orders
    open_orders_query = Porosi.objects.filter(
        statusi='e_hapur',
        data_afati__gte=timezone.now()
    )
    
    if order_ids:
        open_orders_query = open_orders_query.filter(id__in=order_ids)
    
    open_order_ids = list(open_orders_query.values_list('id', flat=True))
    
    # Get optimization settings
    opt_settings = settings.OPTIKARBURANT_SETTINGS['OPTIMIZATION']
    
    return OptimizationRequest(
        target_date=target_date,
        available_trucks=available_truck_ids,
        open_orders=open_order_ids,
        max_route_duration_hours=opt_settings['MAX_ROUTE_DURATION_HOURS'],
        optimization_timeout_seconds=opt_settings['OPTIMIZATION_TIMEOUT_SECONDS']
    )


def _save_optimization_results(result, target_date, user=None) -> List[PlanRruge]:
    """Save optimization results to database"""
    
    route_plans = []
    
    for route_data in result.routes:
        # Create route plan
        truck = Kamion.objects.get(id=route_data['truck_id'])
        
        route_plan = PlanRruge.objects.create(
            data_planifikimit=target_date,
            kamion=truck,
            shofer=truck.shofer_aktual,
            distanca_e_planifikuar_km=route_data['distance_km'],
            kohezgjatja_e_planifikuar_ore=route_data['duration_minutes'] / 60.0,
            kostoja_e_parashikuar=_calculate_route_cost(route_data),
            statusi='draft',
            pikuesi_optimizimit=result.optimization_score,
            krijuar_nga=user
        )
        
        # Set planned start time (assuming depot opens at 6 AM)
        depot_start_time = timezone.make_aware(
            datetime.combine(target_date, datetime.min.time().replace(hour=6))
        )
        route_plan.koha_nisjes_nga_depo = depot_start_time
        route_plan.save()
        
        # Create stops
        current_time = depot_start_time
        
        for stop_index, stop_data in enumerate(route_data['stops']):
            station = Stacion.objects.get(id=stop_data['station_id'])
            
            # Calculate arrival time (including travel time)
            if stop_index > 0:
                prev_stop_data = route_data['stops'][stop_index - 1]
                travel_time_minutes = _get_travel_time_between_stops(
                    prev_stop_data, stop_data
                )
                current_time += timedelta(minutes=travel_time_minutes)
            
            # Create stop
            ndalese = Ndalese.objects.create(
                plan_rruge=route_plan,
                stacion=station,
                sekuenca=stop_index + 1,
                koha_parashikuar_mberritjes=current_time,
                koha_e_parashikuar_sherbimit_min=45,  # Default service time
                koha_parashikuar_nisjes=current_time + timedelta(minutes=45)
            )
            
            # Create loading/unloading operations
            for delivery_data in stop_data['deliveries']:
                compartment = Particion.objects.get(id=delivery_data['compartment_id'])
                product = compartment.produkt_i_dedikuar or compartment.produkti_aktual
                
                # Find corresponding orders
                orders = Porosi.objects.filter(id__in=delivery_data['orders'])
                
                for order in orders:
                    NgarkeseShkarkese.objects.create(
                        ndalese=ndalese,
                        porosi=order,
                        particion=compartment,
                        produkt=product,
                        depozite=order.depozite,
                        sasia=-delivery_data['quantity'],  # Negative for delivery
                        sasia_e_dorezuar=0
                    )
            
            # Update current time for next stop
            current_time = ndalese.koha_parashikuar_nisjes
        
        route_plans.append(route_plan)
    
    return route_plans


def _update_order_statuses(result):
    """Update status of processed orders"""
    
    # Mark assigned orders as planned
    assigned_order_ids = []
    for route_data in result.routes:
        for stop_data in route_data['stops']:
            assigned_order_ids.extend(stop_data['orders'])
    
    Porosi.objects.filter(id__in=assigned_order_ids).update(statusi='e_planifikuar')
    
    logger.info(f"Updated status for {len(assigned_order_ids)} orders to 'planned'")


def _calculate_route_cost(route_data) -> float:
    """Calculate estimated cost for a route"""
    
    distance_km = route_data['distance_km']
    
    # Cost components
    fuel_cost_per_km = 0.35  # EUR per km (35L/100km * 1EUR/L)
    driver_cost_per_hour = 10.0  # EUR per hour
    duration_hours = route_data['duration_minutes'] / 60.0
    
    total_cost = (distance_km * fuel_cost_per_km) + (duration_hours * driver_cost_per_hour)
    
    return round(total_cost, 2)


def _get_travel_time_between_stops(prev_stop_data, current_stop_data) -> int:
    """Calculate travel time between two stops in minutes"""
    # This is a simplified calculation
    # In reality, you would use the distance matrix from the optimization engine
    return 30  # Default 30 minutes between stops


@shared_task
def update_distance_matrix():
    """Periodic task to update distance matrix cache"""
    try:
        # This would update cached distance/time data
        # Implementation depends on your caching strategy
        logger.info("Distance matrix update task started")
        
        # Example: Clear old cached routes, update with fresh OSRM data
        # This is where you would implement matrix caching logic
        
        return "Distance matrix updated successfully"
        
    except Exception as e:
        logger.error(f"Distance matrix update failed: {e}")
        return f"Update failed: {str(e)}"


@shared_task
def generate_emergency_optimization():
    """Generate emergency optimization for critical orders"""
    try:
        # Find critical orders (tanks below safety level)
        critical_orders = Porosi.objects.filter(
            statusi='e_hapur',
            prioriteti='kritike'
        )
        
        if not critical_orders.exists():
            return "No critical orders found"
        
        # Run emergency optimization
        tomorrow = timezone.now().date() + timedelta(days=1)
        
        result = optimize_routes_for_date.delay(
            tomorrow.strftime('%Y-%m-%d'),
            order_ids=list(critical_orders.values_list('id', flat=True))
        )
        
        return f"Emergency optimization started: task {result.id}"
        
    except Exception as e:
        logger.error(f"Emergency optimization failed: {e}")
        return f"Emergency optimization failed: {str(e)}"


@shared_task
def cleanup_old_routes():
    """Clean up old draft routes and completed routes older than 30 days"""
    try:
        cutoff_date = timezone.now().date() - timedelta(days=30)
        
        # Delete old draft routes
        old_drafts = PlanRruge.objects.filter(
            statusi='draft',
            data_planifikimit__lt=cutoff_date
        )
        draft_count = old_drafts.count()
        old_drafts.delete()
        
        # Archive old completed routes (mark as archived instead of deleting)
        old_completed = PlanRruge.objects.filter(
            statusi='perfunduar',
            data_planifikimit__lt=cutoff_date
        )
        completed_count = old_completed.count()
        
        logger.info(f"Cleaned up {draft_count} draft routes and {completed_count} completed routes")
        
        return f"Cleanup completed: {draft_count} drafts deleted, {completed_count} archived"
        
    except Exception as e:
        logger.error(f"Route cleanup failed: {e}")
        return f"Cleanup failed: {str(e)}"
