<!-- templates/mobile/stop_detail.html - Mobile Stop Detail View -->
{% extends 'mobile/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }} - OptiKarburant{% endblock %}

{% block extra_css %}
<style>
    .stop-header {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 1.5rem;
        margin: -1rem -1rem 1rem -1rem;
        border-radius: 0 0 1rem 1rem;
    }
    .info-card {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    .delivery-form {
        background: white;
        border-radius: 0.5rem;
        padding: 1rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    .quantity-input {
        font-size: 1.2rem;
        text-align: center;
        font-weight: bold;
    }
    .action-buttons {
        position: fixed;
        bottom: 1rem;
        left: 1rem;
        right: 1rem;
        z-index: 1000;
    }
    .floating-btn {
        border-radius: 50px;
        padding: 1rem 2rem;
        font-weight: bold;
        font-size: 1.1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .status-badge {
        font-size: 1rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
    }
    .photo-preview {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 0.5rem;
        margin: 0.25rem;
    }
    .camera-btn {
        width: 100px;
        height: 100px;
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        margin: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-3">
    <!-- Stop Header -->
    <div class="stop-header">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h4 class="mb-1">{{ stop.stacion.emri }}</h4>
                <p class="mb-2 opacity-75">
                    <i class="fas fa-map-marker-alt"></i> {{ stop.stacion.adresa }}
                </p>
                <div class="d-flex align-items-center">
                    <span class="status-badge bg-{{ stop.status_color }}">
                        {{ stop.get_statusi_display }}
                    </span>
                </div>
            </div>
            <div class="text-end">
                <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#navigationModal">
                    <i class="fas fa-directions"></i>
                </button>
                <button class="btn btn-light btn-sm" onclick="callStation()">
                    <i class="fas fa-phone"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Delivery Information -->
    <div class="info-card">
        <h6 class="mb-3">
            <i class="fas fa-info-circle"></i> Delivery Details
        </h6>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">Product:</small><br>
                <strong>{{ stop.produkt.emri }}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">Planned Quantity:</small><br>
                <strong>{{ stop.sasia_litra|floatformat:0 }} L</strong>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-6">
                <small class="text-muted">Planned Time:</small><br>
                <strong>{{ stop.koha_e_planifikuar|date:"H:i" }}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">Compartment:</small><br>
                <strong>{{ stop.particion.emri }}</strong>
            </div>
        </div>
        {% if stop.shenim %}
        <div class="mt-2">
            <small class="text-muted">Notes:</small><br>
            <p class="mb-0">{{ stop.shenim }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Station Information -->
    <div class="info-card">
        <h6 class="mb-3">
            <i class="fas fa-gas-pump"></i> Station Information
        </h6>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">Station Code:</small><br>
                <strong>{{ stop.stacion.kodi }}</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">Contact:</small><br>
                {% if stop.stacion.telefoni %}
                    <a href="tel:{{ stop.stacion.telefoni }}" class="text-decoration-none">
                        <strong>{{ stop.stacion.telefoni }}</strong>
                    </a>
                {% else %}
                    <span class="text-muted">Not available</span>
                {% endif %}
            </div>
        </div>
        {% if stop.stacion.menaxher %}
        <div class="mt-2">
            <small class="text-muted">Manager:</small><br>
            <strong>{{ stop.stacion.menaxher }}</strong>
        </div>
        {% endif %}
    </div>

    <!-- Delivery Form -->
    {% if stop.statusi != 'completed' %}
    <div class="delivery-form">
        <h6 class="mb-3">
            <i class="fas fa-clipboard-check"></i> Complete Delivery
        </h6>
        <form id="deliveryForm">
            {% csrf_token %}
            <div class="mb-3">
                <label class="form-label">Actual Quantity Delivered (L)</label>
                <input type="number" class="form-control quantity-input" 
                       name="actual_quantity" 
                       value="{{ stop.sasia_litra }}" 
                       min="0" 
                       max="{{ stop.sasia_litra }}" 
                       step="0.1" 
                       required>
                <small class="form-text text-muted">
                    Planned: {{ stop.sasia_litra|floatformat:0 }}L
                </small>
            </div>

            <div class="mb-3">
                <label class="form-label">Delivery Notes</label>
                <textarea class="form-control" name="delivery_notes" rows="3" 
                          placeholder="Any issues, observations, or special notes..."></textarea>
            </div>

            <div class="mb-3">
                <label class="form-label">Photos</label>
                <div class="d-flex flex-wrap">
                    <div class="camera-btn" onclick="takePhoto()">
                        <i class="fas fa-camera fa-2x text-muted"></i>
                    </div>
                    <div id="photoPreview" class="d-flex flex-wrap"></div>
                </div>
                <small class="form-text text-muted">
                    Take photos of delivery receipt, meter readings, etc.
                </small>
            </div>

            <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="confirmDelivery" required>
                    <label class="form-check-label" for="confirmDelivery">
                        I confirm that the delivery has been completed successfully
                    </label>
                </div>
            </div>
        </form>
    </div>
    {% else %}
    <!-- Completed Delivery Info -->
    <div class="info-card">
        <h6 class="mb-3 text-success">
            <i class="fas fa-check-circle"></i> Delivery Completed
        </h6>
        <div class="row">
            <div class="col-6">
                <small class="text-muted">Delivered Quantity:</small><br>
                <strong>{{ stop.sasia_e_dorezuar|floatformat:0 }} L</strong>
            </div>
            <div class="col-6">
                <small class="text-muted">Completion Time:</small><br>
                <strong>{{ stop.koha_e_perfundimit|date:"H:i" }}</strong>
            </div>
        </div>
        {% if stop.shenim_dorezimi %}
        <div class="mt-2">
            <small class="text-muted">Delivery Notes:</small><br>
            <p class="mb-0">{{ stop.shenim_dorezimi }}</p>
        </div>
        {% endif %}
    </div>
    {% endif %}

    <!-- Spacer for fixed buttons -->
    <div style="height: 120px;"></div>
</div>

<!-- Fixed Action Buttons -->
<div class="action-buttons">
    {% if stop.statusi != 'completed' %}
        <div class="d-grid gap-2">
            <button class="btn btn-success floating-btn" onclick="completeDelivery()">
                <i class="fas fa-check-circle"></i> Complete Delivery
            </button>
            <button class="btn btn-outline-warning floating-btn" data-bs-toggle="modal" data-bs-target="#issueModal">
                <i class="fas fa-exclamation-triangle"></i> Report Issue
            </button>
        </div>
    {% else %}
        <div class="d-grid gap-2">
            <a href="{% url 'mobile:route_detail' stop.plan_rruge.id %}" class="btn btn-primary floating-btn">
                <i class="fas fa-arrow-left"></i> Back to Route
            </a>
        </div>
    {% endif %}
</div>

<!-- Navigation Modal -->
<div class="modal fade" id="navigationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Navigate to Station</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-grid gap-2">
                    <a href="https://maps.google.com/?q={{ stop.stacion.latitude }},{{ stop.stacion.longitude }}" 
                       class="btn btn-outline-primary" target="_blank">
                        <i class="fab fa-google"></i> Open in Google Maps
                    </a>
                    <a href="https://waze.com/ul?ll={{ stop.stacion.latitude }},{{ stop.stacion.longitude }}" 
                       class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-route"></i> Open in Waze
                    </a>
                    <button class="btn btn-outline-secondary" onclick="shareLocation()">
                        <i class="fas fa-share"></i> Share Location
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Issue Report Modal -->
<div class="modal fade" id="issueModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Issue</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="issueForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label class="form-label">Issue Type</label>
                        <select class="form-select" name="issue_type" required>
                            <option value="">Select issue type</option>
                            <option value="access">Cannot access station</option>
                            <option value="equipment">Equipment problem</option>
                            <option value="quantity">Quantity mismatch</option>
                            <option value="quality">Product quality issue</option>
                            <option value="safety">Safety concern</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" name="issue_description" rows="4" 
                                  placeholder="Describe the issue in detail..." required></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="urgentIssue">
                            <label class="form-check-label" for="urgentIssue">
                                This is an urgent issue requiring immediate attention
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="submitIssue()">Report Issue</button>
            </div>
        </div>
    </div>
</div>

<!-- Hidden file input for camera -->
<input type="file" id="cameraInput" accept="image/*" capture="environment" style="display: none;" onchange="handlePhoto(this)">
{% endblock %}

{% block extra_js %}
<script>
let photos = [];

function completeDelivery() {
    const form = document.getElementById('deliveryForm');
    const formData = new FormData(form);
    
    // Add photos to form data
    photos.forEach((photo, index) => {
        formData.append(`photo_${index}`, photo);
    });
    
    // Validate form
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Completing...';
    btn.disabled = true;
    
    // Submit delivery completion
    fetch('{% url "mobile:complete_stop" stop.id %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect back to route
            window.location.href = '{% url "mobile:route_detail" stop.plan_rruge.id %}';
        } else {
            alert('Error completing delivery: ' + data.error);
            btn.innerHTML = originalText;
            btn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error completing delivery. Please try again.');
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function takePhoto() {
    document.getElementById('cameraInput').click();
}

function handlePhoto(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();
        
        reader.onload = function(e) {
            // Add photo to array
            photos.push(file);
            
            // Create preview
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'photo-preview';
            img.onclick = function() {
                // Remove photo
                const index = photos.indexOf(file);
                if (index > -1) {
                    photos.splice(index, 1);
                    img.remove();
                }
            };
            
            document.getElementById('photoPreview').appendChild(img);
        };
        
        reader.readAsDataURL(file);
    }
}

function callStation() {
    {% if stop.stacion.telefoni %}
        window.location.href = 'tel:{{ stop.stacion.telefoni }}';
    {% else %}
        alert('Station phone number not available');
    {% endif %}
}

function shareLocation() {
    if (navigator.share) {
        navigator.share({
            title: 'Delivery Location',
            text: 'Delivery at {{ stop.stacion.emri }}',
            url: window.location.href
        });
    } else {
        navigator.clipboard.writeText(window.location.href).then(function() {
            alert('Location link copied to clipboard!');
        });
    }
}

function submitIssue() {
    const form = document.getElementById('issueForm');
    const formData = new FormData(form);
    
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // Submit issue report
    fetch('{% url "mobile:report_issue" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Issue reported successfully. Support team has been notified.');
            bootstrap.Modal.getInstance(document.getElementById('issueModal')).hide();
        } else {
            alert('Error reporting issue: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error reporting issue. Please try again.');
    });
}

// Get current location for better accuracy
if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(function(position) {
        // Send location to server for tracking
        const data = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            stop_id: {{ stop.id }},
            timestamp: new Date().toISOString()
        };
        
        console.log('Current location:', data);
    });
}
</script>
{% endblock %}
